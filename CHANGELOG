0.1.5
fix: mybatis 未加密时，可能也会解密成功导致显示乱码

0.1.6
refactor: excel 删除 入口启动文件，将示例代码移动到 demo-service

0.1.8
feat: 删除 pitpat-logbac
feat: 优化 日志脱敏

0.1.9
feat: <PERSON>sonUtil 提供原生 ObjectMapper

0.1.10
feat: redisson codec.Jackson 支持 jdk 8 module and time

0.1.11
feat: log switch to log4j

0.2.1
feat: 新增 统一的消息调度中心 rabbitmq-starter

0.2.2
feat: 开发环境不上报钉钉错误日志
feat: 优化钉钉日志错误消息提示

0.2.4
fix: 日志脱敏仅保留邮箱

0.3.1
feat: 填充创建时间时，如果不为空则跳过
feat: ZonedDateTime 替换 Date
