<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.linzi.pitpat</groupId>
        <artifactId>pitpat-framework</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>pitpat-starter</artifactId>
    <name>pitpat-starter</name>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <modules>
        <module>db-spring-boot-starter</module>
        <module>web-spring-boot-starter</module>
        <module>redis-spring-boot-starter</module>
        <module>ampq-spring-boot-starter</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
        </dependency>
    </dependencies>
    <!--<build>-->
    <!--    <finalName>${project.artifactId}</finalName>-->
    <!--    <plugins>-->
    <!--        <plugin>-->
    <!--            <groupId>org.springframework.boot</groupId>-->
    <!--            <artifactId>spring-boot-maven-plugin</artifactId>-->
    <!--            <executions>-->
    <!--                <execution>-->
    <!--                    <goals>-->
    <!--                        <goal>repackage</goal>-->
    <!--                    </goals>-->
    <!--                </execution>-->
    <!--            </executions>-->
    <!--            <configuration>-->
    <!--                <excludes>-->
    <!--                    <exclude>-->
    <!--                        <groupId>org.projectlombok</groupId>-->
    <!--                        <artifactId>lombok</artifactId>-->
    <!--                    </exclude>-->
    <!--                </excludes>-->
    <!--            </configuration>-->
    <!--        </plugin>-->
    <!--    </plugins>-->
    <!--</build>-->

</project>
