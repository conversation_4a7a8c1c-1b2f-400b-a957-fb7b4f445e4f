/*

 * @description:

 *

 * projectName: pitpat-server

 * fileName: RedisUtil.java

 * date 2021-10-13

 * copyright(c) 2018-2020 杭州霖扬网络科技有限公司版权所有

 */

package com.linzi.pitpat.framework.redis.util;


import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * description: redis工具类
 *
 * <AUTHOR>
 * <p>
 * className RedisUtil
 * <p>
 * version V1.0
 * @date 2021-10-13
 **/
@Component
@Slf4j
public class RedisUtil {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 设置值， redisCache保存String会带""
     */
    public void set(String key, Object value, long expire, TimeUnit timeUnit) {
        if (!StringUtils.hasText(key) || value == null) {
            return;
        }
        try {
            redisTemplate.opsForValue().set(key, value, expire, timeUnit);
        } catch (Exception e) {
            //
        }
    }

    /**
     * 获取缓存对象
     *
     * @param key 缓存key
     * @return
     */
    @SuppressWarnings("all")
    public <T> T get(final String key) {
        if (!StringUtils.hasText(key)) {
            return null;
        }
        try {
            return (T) redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 删除缓存
     */
    public void delete(String key) {
        redisTemplate.delete(key);
    }

}
