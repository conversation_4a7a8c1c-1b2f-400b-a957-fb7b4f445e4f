package com.linzi.pitpat.framework.redis.util.lock;

import com.linzi.pitpat.exception.BizException;
import com.linzi.pitpat.exception.ExceptionFactory;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Slf4j
public class LockHolder {

    private static RedissonClient redissonClient;

    private static final ThreadLocal<String> contextHolder = new InheritableThreadLocal<>();

    //public static RedissonClient hold(RedissonClient redissonClient) {
    //    LockHolder.redissonClient = redissonClient;
    //    return redissonClient;
    //}

    /**
     * 最大超时时间30s, 未获得锁，立即返回
     *
     * @param lock
     * @param function
     * @return
     */
    public static void tryLock(RLock lock, Runnable function) {
        tryLock(lock, -1, -1, function);
    }

    /**
     * 给定持 leaseTime 有锁时间, 未获得锁，立即返回
     *
     * @param lock     锁对象
     * @param waitTime 持有锁时间
     * @param function task
     */
    public static void tryLock(RLock lock, long waitTime, Runnable function) {
        tryLock(lock, waitTime, -1, function);
    }

    /**
     * 给定持 leaseTime 有锁时间, 未获得锁最多等待 waitTime，  未的锁时抛出异常
     *
     * @param lock      锁对象
     * @param leaseTime 获取锁成功后持有锁的时间
     * @param waitTime  未获得时，最大等待锁时间
     * @param function  function
     */
    public static void tryLock(RLock lock, long waitTime, long leaseTime, Runnable function) {
        tryLock(lock, waitTime, leaseTime, () -> {
            function.run();
            return null;
        });
    }

    /**
     * 最大超时时间30s, 未获得锁，立即返回
     *
     * @param lock
     * @param function
     * @return
     */
    public static <V> V tryLock(RLock lock, Supplier<V> function) {
        return tryLock(lock, -1, -1, function);
    }


    /**
     * 给定持 leaseTime 有锁时间, 未获得锁，立即返回
     *
     * @param lock     锁对象
     * @param waitTime 持有锁时间
     * @param function task
     */
    public static <V> V tryLock(RLock lock, long waitTime, Supplier<V> function) {
        return tryLock(lock, waitTime, -1, function);
    }

    /**
     * 给定持 leaseTime 有锁时间, 未获得锁最多等待 waitTime, 未获得锁时抛出异常
     *
     * @param lock      锁对象
     * @param waitTime  未获得时，最大等待锁时间
     * @param leaseTime 获取锁成功后持有锁的时间
     * @param function  task
     */
    public static <V> V tryLock(RLock lock, long waitTime, long leaseTime, Supplier<V> function) {
        try {
            boolean locked = lock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS);
            log.info("LockKey={}, locked={}", lock.getName(), locked);
            if (locked) {
                return function.get();
            } else {
                log.error("获取锁失败,key= {}, tName={}", lock.getName(), Thread.currentThread().getName());
                throw ExceptionFactory.baseException("Repeated Request");
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            if (lock.isHeldByCurrentThread()) { //判断锁是否存在，和是否当前线程加的锁。
                lock.unlock();
                log.info("Release Lock,key={}", lock.getName());
            }
        }
    }

    /**
     * 原生 tryLock 代理，捕获异常
     *
     * @param lock      　lock
     * @param waitTime  等待时间
     * @param leaseTime 　锁持有时间
     * @return　是否获得锁
     */
    public static boolean tryLock(RLock lock, long waitTime, long leaseTime) {
        try {
            return lock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            throw new BizException("获取锁失败", e);
        }
    }

    /**
     * 获取锁， 默认续期，需要手动释放，业务上慎用
     * 原生 tryLock 代理，捕获异常
     *
     * @param lock     lock
     * @param waitTime 等待时间
     * @return　是否获得锁
     */
    public static boolean tryLock(RLock lock, long waitTime) {
        try {
            return lock.tryLock(waitTime, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            throw new BizException("获取锁失败", e);
        }
    }

}
