package com.linzi.pitpat.framework.redis.util.limiter;

/**
 * 基于redission的限流器
 */

import com.linzi.pitpat.framework.redis.util.annotation.RateLimiter;
import com.linzi.pitpat.framework.redis.util.annotation.RateLimiters;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class LimiterAspect {

    private final RateLimiterService rateLimiterService;

    @Around("@annotation(rateLimiter)")
    public Object aroundRateLimiter(ProceedingJoinPoint joinPoint, RateLimiter rateLimiter) throws Throwable {
        String keyPrefix = prepareKeyPrefix(joinPoint, rateLimiter);
        return rateLimiterService.rateLimiter(keyPrefix,rateLimiter.key(),
                rateLimiter.rate(),
                rateLimiter.rateInterval(),
                rateLimiter.rateIntervalUnit(),
                rateLimiter.waitTime(),rateLimiter.waitTimeUnit(),
                joinPoint::proceed);
    }

    private static String prepareKeyPrefix(ProceedingJoinPoint joinPoint, RateLimiter rateLimiter) {
        String keyPrefix = "";
        if (RateLimiter.DEFAULT_KEY_PREFIX.equals(rateLimiter.keyPrefix())) {
            keyPrefix = joinPoint.getSignature().getName();
        }else{
            keyPrefix = rateLimiter.keyPrefix();
        }
        if (StringUtils.hasText(rateLimiter.key())) {
            keyPrefix+=":"+ rateLimiter.key();
        }
        return keyPrefix;
    }

    @Around("@annotation(rateLimiters)")
    public Object aroundRateLimiter(ProceedingJoinPoint joinPoint, RateLimiters rateLimiters) throws Throwable {
        RateLimiter[] rateLimitersArray = rateLimiters.value();
        for (RateLimiter rateLimiter : rateLimitersArray) {
            String keyPrefix = prepareKeyPrefix(joinPoint, rateLimiter);
            if (!rateLimiterService.tryAcquire(keyPrefix+rateLimiter.key(),1,
                    rateLimiter.rate(),rateLimiter.rateInterval(),rateLimiter.rateIntervalUnit(),rateLimiter.waitTime(),rateLimiter.waitTimeUnit())) {
                log.error("Rate limit exceeded,Batch:{}", keyPrefix);
                throw new RuntimeException("Rate limit exceeded");
            }
        }
        return joinPoint.proceed();
    }

}
