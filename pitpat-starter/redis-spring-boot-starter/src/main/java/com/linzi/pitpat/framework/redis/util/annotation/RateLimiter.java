package com.linzi.pitpat.framework.redis.util.annotation;

import org.redisson.api.RateIntervalUnit;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

/**
 * 限流切面表示。
 * 只支持固定的key。
 *
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimiter {
    public static final String DEFAULT_KEY_PREFIX = "METHOD";
    /**
     *  // 令牌生成速率
     * @return
     */
    int rate();

    /**
     * // 时间间隔
     * @return
     */
    int rateInterval();

    /**
     *  // 时间间隔单位
     * @return
     */
    RateIntervalUnit rateIntervalUnit() default RateIntervalUnit.SECONDS;

    /**
     * rate limiter  key
     */
    String key() default "";

    /**
     * 默认使用当前的方法名称。
     * @return
     */
    String keyPrefix() default DEFAULT_KEY_PREFIX;


    /**
     * 等待时间
     * 默认5s
     * @return
     */
    int waitTime() default 0;

    /**
     * 等待时间单位
     * @return
     */
    TimeUnit waitTimeUnit() default TimeUnit.SECONDS;

}
