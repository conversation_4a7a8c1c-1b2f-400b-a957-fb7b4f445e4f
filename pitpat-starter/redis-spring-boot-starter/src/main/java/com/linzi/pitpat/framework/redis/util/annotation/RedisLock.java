package com.linzi.pitpat.framework.redis.util.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface RedisLock {

    String[] value() default "_JedisLock_Lock";


    boolean isTry() default true;               //是否是重试锁

    long timeout() default 30l; //默认5分钟

}
