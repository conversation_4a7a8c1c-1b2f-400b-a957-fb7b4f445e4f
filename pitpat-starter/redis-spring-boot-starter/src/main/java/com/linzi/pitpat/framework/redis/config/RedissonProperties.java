package com.linzi.pitpat.framework.redis.config;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Redisson 配置属性
 *
 * <AUTHOR> Li
 */
@Data
@NoArgsConstructor
@Component
@ConfigurationProperties(prefix = "spring.redis.redisson")
public class RedissonProperties {

    /**
     * 缓存组
     */
    private List<CacheGroup> cacheGroup;


    @Data
    @NoArgsConstructor
    public static class CacheGroup {

        /**
         * 组id
         */
        private String groupId;

        /**
         * 组过期时间
         */
        private long ttl;

        /**
         * 组最大空闲时间
         */
        private long maxIdleTime;

        /**
         * 组最大长度
         */
        private int maxSize;

        /**
         * 是否默认组
         */
        private boolean defaultGroup;

    }

}
