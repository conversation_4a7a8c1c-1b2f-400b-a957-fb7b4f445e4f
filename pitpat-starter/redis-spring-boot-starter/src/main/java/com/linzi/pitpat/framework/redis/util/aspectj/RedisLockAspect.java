package com.linzi.pitpat.framework.redis.util.aspectj;

import com.linzi.pitpat.framework.redis.util.annotation.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 通知事件aop
 * @date 2021-01-12
 */
@Aspect
@Component
@Slf4j
public class RedisLockAspect {

    private final static DefaultParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();

    @Pointcut("@annotation(com.linzi.pitpat.framework.redis.util.annotation.RedisLock)")
    public void notice() {

    }
    @Autowired
    private RedissonClient redissonClient;



    @Around("notice()")
    public Object doAround(ProceedingJoinPoint point) throws Throwable {
        //获取切入方法的数据
        MethodSignature signature = (MethodSignature) point.getSignature();
        //获取切入方法
        Method method = signature.getMethod();
        //获得注解
        RedisLock noticeEvent = method.getAnnotation(RedisLock.class);
        if (noticeEvent == null) {
            return point.proceed();
        }
        String methodName = method.getName();

        String mapperId = method.getDeclaringClass().getName() +"." + methodName;
        StringBuilder sbMapperId = new StringBuilder(mapperId);
        StringBuilder sb = new StringBuilder(mapperId);
        Class returnClass = method.getReturnType();

        EvaluationContext context = DataCacheAspect.getArgs(point, method,sb);
        for (String value : noticeEvent.value()) {
            String spelValue = DataCacheAspect.getSpelValue(context, "#" + value);
            sb.append("_").append(spelValue);
            sbMapperId.append("_").append(spelValue);
        }
        String key = sb.toString();
        String printKey = sb.toString();
        boolean flag = false;
        RLock lock = redissonClient.getLock(key);
        try {
            flag = false;
            if (noticeEvent.isTry()) {
                flag = lock.tryLock( noticeEvent.timeout(), noticeEvent.timeout(),TimeUnit.SECONDS);
            } else {
                flag = lock.tryLock(0, noticeEvent.timeout(), TimeUnit.SECONDS);
            }
            if (flag) {
                log.info("RedisLockAspect获取锁成功 ：" + printKey);
                return point.proceed();
            } else {
                log.info("RedisLockAspect没有获取到锁 ：" + printKey);
            }
        } catch (Exception e) {
            log.error("RedisLockAspect锁异常 key  = " + printKey, e);
            throw e;
        } finally {
            if (flag) {
                log.info("RedisLockAspect删除锁成功 key = " + printKey);
                if(lock.isHeldByCurrentThread()){
                    lock.unlock();
                }
            }
        }
        try {
            returnClass.newInstance();
        } catch (Exception e) {
           log.error("RedisLockAspect没有获得锁 创建实例对象异常",e);
        }
        return null;
    }


}
