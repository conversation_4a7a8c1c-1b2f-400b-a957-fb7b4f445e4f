/*

 * @description:

 *

 * projectName: pitpat-server

 * fileName: RedissonAutoConfigure.java

 * date 2021-09-26

 * copyright(c) 2018-2020 杭州霖扬网络科技有限公司版权所有

 */

package com.linzi.pitpat.framework.redis.autoconfigure;


import com.linzi.pitpat.framework.redis.config.RedissonProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.spring.cache.CacheConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * description:
 *
 * <AUTHOR>
 * <p>
 * className RedissonAutoConfigure
 * <p>
 * version V1.0
 * @date 2021-09-26
 **/
@Slf4j
@Configuration
@EnableCaching
@EnableConfigurationProperties({RedissonProperties.class})
@RequiredArgsConstructor
public class RedissonAutoConfigure {

    private final  RedissonProperties redissonProperties;

    /**
     * 整合spring-cache
     */
    @Bean
    public CacheManager cacheManager(RedissonClient redissonClient) {
        Map<String, CacheConfig> config = new HashMap<>();
        RedissonProperties.CacheGroup defaultGroup = null;

        for (RedissonProperties.CacheGroup group : redissonProperties.getCacheGroup()) {
            if (Objects.equals(group.isDefaultGroup(), true)) {
                defaultGroup = group;
                continue;
            }
            CacheConfig cacheConfig = new CacheConfig(group.getTtl(), group.getMaxIdleTime());
            cacheConfig.setMaxSize(group.getMaxSize());
            config.put(group.getGroupId(), cacheConfig);
        }
        return new RedissonSpringCacheManager(redissonClient, config, redissonClient.getConfig().getCodec(), defaultGroup);
    }

    /**
     * 自定义的 RedissonSpringCacheManager的默认配置逻辑，提供默认5分钟过期逻辑
     */
    static class RedissonSpringCacheManager extends org.redisson.spring.cache.RedissonSpringCacheManager {
        RedissonProperties.CacheGroup cacheGroup;

        public RedissonSpringCacheManager(RedissonClient redisson, Map<String, CacheConfig> config, Codec codec, RedissonProperties.CacheGroup cacheGroup) {
            super(redisson, config, codec);
            this.cacheGroup = cacheGroup;
        }

        @Override
        protected CacheConfig createDefaultConfig() {
            log.info("默认的 RedissonSpringCacheManager cacheGroup:{}", cacheGroup);
            CacheConfig cacheConfig = new CacheConfig(cacheGroup.getTtl(), cacheGroup.getMaxIdleTime());
            cacheConfig.setMaxSize(cacheGroup.getMaxSize());
            return cacheConfig;
        }
    }

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(new StringRedisSerializer());
        redisTemplate.setConnectionFactory(factory);
        return redisTemplate;
    }
}
