package com.linzi.pitpat.framework.redis.util.aspectj;

import com.google.common.hash.Hashing;
import com.linzi.pitpat.core.util.EnvUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import com.linzi.pitpat.framework.redis.util.annotation.DataCache;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 后期通过spring-data-cache 或者 jetcache 替代
 * @date 2021-01-12
 *
 */
@Aspect
@Component
@Slf4j
@Deprecated
public class DataCacheAspect {

    private final static DefaultParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();

    private final static Map<String, String[]> methodParams = new HashMap<>();


    private final static Map<String, Boolean> hasRestart = new HashMap<>();

    @Pointcut("@annotation(com.linzi.pitpat.framework.redis.util.annotation.DataCache)")
    public void notice() {
    }

    @Resource
    private RedisUtil redisUtil;

    @Value("${spring.profiles.active}")
    private String profile;

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static final String redis_cache_key_switch = "redis_cache_key_switch";


    @Around("notice()")
    public Object doAround(ProceedingJoinPoint point) throws Throwable {



        //获取切入方法的数据
        MethodSignature signature = (MethodSignature) point.getSignature();
        //获取切入方法
        Method method = signature.getMethod();
        if(EnvUtils.isTest(profile)){               // 测试环境不做缓存
            return point.proceed();
        }

        //获得注解
        DataCache noticeEvent = method.getAnnotation(DataCache.class);
        if (noticeEvent == null) {
            return point.proceed();
        }

        String methodName = method.getName();
        String mapperId = method.getDeclaringClass().getName() +"." + methodName;
        StringBuilder sbMapperId = new StringBuilder(mapperId);
        StringBuilder sb = new StringBuilder("Cache"+mapperId);

        EvaluationContext context = getArgs(point, method,sb);
        for (String value : noticeEvent.value()) {
            String spelValue = getSpelValue(context, "#" + value);
            sb.append("_").append(spelValue);
            sbMapperId.append("_").append(spelValue);
        }

        String key = sb.toString();
        String printKey = sbMapperId.toString();
        // 项目是否重启过
        Boolean hashSave = hasRestart.get(key);
        if (hashSave != null) {                         //如果hashSave ！=null,则表示项目重启后，已经加载key 到redis中去了。
            Object object = redisUtil.get(redis_cache_key_switch);                       //如果有值，说明测试小伙伴设置了，不走缓存一天
            if(object == null){                                     //如果redis没有存放相关的锁，则需要从缓存中获取值
                Object result = redisUtil.get(key);
                if (result != null) {
                    log.info("使用DataCache中的值 key = " + key);
                    Class clazz = method.getReturnType();
                    if(isList(clazz)){
                        // 对list类型处理
                        return JsonUtil.readList(result.toString(), getActualType(method, 0));
                    } else if (isMap(clazz)) {                              // 对map 类型处理
                        String stringType = getActualStringType(method, 1);
                        Map<Object, Object> map = JsonUtil.readValue(result.toString(), Map.class);
                        Map<Object, Object> data = new HashMap<>();
                        // 如果map的第二个参数是List 类型
                        if (stringType.startsWith("java.util.List")) {
                            for (Map.Entry<Object, Object> m : map.entrySet()) {
                                data.put(m.getKey(), JsonUtil.readList(JsonUtil.writeString(m.getValue()), getActualType(method, 1, 0)));
                            }
                            return data;
                        }
                        Class actualType = getActualType(method,1);
                        // 如果map 第二个参数是非基本数据类型
                        if (!isBasicDataTypes(actualType)){
                            for (Map.Entry<Object, Object> m : map.entrySet()) {
                                data.put(m.getKey(), JsonUtil.readValue(JsonUtil.writeString(m.getValue()), actualType));
                            }
                            return data;
                        }
                        return map;
                    }
                    return JsonUtil.readValue(result.toString(), method.getReturnType());
                }
            }
        } else {
            hasRestart.put(key, Boolean.TRUE);
        }
        Object object = point.proceed();
        String cacheValue = JsonUtil.writeString(object);
        log.info("DataCache实际缓存到redis的值为 key = "+ key + "，cache = " + cacheValue);
        redisUtil.set(key,cacheValue, noticeEvent.timeout(), TimeUnit.SECONDS);
        return object;
    }




    public static boolean isList(Class clz) {
        if (clz.getName().startsWith("java.util.List")) {
            return true;
        }
        return false;
    }


    public static boolean isMap(Class clz) {
        if (clz.getName().startsWith("java.util.Map")) {
            return true;
        }
        return false;
    }

    // 如  ,获取返回参数类型为    Map<Long,List<User>>  类型
    //  获取User类型
    public static Class getActualType(Method method, int index, int index2) {
        // 获取返回值类型，getGenericReturnType()会返回值带有泛型的返回值类型
        Type genericReturnType = method.getGenericReturnType();
        // 但我们实际上需要获取返回值类型中的泛型信息，所以要进一步判断，即判断获取的返回值类型是否是参数化类型ParameterizedType
        try {
            if (genericReturnType instanceof ParameterizedType) {
                // 如果要使用ParameterizedType中的方法，必须先强制向下转型
                ParameterizedType type = (ParameterizedType) genericReturnType;
                // 获取返回值类型中的泛型类型，因为可能有多个泛型类型，所以返回一个数组
                Type[] actualTypeArguments = type.getActualTypeArguments();
                Type types = actualTypeArguments[index];
                ParameterizedType type2 = (ParameterizedType) types;
                Type[] type2s = type2.getActualTypeArguments();
                Type real = type2s[index2];
                return Class.forName(real.getTypeName());
            }
        } catch (ClassNotFoundException e) {
            log.error("异常",e);
        }
        return null;
    }

    public static Class getActualType(Method method, int index) {
        try {
            return Class.forName(getActualStringType(method, index));
        } catch (ClassNotFoundException e) {
            log.info("异常", e);
        }
        return null;
    }


    public static String getActualStringType(Method method, int index) {
        // 获取返回值类型，getGenericReturnType()会返回值带有泛型的返回值类型
        Type genericReturnType = method.getGenericReturnType();
        // 但我们实际上需要获取返回值类型中的泛型信息，所以要进一步判断，即判断获取的返回值类型是否是参数化类型ParameterizedType
        try {
            if (genericReturnType instanceof ParameterizedType) {
                // 如果要使用ParameterizedType中的方法，必须先强制向下转型
                ParameterizedType type = (ParameterizedType) genericReturnType;
                // 获取返回值类型中的泛型类型，因为可能有多个泛型类型，所以返回一个数组
                Type[] actualTypeArguments = type.getActualTypeArguments();
                Type types = actualTypeArguments[index];
                return types.getTypeName();
            }
        } catch (Exception e) {
            log.error("异常",e);
        }
        return null;
    }





    public static EvaluationContext getArgs(JoinPoint joinPoint, Method method, StringBuilder sb) {
        Object[] args = joinPoint.getArgs();
        Class<?>[] clazzs = method.getParameterTypes();
        for (Class cla : clazzs) {
            sb.append(cla.getName()).append("_");
        }
        String[] params = methodParams.get(sb.toString());
        if (params == null) {
            params = discoverer.getParameterNames(method);
            if(params ==null){
                params = new String[0];
            }
            methodParams.put(sb.toString(),params);
        }
        EvaluationContext context = new StandardEvaluationContext();
        for (int len = 0; len < params.length; len++) {
            context.setVariable(params[len], args[len]);
        }
        return context;
    }


    public static String getSpelValue(EvaluationContext context, String spel) {
        String value = "";
        if (!StringUtils.hasText(spel)) {
            return value;
        }
        ExpressionParser parser = new SpelExpressionParser();
        Expression expression = parser.parseExpression(spel);
        Object obj = expression.getValue(context);
        if(obj == null){
            return null;
        }else if (obj instanceof String) {
            value = obj.toString();
            if(!StringUtils.hasText(value)){
                return null;
            }
        } else if(obj instanceof Date){
            return formatDateStr((Date) obj, YYYYMMDDHHMMSS);
        } else {
            if(!isBasicDataTypes(obj.getClass())){                  //如果非基本数据类型
                value = Hashing.md5().hashString(JsonUtil.writeString(obj), StandardCharsets.UTF_8).toString();
            }else{
                value = JsonUtil.writeString(obj);
            }
        }
        return value;
    }

    public static String formatDateStr(Date gmtCreate, String yyyyMmDdHhMmSs) {
        try {
            if (gmtCreate == null || !StringUtils.hasText(yyyyMmDdHhMmSs)) {
                return null;
            }
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(yyyyMmDdHhMmSs);
            return simpleDateFormat.format(gmtCreate);
        } catch (Exception e) {
            log.error("异常", e);
        }
        return null;
    }

    private static final List<Class<?>> primitiveTypes = new ArrayList<>(32);

    static {
        primitiveTypes.add(Boolean.class);
        primitiveTypes.add(Byte.class);
        primitiveTypes.add(Character.class);
        primitiveTypes.add(Double.class);
        primitiveTypes.add(Float.class);
        primitiveTypes.add(Integer.class);
        primitiveTypes.add(Long.class);
        primitiveTypes.add(Short.class);
        primitiveTypes.add(BigDecimal.class);

        primitiveTypes.add(boolean.class);
        primitiveTypes.add(byte.class);
        primitiveTypes.add(char.class);
        primitiveTypes.add(double.class);
        primitiveTypes.add(float.class);
        primitiveTypes.add(int.class);
        primitiveTypes.add(long.class);
        primitiveTypes.add(short.class);
        primitiveTypes.add(String.class);
        primitiveTypes.add(Date.class);
        primitiveTypes.add(java.sql.Date.class);

        primitiveTypes.addAll(Arrays.asList(new Class<?>[]{
                boolean[].class, byte[].class, char[].class, double[].class,
                float[].class, int[].class, long[].class, short[].class}));

        primitiveTypes.addAll(Arrays.asList(new Class<?>[]{
                Boolean[].class, Byte[].class, Character[].class, Double[].class,
                Float[].class, Integer[].class, Long[].class, Short[].class, String[].class}));
    }

    private static boolean isBasicDataTypes(Class clazz) {
        return primitiveTypes.contains(clazz) ? true : false;
    }


}
