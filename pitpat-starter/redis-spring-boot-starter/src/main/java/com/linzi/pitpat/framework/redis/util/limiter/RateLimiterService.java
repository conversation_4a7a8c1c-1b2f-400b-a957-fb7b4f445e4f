package com.linzi.pitpat.framework.redis.util.limiter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@RequiredArgsConstructor
@Service
@Slf4j
public class RateLimiterService {
    private final RedissonClient redissonClient;
    /**
     *
     * @param keyPrefix  k
     * @param key
     * @param permits 本次请求凭证数量
     * @param rate  token数量
     * @param rateInterval  周期数量
     * @param rateIntervalUnit  周期单位
     * @param waitTime 等待时间
     * @param waitTimeUnit 等待时间单位
     * @param callback 获取成功后执行
     * @return
     * @param <T>
     * @throws Throwable
     */

    public <T> T rateLimiter(String keyPrefix,String key, int permits,int rate, int rateInterval, RateIntervalUnit rateIntervalUnit, int waitTime, TimeUnit waitTimeUnit, RateLimiterCallback<T> callback) throws Throwable {
        if (tryAcquire(keyPrefix+":"+key, permits,rate, rateInterval, rateIntervalUnit, waitTime, waitTimeUnit)) {
            return callback.doService();
        } else {
            log.warn("Rate limit exceeded,:{}", keyPrefix);
            throw new RateLimitException("Rate limit exceeded");
        }
    }

    public <T> T rateLimiter(String keyPrefix,String key, int rate, int rateInterval, RateIntervalUnit rateIntervalUnit, int waitTime, TimeUnit waitTimeUnit, RateLimiterCallback<T> callback) throws Throwable {
        return rateLimiter(keyPrefix,key,1,rate,rateInterval,rateIntervalUnit,waitTime,waitTimeUnit,callback);
    }

    /**
     * 不等待，获取失败，就失败了
     *
     * @param keyPrefix
     * @param rate
     * @param rateInterval
     * @param callback
     * @param <T>
     * @return
     */
    public <T> T rateLimiter(String keyPrefix, String key,int rate, int rateInterval, RateLimiterCallback<T> callback) throws Throwable {
        return rateLimiter(keyPrefix,key,rate,rateInterval,RateIntervalUnit.SECONDS,0,TimeUnit.SECONDS,callback);
    }

    public boolean tryAcquire(String key, int permits,int rate, int rateInterval, RateIntervalUnit rateIntervalUnit, int waitTime, TimeUnit waitTimeUnit) {
        RRateLimiter rRateLimiter = redissonClient.getRateLimiter(key);
        if (!rRateLimiter.isExists()) {
            rRateLimiter.trySetRate(RateType.OVERALL, rate, rateInterval, rateIntervalUnit);
            rRateLimiter.expire(5,TimeUnit.DAYS);
        }
        log.info("RateLimiter:key:{},permits:{},rate:{},rateInterval:{},rateIntervalUnit:{},waitTime:{},waitTimeUnit:{}", key,permits, rate, rateInterval, rateIntervalUnit, waitTime, waitTimeUnit);
        if(waitTime==0){
            return rRateLimiter.tryAcquire(permits);
        }else{
            return rRateLimiter.tryAcquire(permits,waitTime, waitTimeUnit);
        }
    }
}
