# 统一消息组件

基于Spring Boot Starter封装的RabbitMQ消息组件，提供统一的消息发送和接收接口，简化消息中间件的使用。

## 功能特性

1. 提供多种消息发送方式：
   - 实时消息：立即发送并处理的消息
   - 延迟消息：在指定时间后处理的消息
   - 短期延迟消息：基于Redisson实现的延迟消息，支持按队列名区分不同的延迟队列
   - 本地同步消息：在当前应用内同步处理的消息
   - 本地异步消息：在当前应用内异步处理的消息（基于Spring Event机制）

2. 统一的消息格式和处理流程：
   - 统一的消息包装格式，支持任意类型的消息内容
   - 统一的日志记录，方便问题排查
   - 统一的异常处理，支持钉钉通知快速暴露问题

3. 简化的API接口：
   - 降低调用复杂度，无需关注底层实现细节
   - 支持直接使用DTO对象作为消息内容，无需手动序列化

## 快速开始

### 1. 引入依赖

```xml
<dependency>
    <groupId>com.example</groupId>
    <artifactId>message-starter</artifactId>
    <version>1.0.0</version>
</dependency>
```

### 2. 配置

在`application.yml`中添加配置：

```yaml
spring:
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
    # 自定义队列配置
    custom:
      queues:
        - name: sample.queue
          durable: true
      exchanges:
        - name: sample.exchange
          type: topic
          durable: true
      bindings:
        - queue: sample.queue
          exchange: sample.exchange
          routing-key: sample.#

# 消息组件配置
message:
  application-name: your-app-name
  enable-logging: true
  enable-ding-talk-notification: false
  ding-talk:
    webhook-url: https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN
    secret: YOUR_SECRET
```

### 3. 发送消息

```java
@RestController
public class DemoController {

    @Autowired
    private MessageService messageService;
    
    @PostMapping("/send")
    public void sendMessage() {
        // 发送实时消息
        UserDTO user = new UserDTO(1L, "张三");
        messageService.sendMessage("sample.exchange", "sample.user", user);
        
        // 发送延迟消息（5秒后处理）
        OrderDTO order = new OrderDTO(1001L, 100.00);
        messageService.sendDelayMessage("sample.exchange", "sample.order", order, 5000);
        
        // 发送短期延迟消息（基于Redisson实现，3秒后处理）
        NotificationDTO notification = new NotificationDTO("新消息提醒", "您有一条新消息");
        messageService.sendShortDelayMessage("notification.delay.queue", "sample.exchange", "sample.notification", notification, 3000);
        
        // 发送本地同步消息
        messageService.sendLocalSyncMessage("local.event.type", "本地同步消息");
        
        // 发送本地异步消息
        messageService.sendLocalAsyncMessage("local.event.type", "本地异步消息");
    }
}
```

### 4. 接收消息

#### 接收RabbitMQ消息

创建一个继承自`MessageListener`的监听器：

```java
@Component
public class MyMessageListener extends MessageListener {

    public MyMessageListener(ObjectMapper objectMapper, MessageProperties messageProperties) {
        super(objectMapper, messageProperties);
    }
    

    @Override
    protected void handleMessage(MessageWrapper<?> wrapper) throws Exception {
        // 根据消息类型处理不同的消息
        switch (wrapper.getMessageType()) {
            case "UserDTO":
                UserDTO user = objectMapper.convertValue(wrapper.getPayload(), UserDTO.class);
                // 处理用户消息
                break;
            case "OrderDTO":
                OrderDTO order = objectMapper.convertValue(wrapper.getPayload(), OrderDTO.class);
                // 处理订单消息
                break;
            default:
                log.info("未知消息类型: {}", wrapper.getMessageType());
        }
    }
}
```

#### 处理本地消息

实现`MessageHandler`接口：

```java
@Component
public class MyLocalMessageHandler implements MessageHandler {

    @Override
    public String getSupportedEventType() {
        return "local.event.type";
    }

    @Override
    public void handleMessage(MessageEvent<?> event) {
        // 处理本地消息
        System.out.println("收到本地消息: " + event.getPayload());
    }
}
```

## 高级特性

### 1. 异常处理与钉钉通知

当消息处理出现异常时，组件会自动记录日志，并可选择发送钉钉通知：

```yaml
message:
  enable-ding-talk-notification: true
  ding-talk:
    webhook-url: https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN
    secret: YOUR_SECRET
    title-prefix: "[系统通知]"
    include-stack-trace: true
```

### 2. 本地异步消息线程池配置

可以配置处理本地异步消息的线程池大小：

```yaml
message:
  async-event-thread-pool-size: 10
```

### 3. 自定义消息处理逻辑

通过继承`MessageListener`或实现`MessageHandler`接口，可以自定义消息处理逻辑。 
