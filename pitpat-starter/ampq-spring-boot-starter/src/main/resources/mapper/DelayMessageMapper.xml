<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linzi.pitpat.framework.rabbitmq.mapper.DelayMessageMapper">

    <!-- 批量插入延迟消息 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO zns_delay_queue_message (
            message_id, message_type, exchange, routing_key,
            batch_id, payload, scheduled_time, status, created_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.messageId}, #{item.messageType},  #{item.exchange}, #{item.routingKey},
                #{item.batchId}, #{item.payload}, #{item.scheduledTime}, #{item.status}, #{item.createdTime}
            )
        </foreach>
    </insert>

    <!-- 删除指定日期前的已发送消息 -->
    <delete id="deleteSentMessagesBeforeDate">
        DELETE FROM zns_delay_queue_message
        WHERE status = #{status}
        AND sent_time &lt; #{beforeDate}
    </delete>

    <select id="selectLongTermMessages" resultType="com.linzi.pitpat.framework.rabbitmq.model.DelayMessage">
        SELECT * FROM zns_delay_queue_message WHERE
        <if test="startTime != null">
            scheduled_time >= #{startTime} AND
        </if>
        scheduled_time <![CDATA[ <=  ]]>  #{endTime} AND status = 0 ORDER BY scheduled_time ASC LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 单条更新消息状态和发送时间（按ID和分片键更新，但不更新分片键本身） -->
    <update id="updateStatusById">
        UPDATE zns_delay_queue_message
        SET status = #{status}, sent_time = #{sentTime}
        WHERE id = #{id} AND scheduled_time = #{scheduledTime}
    </update>

    <update id="batchUpdateMessageStatusById">
       UPDATE zns_delay_queue_message_${tablePostfix} SET status = #{status}, sent_time = #{sentTime}
       WHERE id in
        <foreach item="item" index="index" collection="messageIdList"
                 open="(" separator="," close=")" nullable="true">
            #{item}
        </foreach>
    </update>

</mapper>
