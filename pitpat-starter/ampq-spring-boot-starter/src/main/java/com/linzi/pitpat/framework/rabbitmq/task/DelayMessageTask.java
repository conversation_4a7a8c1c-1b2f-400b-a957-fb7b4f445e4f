package com.linzi.pitpat.framework.rabbitmq.task;


import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.framework.rabbitmq.QueueNameGenerator;
import com.linzi.pitpat.framework.rabbitmq.constant.QueueConstants;
import com.linzi.pitpat.framework.rabbitmq.mapper.DelayMessageMapper;
import com.linzi.pitpat.framework.rabbitmq.model.DelayMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class DelayMessageTask {

    private final JdbcTemplate jdbcTemplate;
    private final RedissonClient redissonClient;
    private final DelayMessageMapper messageMapper;
    private final QueueNameGenerator nameGenerator;
    private final RabbitTemplate rabbitTemplate;
    public static final DateTimeFormatter pattern = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 创建分表
     */
    //@Scheduled(cron = "0 1 * * * *")
    public void doCreateTable() {
        ZonedDateTime nextMonth = ZonedDateTime.now().plusMonths(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        ZonedDateTime start = nextMonth.with(TemporalAdjusters.firstDayOfMonth());
        ZonedDateTime end = nextMonth.with(TemporalAdjusters.lastDayOfMonth());

        while (start.isBefore(end)) {
            String table = "zns_delay_queue_message_" + start.format(formatter);
            start = start.plusDays(1);
            String sql = "CREATE TABLE IF NOT EXISTS " + table + " LIKE zns_delay_queue_message";
            log.info("prepare sql={}", sql);
            jdbcTemplate.execute(sql);
            log.info("create table success, table={}", table);
        }
    }

    /**
     * 过期一个小时内消息，10分钟调度一次
     */
    //@Scheduled(cron = "9 */10 * * * *")
    public void doScanPastMessages() {
        // 创建分布式锁
        RLock lock = redissonClient.getLock("DelayMessageTask:scanPastMessages");
        try {
            // 尝试获取锁，等待5秒，持有锁60秒
            if (lock.tryLock(5, 600, TimeUnit.SECONDS)) {
                try {
                    // 执行消息处理逻辑
                    processPastMessages();
                } finally {
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    } else {
                        log.warn("锁已不再被当前线程持有，跳过解锁操作");
                    }
                }
            } else {
                log.debug("未获取到分布式锁，跳过本次处理");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取分布式锁被中断", e);
        } catch (Exception e) {
            log.error("处理延迟消息任务失败", e);
        }
    }

    /**
     * 处理正常消息，1分钟调度一次
     */
    //@Scheduled(cron = "12 * * * * *")
    public void doScanMessages() {
        // 创建分布式锁
        RLock lock = redissonClient.getLock("DelayMessageTask:scanMessages");
        try {
            // 尝试获取锁，等待5秒，持有锁60秒
            if (lock.tryLock(5, 60, TimeUnit.SECONDS)) {
                try {
                    // 执行消息处理逻辑
                    processMessages();
                } finally {
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    } else {
                        log.warn("锁已不再被当前线程持有，跳过解锁操作");
                    }
                }
            } else {
                log.debug("未获取到分布式锁，跳过本次处理");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取分布式锁被中断", e);
        } catch (Exception e) {
            log.error("处理延迟消息任务失败", e);
        }
    }

    /**
     * 处理即将到期的消息，5分钟处理一次
     */
    //@Scheduled(cron = "5 */5 * * * *")
    public void doScanFutureMessages() {
        // 创建分布式锁
        RLock lock = redissonClient.getLock("DelayMessageTask:scanFutureMessages");
        try {
            // 尝试获取锁，等待5秒，持有锁60秒
            if (lock.tryLock(5, 300, TimeUnit.SECONDS)) {
                try {
                    // 执行消息处理逻辑
                    processFutureMessages();
                } finally {
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    } else {
                        log.warn("锁已不再被当前线程持有，跳过解锁操作");
                    }
                }
            } else {
                log.debug("未获取到分布式锁，跳过本次处理");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取分布式锁被中断", e);
        } catch (Exception e) {
            log.error("处理延迟消息任务失败", e);
        }
    }

    /**
     * 处理过期但可能遗漏的消息，定义范围为（-60 ～ -5）
     */
    private void processPastMessages() {
        ZonedDateTime currentTime = ZonedDateTime.now();

        ZonedDateTime startTime = currentTime.minusMinutes(60);
        ZonedDateTime endTime = currentTime.minusMinutes(5); // 处理未来24小时内的消息

        log.info("[processPastMessages]开始处理延迟消息，时间范围: {} 到 {}", startTime, endTime);

        try {
            // 使用分页查询避免一次性加载过多数据
            int pageSize = 1000;
            int pageNum = 0;
            int totalProcessed = 0;
            int batchSize = 100;
            boolean hasMore = true;

            while (hasMore) {
                List<DelayMessage> messages = messageMapper.selectLongTermMessages(startTime, endTime, pageSize, 0);

                if (messages.isEmpty()) {
                    hasMore = false;
                } else {
                    // 处理当前页的消息
                    for (int i = 0; i < messages.size(); i += batchSize) {
                        int endIndex = Math.min(i + batchSize, messages.size());
                        List<DelayMessage> batch = messages.subList(i, endIndex);
                        processBatch(batch);
                    }

                    totalProcessed += messages.size();
                    pageNum++;

                    // 如果返回的消息数量小于页大小，说明没有更多数据了
                    if (messages.size() < pageSize) {
                        hasMore = false;
                    }
                }
            }

            log.info("长期延迟消息处理完成，共处理 {} 条消息", totalProcessed);
        } catch (Exception e) {
            log.error("处理长期延迟消息失败", e);
        }
    }


    /**
     * - 10 ~ 3 过期 10分钟到未来 3分钟内没有处理的数据
     * 1分钟调度一次
     * 处理即将到期的消息（5分钟内）
     * 这些消息需要更精确的延迟处理
     */
    private void processMessages() {
        ZonedDateTime currentTime = ZonedDateTime.now();

        //时间回退 1 小时，避免因为服务宕机而导致部分数据没有消费掉
        ZonedDateTime startTime = currentTime.minusMinutes(10);
        ZonedDateTime endTime = currentTime.plusMinutes(3);

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("【processMessages】开始处理延迟消息，时间范围: {} 到 {}", startTime, endTime);

        try {
            // 使用分页查询避免一次性加载过多数据
            int pageSize = 1000;
            int pageNum = 0;
            int batchSize = 100;
            int totalProcessed = 0;
            boolean hasMore = true;

            while (hasMore) {
                //补偿一小时内的过期数据
                List<DelayMessage> messages = messageMapper.selectLongTermMessages(startTime, endTime, pageSize, 0);

                if (messages.isEmpty()) {
                    log.info("【done】所有消息已处理完毕， pageNum={}", pageNum);
                    hasMore = false;
                } else {
                    // 处理当前页的消息
                    for (int i = 0; i < messages.size(); i += batchSize) {
                        int endIndex = Math.min(i + batchSize, messages.size());
                        List<DelayMessage> batch = messages.subList(i, endIndex);
                        //放在线程池中执行
                        //messagePostTaskExecutor.execute(() -> {
                        processBatch(batch);
                        //log.info("messagePostTaskExecutor {}",batch.stream().map(DelayMessage::getId).toList());
                        //});
                    }

                    totalProcessed += messages.size();
                    //不需要页数增加，因为处理的同时标记了数据的状态，总是从第一页获取数据，直到获取不到
                    //pageNum++;

                    // 如果返回的消息数量小于页大小，说明没有更多数据了
                    if (messages.size() < pageSize) {
                        log.info("【done】已处理完最后一页数据， pageNum={}", pageNum);
                        hasMore = false;
                    }
                }
            }
            stopWatch.stop();
            log.info("期延迟消息处理完成，共处理 {} 条消息,cost={}", totalProcessed, stopWatch.getTotalTimeSeconds());
        } catch (Exception e) {
            log.error("处理长期延迟消息失败", e);
        }
    }

    /**
     * 处理未来消息（3分钟-5分钟）内
     * 这些消息相对较近，但不需要特别精确的延迟
     * 从 5 分钟调整到 3 分钟，降低消息堆积
     */
    private void processFutureMessages() {
        ZonedDateTime currentTime = ZonedDateTime.now();

        ZonedDateTime startTime = currentTime.plusMinutes(3);
        ZonedDateTime endTime = currentTime.plusMinutes(3);

        log.info("[processFutureMessages]开始处理延迟消息，时间范围: {} 到 {}", startTime, endTime);

        try {
            // 使用分页查询避免一次性加载过多数据
            int pageSize = 1000;
            int pageNum = 0;
            int totalProcessed = 0;
            int batchSize = 100;
            boolean hasMore = true;

            while (hasMore) {
                List<DelayMessage> messages = messageMapper.selectLongTermMessages(startTime, endTime, pageSize, 0);

                if (messages.isEmpty()) {
                    hasMore = false;
                } else {
                    // 处理当前页的消息
                    for (int i = 0; i < messages.size(); i += batchSize) {
                        int endIndex = Math.min(i + batchSize, messages.size());
                        List<DelayMessage> batch = messages.subList(i, endIndex);
                        processBatch(batch);
                    }

                    totalProcessed += messages.size();
                    pageNum++;

                    // 如果返回的消息数量小于页大小，说明没有更多数据了
                    if (messages.size() < pageSize) {
                        hasMore = false;
                    }
                }
            }

            log.info("长期延迟消息处理完成，共处理 {} 条消息", totalProcessed);
        } catch (Exception e) {
            log.error("处理长期延迟消息失败", e);
        }
    }


    /**
     * 批量处理消息
     * 将消息批量发送到RabbitMQ并更新状态
     *
     * @param messages 消息批次
     */
    private void processBatch(List<DelayMessage> messages) {
        if (messages.isEmpty()) {
            return;
        }

        ZonedDateTime now = ZonedDateTime.now();
        List<DelayMessage> sentMessages = new ArrayList<>();
        List<DelayMessage> transferredMessages = new ArrayList<>();

        for (DelayMessage message : messages) {
            try {
                // 判断消息是否已到期
                if (message.getScheduledTime().isBefore(now) || message.getScheduledTime().isEqual(now)) {
                    // 已到期，直接发送
                    sendMessageToRabbitMQ(message);
                    sentMessages.add(message);
                } else {
                    // 未到期，但在处理窗口内，转移到合适的延迟队列
                    long delayMillis = ChronoUnit.MILLIS.between(now, message.getScheduledTime());
                    if (delayMillis <= 300000) { // 5分钟内
                        // 转移到RabbitMQ的延迟队列
                        transferToDelayQueue(message, delayMillis);
                        transferredMessages.add(message);
                    }
                }
            } catch (Exception e) {
                log.error("处理消息失败: messageId={}, messageType={}", message.getMessageId(), message.getMessageType(), e);
            }
        }

        // 批量更新已发送消息的状态
        if (!sentMessages.isEmpty()) {
            // 使用事务批处理
            updateMessagesWithTransaction(sentMessages, DelayMessage.STATUS_SENT);
        }

        // 批量更新已转移消息的状态
        if (!transferredMessages.isEmpty()) {
            // 使用事务批处理
            updateMessagesWithTransaction(transferredMessages, DelayMessage.STATUS_TRANSFERRED);
        }

    }

    /**
     * 发送消息到RabbitMQ
     */
    private void sendMessageToRabbitMQ(DelayMessage delayMessage) {
        try {
            //TODO 考虑到发送消息目前不走定申请队列，这里还不能启动nameGenerator， 等 所有配置迁移过来后，在启用，目前不启用，也没有太大问题，长期消息主要是在用的方式发送
            //String exchangeName = nameGenerator.exchangeName(delayMessage.getExchange());
            String exchangeName = delayMessage.getExchange();

            Message message = MessageBuilder.withBody(delayMessage.getPayload().getBytes()).setMessageId(delayMessage.getMessageId()).build();
            rabbitTemplate.convertAndSend(exchangeName, delayMessage.getRoutingKey(), message);
        } catch (Exception e) {
            // 增加重试计数
            int retryCount = delayMessage.getRetryCount() == null ? 0 : delayMessage.getRetryCount();
            delayMessage.setRetryCount(retryCount + 1);

            log.error("消息发送失败且超过最大重试次数，已移入死信: messageId={}", delayMessage.getMessageId(), e);

            throw e;
        }
    }

    private void transferToDelayQueue(DelayMessage delayMessage, long delayMillis) throws Exception {
        // 根据延迟时间选择合适的延迟队列
        String routingKey;
        if (delayMillis <= 60000) {
            routingKey = QueueConstants.DLX_1MIN_KEY; // 1min.queue.dlx
        } else if (delayMillis <= 120000) {
            routingKey = QueueConstants.DLX_2MIN_KEY; // 2min.queue.dlx
        } else if (delayMillis <= 180000) {
            routingKey = QueueConstants.DLX_3MIN_KEY; // 3min.queue.dlx
        } else if (delayMillis <= 240000) {
            routingKey = QueueConstants.DLX_4MIN_KEY; // 4min.queue.dlx
        } else {
            routingKey = QueueConstants.DLX_5MIN_KEY; // 5min.queue.dlx
        }
        // 发送到延迟队列

        DelayMessage simpleDelayMessage = new DelayMessage();
        simpleDelayMessage.setMessageId(delayMessage.getMessageId());
        simpleDelayMessage.setExchange(delayMessage.getExchange());
        simpleDelayMessage.setRoutingKey(delayMessage.getRoutingKey());
        simpleDelayMessage.setPayload(delayMessage.getPayload());

        Message message = MessageBuilder.withBody(JsonUtil.writeBytes(simpleDelayMessage)).setMessageId(delayMessage.getMessageId()).build();

        String exchangeName = nameGenerator.exchangeName(QueueConstants.TRANSFER_EXCHANGE);
        rabbitTemplate.convertAndSend(exchangeName, routingKey, message);

        if (log.isDebugEnabled()) {
            log.debug("消息已转移到延迟队列: messageId={}, queue={}, delayMillis={}", delayMessage.getMessageId(), routingKey, delayMillis);
        }
    }

    /**
     * 使用事务批处理更新消息状态
     *
     * @param messages 需要更新的消息列表
     * @param status   目标状态
     */
    protected void updateMessagesWithTransaction(List<DelayMessage> messages, int status) {
        if (messages == null || messages.isEmpty()) {
            return;
        }
        DelayMessage first = messages.get(0);
        DelayMessage last = messages.get(messages.size() - 1);
        //同一天，直接更新
        ZonedDateTime sentTime = ZonedDateTime.now();
        if (first.getScheduledTime().toLocalDate().equals(last.getScheduledTime().toLocalDate())) {
            String tablePostfix = pattern.format(first.getScheduledTime());
            List<Long> messageIdList = messages.stream().map(DelayMessage::getId).toList();
            messageMapper.batchUpdateMessageStatusById(tablePostfix, messageIdList, status, sentTime);
        } else {
            //跨天，分组更新
            Map<String, List<DelayMessage>> listMap = messages.stream()
                    .map(item -> {
                        item.setTablePostfix(pattern.format(item.getScheduledTime()));
                        return item;
                    }).collect(Collectors.groupingBy(DelayMessage::getTablePostfix));
            listMap.forEach((key, value) -> {
                //按照目标更新表分组更新
                messageMapper.batchUpdateMessageStatusById(key, value.stream().map(DelayMessage::getId).toList(), status, sentTime);
            });
        }
    }

}
