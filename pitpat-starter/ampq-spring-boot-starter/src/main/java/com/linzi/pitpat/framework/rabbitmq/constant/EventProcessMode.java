package com.linzi.pitpat.framework.rabbitmq.constant;

import java.util.Arrays;

public enum EventProcessMode {
    /**
     * 同步处理 - 在当前线程中立即处理
     */
    SYNC,
    /**
     * 异步处理 - 在异步线程池中处理
     */
    ASYNC,

    AUTO;

    private static final EventProcessMode[] values = EventProcessMode.values();

    public static EventProcessMode resolve(String name) {
        return Arrays.stream(values).filter(item -> item.name().equals(name)).findFirst().orElse(EventProcessMode.SYNC);
    }

}
