package com.linzi.pitpat.framework.rabbitmq.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * 死信消息实体类
 * 用于存储处理失败的延迟消息
 */
@Data
@TableName("zns_delay_queue_dead_letter")
public class DeadLetterMessage {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 消息唯一标识
     */
    private String messageId;

    /**
     * 消息类型
     */
    private String messageType;

    /**
     * 交换机名称
     */
    private String exchange;

    /**
     * 路由键
     */
    private String routingKey;

    /**
     * 批次ID
     */
    private String batchId;

    /**
     * 消息内容（JSON格式）
     */
    private String payload;

    /**
     * 计划发送时间
     */
    private ZonedDateTime scheduledTime;

    /**
     * 原始消息状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private ZonedDateTime createdTime;

    /**
     * 死信时间
     */
    private ZonedDateTime deadTime;

    /**
     * 失败原因
     */
    private String errorMessage;

    /**
     * 重试次数
     */
    private Integer retryCount;
} 