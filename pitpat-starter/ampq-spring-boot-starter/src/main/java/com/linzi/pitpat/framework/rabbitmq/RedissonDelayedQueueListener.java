package com.linzi.pitpat.framework.rabbitmq;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.linzi.pitpat.framework.rabbitmq.model.MessageWrapper;
import com.linzi.pitpat.framework.rabbitmq.property.RabbitMQProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Component
@RequiredArgsConstructor
public class RedissonDelayedQueueListener implements ApplicationContextAware {
    // 用于存储队列名称与对应的队列的映射
    //private final Table<String, String, String> table = HashBasedTable.create();
    private final RabbitMQProperties rabbitMQProperties;
    private final RedissonClient redissonClient;
    private final RabbitTemplate rabbitTemplate;
    private final QueueNameGenerator nameGenerator;

    // 用于管理监听线程
    private final ConcurrentHashMap<String, Thread> listenerThreads = new ConcurrentHashMap<>();
    // 用于控制线程停止
    private final AtomicBoolean shutdown = new AtomicBoolean(false);


    private final static ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
            .setNameFormat("mc-delay-pool-%d")
            .setUncaughtExceptionHandler((thread, throwable) ->
                    log.error("ThreadPool {} got exception", thread, throwable))
            .build();

    /**
     * 创建线程池，使⽤有界阻塞队列防⽌内存溢出
     */
    private final static ExecutorService statsThreadPool = new ThreadPoolExecutor(5, 10,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(500), namedThreadFactory);


    /**
     *读取 queue 配置，只监听延迟队列
     * 因为只有延迟队列会投放到短期延迟消息到 redisson 中，避免扫描所有队列，节省线程资源
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        rabbitMQProperties.getBindings().stream()
                .filter(item -> item.getQueue().endsWith(".delay"))
                .forEach(item -> {
                    log.info("queue={}, exchange={}, routingKey={}", item.getQueue(), item.getExchange(), item.getRoutingKey());
                    startThread(item.getQueue());
                });
    }

    /**
     * 启动线程获取队列*
     *
     * @param queueName queueName
     * @param <T>       泛型
     * @return
     */
    private <T> void startThread(String queueName) {
        RBlockingQueue<MessageWrapper<T>> blockingFairQueue = redissonClient.getBlockingQueue(queueName);
        //由于此线程需要常驻，可以新建线程，不用交给线程池管理
        Thread thread = new Thread(() -> {
            log.info("启动监听队列线程" + queueName);
            while (!shutdown.get() && !Thread.currentThread().isInterrupted()) {
                try {
                    // 使用带超时的take方法，避免无限阻塞
                    MessageWrapper<T> message = blockingFairQueue.poll(5, TimeUnit.SECONDS);
                    if (message != null) {
                        // 检查是否正在关闭
                        if (shutdown.get()) {
                            log.info("应用正在关闭，停止处理新消息: queueName={}", queueName);
                            break;
                        }

                        statsThreadPool.execute(() -> {
                            try {
                                // 发送消息到RabbitMQ
                                String exchangeName = nameGenerator.exchangeName(message.getExchange());
                                rabbitTemplate.convertAndSend(exchangeName, message.getRoutingKey(), message.getPayload());

                                log.info("处理短期延迟消息: messageId={}, exchange={}, routingKey={}",
                                        message.getMessageId(), message.getExchange(), message.getRoutingKey());
                            } catch (Exception e) {
                                handleException("处理短期延迟消息失败", e, message.getExchange(), message.getRoutingKey(), message);
                            }
                        });
                    }
                } catch (InterruptedException e) {
                    log.info("监听队列线程被中断: queueName={}", queueName);
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    if (!shutdown.get()) {
                        log.error("监听队列线程错误: queueName={}", queueName, e);
                        try {
                            Thread.sleep(10000);
                        } catch (InterruptedException ex) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }
            }
            log.info("监听队列线程已停止: queueName={}", queueName);
        });
        thread.setName(queueName);
        thread.start();

        // 将线程添加到管理集合中
        listenerThreads.put(queueName, thread);
    }


    /**
     * Spring Boot 关闭时的清理方法
     */
    @PreDestroy
    public void shutdown() {
        log.info("开始关闭 RedissonDelayedQueueListener...");

        // 设置关闭标志
        shutdown.set(true);

        // 关闭线程池，不再接受新任务
        statsThreadPool.shutdown();

        // 中断所有监听线程
        listenerThreads.forEach((queueName, thread) -> {
            log.info("正在中断监听线程: queueName={}", queueName);
            thread.interrupt();
        });

        // 等待监听线程结束
        listenerThreads.forEach((queueName, thread) -> {
            try {
                thread.join(5000); // 最多等待5秒
                if (thread.isAlive()) {
                    log.warn("监听线程未能在5秒内停止: queueName={}", queueName);
                } else {
                    log.info("监听线程已成功停止: queueName={}", queueName);
                }
            } catch (InterruptedException e) {
                log.warn("等待监听线程停止时被中断: queueName={}", queueName);
                Thread.currentThread().interrupt();
            }
        });

        // 等待线程池中的任务完成
        try {
            if (!statsThreadPool.awaitTermination(10, TimeUnit.SECONDS)) {
                log.warn("线程池未能在10秒内完全关闭，强制关闭");
                statsThreadPool.shutdownNow();
            } else {
                log.info("线程池已成功关闭");
            }
        } catch (InterruptedException e) {
            log.warn("等待线程池关闭时被中断");
            statsThreadPool.shutdownNow();
            Thread.currentThread().interrupt();
        }

        log.info("RedissonDelayedQueueListener 已完成关闭");
    }

    /**
     * 统一异常处理
     *
     * @param message    错误消息
     * @param e          异常
     * @param exchange   交换机
     * @param routingKey 路由键
     * @param payload    消息内容
     */
    private void handleException(String message, Exception e, String exchange, String routingKey, Object payload) {
        log.error("{}. exchange={}, routingKey={}, payload={}",
                message, exchange, routingKey, payload, e);
    }
}
