package com.linzi.pitpat.framework.rabbitmq.service;


import com.linzi.pitpat.framework.rabbitmq.model.MessageWrapper;

/**
 * 延迟消息服务接口
 * 提供基于Redisson的短期延迟消息功能
 */
public interface DelayedMessageService {

    /**
     * 发送短期延迟消息
     * @param exchange 交换机名称
     * @param routingKey 路由键 必须是精确的 key
     * @param message 消息对象（将被序列化为JSON）
     * @param delayMillis 延迟时间（毫秒）
     * @param <T> 消息类型
     */
    <T> MessageWrapper<T> sendShortDelayMessage(String exchange, String routingKey, T message, long delayMillis);

    /**
     * 处理到期的延迟消息
     * 此方法由定时任务调用，用于检查和处理到期的延迟消息
     */
    void processExpiredMessages();
}
