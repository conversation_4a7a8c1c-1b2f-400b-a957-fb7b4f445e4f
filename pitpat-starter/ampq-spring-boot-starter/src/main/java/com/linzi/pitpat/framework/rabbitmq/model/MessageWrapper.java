package com.linzi.pitpat.framework.rabbitmq.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.UUID;

/**
 * 统一消息模型
 * 用于封装所有类型的消息，不区分是否为延迟消息
 *
 * @param <T> 消息内容类型
 */
@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MessageWrapper<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    private String messageId = UUID.randomUUID().toString();

    /**
     * 消息类型
     */
    private String messageType;

    /**
     * 消息内容
     */
    private T payload;

    /**
     * 交换机名称
     */
    private String exchange;

    /**
     * 路由键
     */
    private String routingKey;

    private String batchId;

    /**
     * 发送时间戳
     */
    private long timestamp = System.currentTimeMillis();

    /**
     * 消息来源（应用名称）
     */
    private String source;

    /**
     * 延迟时间（毫秒），0表示不延迟
     */
    private long delayMillis;

    /**
     * 创建时间（毫秒时间戳）
     */
    private long createTime = System.currentTimeMillis();

    /**
     * 计划执行时间（毫秒时间戳），仅当delayMillis > 0时有效
     */
    private long scheduledTime;

    /**
     * 创建一个普通消息
     *
     * @param payload 消息内容
     * @param <T>     消息内容类型
     * @return 统一消息实例
     */
    public static <T> MessageWrapper<T> ofMessage(T payload) {
        return new MessageWrapper<T>()
                .setMessageType(UUID.randomUUID().toString())
                .setMessageType(payload.getClass().getName())
                .setPayload(payload);
    }

    /**
     * 创建一个普通消息
     *
     * @param payload 消息内容
     * @param <T>     消息内容类型
     * @return 统一消息实例
     */
    public static <T> MessageWrapper<T> ofMessage(T payload, String exchange, String routingKey) {
        return new MessageWrapper<T>()
                .setMessageType(UUID.randomUUID().toString())
                .setMessageType(payload.getClass().getName())
                .setExchange(exchange)
                .setRoutingKey(routingKey)
                .setPayload(payload);
    }

    /**
     * 创建一个延迟消息
     *
     * @param payload     消息内容
     * @param exchange    交换机名称
     * @param routingKey  路由键
     * @param delayMillis 延迟时间（毫秒）
     * @param <T>         消息内容类型
     * @return 统一消息实例
     */
    public static <T> MessageWrapper<T> ofMessage(T payload, String exchange, String routingKey, long delayMillis) {
        return new MessageWrapper<T>()
                .setMessageId(UUID.randomUUID().toString())
                .setMessageType(payload.getClass().getName())
                .setPayload(payload)
                .setExchange(exchange)
                .setRoutingKey(routingKey)
                .setDelayMillis(delayMillis)
                .setScheduledTime(System.currentTimeMillis() + delayMillis);
    }


    /**
     * 判断是否为延迟消息
     *
     * @return 是否为延迟消息
     */
    public boolean isDelayed() {
        return delayMillis > 0;
    }
}
