package com.linzi.pitpat.framework.rabbitmq.service;


import com.linzi.pitpat.framework.rabbitmq.model.MessageEvent;
import com.linzi.pitpat.framework.rabbitmq.model.MessageWrapper;

import java.util.Map;

/**
 * 统一消息服务接口
 * 提供实时消息、延迟消息、本地同步消息、本地异步消息的发送能力
 */
public interface QueueMessageService {

    /**
     * 封装 RabbitMq 的消息 路由、路由键、消息体、延迟时间等
     * 替代 sendMessage(String exchange, String routingKey, T message)
     * 替代 sendDelayMessage(String exchange, String routingKey, T message, long delayMillis)
     *
     * @param wrapper
     * @param <T>
     * @return
     */
    default <T> MessageWrapper<T> sendMessage(MessageWrapper<T> wrapper) {
        return null;
    }

    /**
     * 封装 事件类型、消息、调用模式（同步、异步）
     * 替代 sendMessageEvent(String eventType, T message)
     * 替代 sendAsyncMessageEvent(String eventType, T message)
     *
     * @param event
     * @param <T>
     * @return
     */
    default <T> MessageEvent<T> sendMessageEvent(MessageEvent<T> event) {
        return null;
    }

    /**
     * 发送实时消息
     *
     * @param exchange   交换机名称
     * @param routingKey 路由键
     * @param message    消息对象（将被序列化为JSON）
     * @param <T>        消息类型
     */
    <T> MessageWrapper<T> sendMessage(String exchange, String routingKey, T message);

    /**
     * 发送延迟消息
     *
     * @param exchange    交换机名称
     * @param routingKey  路由键
     * @param message     消息对象（将被序列化为JSON）
     * @param delayMillis 延迟时间（毫秒）
     * @param <T>         消息类型
     */
    <T> MessageWrapper<T> sendDelayMessage(String exchange, String routingKey, T message, Long delayMillis);

    /**
     * 发送短期延迟消息（基于Redisson实现）
     *
     * @param exchange    交换机名称
     * @param routingKey  路由键
     * @param message     消息对象（将被序列化为JSON）
     * @param delayMillis 延迟时间（毫秒）
     * @param <T>         消息类型
     */
    <T> MessageWrapper<T> sendDelayMessageShort(String exchange, String routingKey, T message, Long delayMillis);

    /**
     * 发送长期延迟消息（基于数据库实现）
     * 适用于需要长时间延迟的消息场景
     *
     * @param exchange    交换机名称
     * @param routingKey  路由键
     * @param message     消息对象（将被序列化为JSON）
     * @param delayMillis 延迟时间（毫秒）
     * @param <T>         消息类型
     * @return 消息包装对象
     */
    <T> MessageWrapper<T> sendDelayMessageLong(String exchange, String routingKey, T message, Long delayMillis);

    /**
     * 发送本地同步消息（直接调用处理方法）
     *
     * @param eventType 事件类型
     * @param message   消息对象
     * @param <T>       消息类型
     */
    <T> MessageEvent<T> sendMessageEvent(String eventType, T message);

    /**
     * 取消延迟消息
     *
     * @param messageId 消息ID
     * @return 是否成功
     */
    boolean cancelDelayMessage(String messageId);

    /**
     * 查询延迟消息状态
     *
     * @param messageId 消息ID
     * @return 消息状态信息
     */
    Map<String, Object> getDelayMessageStatus(String messageId);
}
