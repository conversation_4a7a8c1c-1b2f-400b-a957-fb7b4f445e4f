package com.linzi.pitpat.framework.rabbitmq.service.impl;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.linzi.pitpat.framework.rabbitmq.model.MessageWrapper;
import com.linzi.pitpat.framework.rabbitmq.property.RabbitMQProperties;
import com.linzi.pitpat.framework.rabbitmq.service.DelayedMessageService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 延迟消息服务实现类
 */
@Slf4j
@Service
public class DelayedMessageServiceImpl implements DelayedMessageService {

    private final RedissonClient redissonClient;

    // 用于存储队列名称与对应的队列的映射
    private final Table<String, String, String> table = HashBasedTable.create();


    public DelayedMessageServiceImpl(
            RedissonClient redissonClient,
            RabbitMQProperties rabbitMQProperties) {
        this.redissonClient = redissonClient;


        rabbitMQProperties.getBindings().stream().filter(item -> item.getQueue().endsWith(".delay"))
                .forEach(item -> {
                    log.info("queue={}, exchange={}, routingKey={}", item.getQueue(), item.getExchange(), item.getRoutingKey());
                    table.put(item.getExchange(), item.getRoutingKey(), item.getQueue());
                });
    }

    @Override
    public <T> MessageWrapper<T> sendShortDelayMessage(String exchange, String routingKey, T message, long delayMillis) {
        MessageWrapper<T> wrapper = null;
        try {
            // 创建延迟消息对象
            wrapper = MessageWrapper.ofMessage(message, exchange, routingKey, System.currentTimeMillis() + delayMillis);

            // 获取或创建队列
            RDelayedQueue<MessageWrapper<T>> queue = getOrCreateQueue(exchange, routingKey);

            queue.offer(wrapper, delayMillis, TimeUnit.MILLISECONDS);

            // 记录日志
            if (log.isDebugEnabled()) {
                log.debug("发送短期延迟消息:  exchange={}, routingKey={}, messageId={}, messageType={}, delay={}ms", exchange, routingKey, wrapper.getMessageId(), wrapper.getMessageType(), delayMillis);
            }
        } catch (Exception e) {
            handleException("发送短期延迟消息失败", e, exchange, routingKey, message);
        }
        return wrapper;
    }

    @Override
    public void processExpiredMessages() {

    }

    /**
     * 获取或创建队列
     *
     * @param exchange   交换机名称
     * @param routingKey 路由键
     * @return 队列
     */
    private <T> RDelayedQueue<MessageWrapper<T>> getOrCreateQueue(String exchange, String routingKey) {
        String delayQueueName = table.get(exchange, routingKey);
        RBlockingQueue<MessageWrapper<T>> blockingQueue = redissonClient.getBlockingQueue(delayQueueName);
        return redissonClient.getDelayedQueue(blockingQueue);
    }

    /**
     * 统一异常处理
     *
     * @param message    错误消息
     * @param e          异常
     * @param exchange   交换机
     * @param routingKey 路由键
     * @param payload    消息内容
     */
    private void handleException(String message, Exception e, String exchange, String routingKey, Object payload) {
        log.error("{}. exchange={}, routingKey={}, payload={}",
                message, exchange, routingKey, payload, e);

    }
}
