package com.linzi.pitpat.framework.rabbitmq;

import com.linzi.pitpat.framework.rabbitmq.property.RabbitMQProperties;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.rabbit.config.DirectRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.ConditionalRejectingErrorHandler;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 消息组件自动配置类
 */
@Configuration
@EnableAsync
@EnableConfigurationProperties({RabbitMQProperties.class})
public class QueueMessageAutoConfiguration {

    /**
     * 消息处理线程池
     */
    @Bean("messageTaskExecutor")
    public Executor messageTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2);
        executor.setQueueCapacity(500);
        executor.setThreadNamePrefix("message-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }


    /**
     * 消息处理线程池
     */
    @Bean("messagePostTaskExecutor")
    public Executor messagePostTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2);
        executor.setThreadNamePrefix("message-post-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }


    @Bean
    public QueueNameGenerator nameGenerator(RabbitMQProperties properties, Environment environment) {
        return new QueueNameGenerator(properties, environment);
    }

    @Bean
    @ConditionalOnMissingBean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        return template;
    }

    @Bean
    public DirectRabbitListenerContainerFactory directRabbitListenerContainerFactory(
            ConnectionFactory connectionFactory, RabbitProperties properties) {

        DirectRabbitListenerContainerFactory factory = new DirectRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);

        // 从配置文件中读取
        factory.setConsumersPerQueue(properties.getListener().getDirect().getConsumersPerQueue());
        factory.setPrefetchCount(properties.getListener().getDirect().getPrefetch());

        // 错误处理器
        factory.setErrorHandler(new ConditionalRejectingErrorHandler());

        // 消息确认模式
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);

        // 重试策略
        factory.setRetryTemplate(new RetryTemplate());

        return factory;
    }

}
