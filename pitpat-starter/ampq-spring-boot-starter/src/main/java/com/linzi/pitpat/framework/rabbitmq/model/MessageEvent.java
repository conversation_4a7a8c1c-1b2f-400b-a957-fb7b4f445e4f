package com.linzi.pitpat.framework.rabbitmq.model;

import com.linzi.pitpat.framework.rabbitmq.constant.EventProcessMode;
import lombok.Builder;
import lombok.Data;

import java.util.UUID;

/**
 * 消息事件模型，用于本地事件传递
 *
 * @param <T> 消息内容类型
 */
@Data
@Builder
public class MessageEvent<T> {

    /**
     * 事件类型
     */
    private String eventType;


    /**
     * 处理模式: 同步、异步
     *
     * @see EventProcessMode
     */
    private EventProcessMode processMode;

    /**
     * 消息内容
     */
    private T payload;

    /**
     * 创建时间戳
     */
    private long timestamp;
    /**
     * 事件消息 ID
     */
    private String messageId;

    public static <T> MessageEvent<T> of(String eventType, T payload) {
        return MessageEvent.<T>builder()
                .eventType(eventType)
                //.processMode(EventProcessMode.SYNC)
                .payload(payload)
                .timestamp(System.currentTimeMillis())
                .messageId(UUID.randomUUID().toString())
                .build();
    }

    public static <T> MessageEvent<T> ofAsync(String eventType, T payload) {
        return MessageEvent.<T>builder()
                .eventType(eventType)
                //.processMode(EventProcessMode.ASYNC)
                .payload(payload)
                .timestamp(System.currentTimeMillis())
                .messageId(UUID.randomUUID().toString())
                .build();
    }
}
