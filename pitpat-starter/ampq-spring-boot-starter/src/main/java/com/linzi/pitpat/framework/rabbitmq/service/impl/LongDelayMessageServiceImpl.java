package com.linzi.pitpat.framework.rabbitmq.service.impl;

import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.framework.rabbitmq.mapper.DelayMessageMapper;
import com.linzi.pitpat.framework.rabbitmq.model.DelayMessage;
import com.linzi.pitpat.framework.rabbitmq.model.MessageWrapper;
import com.linzi.pitpat.framework.rabbitmq.service.AsyncBatchProcessor;
import com.linzi.pitpat.framework.rabbitmq.service.LongDelayMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.BlockingQueue;

/**
 * 重构后的延迟消息服务
 * 使用 AsyncBatchProcessor 实现异步批量处理，降低代码复杂度
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LongDelayMessageServiceImpl implements LongDelayMessageService {

    private final DelayMessageMapper messageMapper;

    // 智能批量配置
    private static final int BATCH_SIZE = 500;
    private static final long BATCH_TIME_WINDOW_MS = 2000;

    // 异步批量处理器（懒加载）
    private final AsyncBatchProcessor<DelayMessageWithContext> asyncProcessor =
            new AsyncBatchProcessor<>("DelayMessage", BATCH_SIZE, BATCH_TIME_WINDOW_MS, this::processBatch);

    /**
     * 包含追踪上下文的延迟消息
     */
    private static class DelayMessageWithContext {
        final DelayMessage message;
        final String traceId;
        final String spanId;

        DelayMessageWithContext(DelayMessage message) {
            this.message = message;
            this.traceId = MDC.get("traceId");
            this.spanId = MDC.get("spanId");
        }

        public void setTraceContext() {
            if (traceId != null) {
                MDC.put("traceId", traceId);
                if (spanId != null) {
                    MDC.put("spanId", spanId);
                }
            }
        }
    }

    @Override
    public <T> MessageWrapper<T> sendLongDelayMessage(String exchange, String routingKey, T message, long delayMillis) {
        //TODO 可以考虑对  exchange+  routingKey + delayMillis 做 key, 缓存发送时间， 可以降低因为入库时间对发送的影响
        //TODO 对同一个交换机的的同一个 routing key 的同一个发送时间，做一个缓存，确保入库时，他们的发送时间是一致的
        ZonedDateTime scheduledTime = ZonedDateTime.now().plus(delayMillis, ChronoUnit.MILLIS).withSecond(0).withNano(0);

        try {
            // 创建消息包装
            MessageWrapper<T> wrapper = MessageWrapper.ofMessage(message, exchange, routingKey, delayMillis);

            // 创建延迟消息
            DelayMessage delayMessage = createDelayMessage(wrapper, exchange, routingKey, message, scheduledTime);

            // 添加到异步处理器（首次使用时自动初始化）
            DelayMessageWithContext messageWithContext = new DelayMessageWithContext(delayMessage);
            boolean success = asyncProcessor.offer(messageWithContext);

            if (!success) {
                log.warn("消息缓冲区已满，消息可能丢失: messageId={}", wrapper.getMessageId());
            }

            if (log.isDebugEnabled()) {
                log.debug("延迟消息已添加到处理器: messageId={}, traceId={}", wrapper.getMessageId(), messageWithContext.traceId);
            }

            return wrapper;

        } catch (Exception e) {
            log.error("发送长期延迟消息失败: exchange={}, routingKey={}", exchange, routingKey, e);
            throw new RuntimeException("发送长期延迟消息失败", e);
        }
    }

    /**
     * 创建延迟消息对象
     */
    private <T> DelayMessage createDelayMessage(MessageWrapper<T> wrapper, String exchange, String routingKey, T message, ZonedDateTime scheduledTime) {
        DelayMessage delayMessage = new DelayMessage();
        delayMessage.setMessageId(wrapper.getMessageId());
        delayMessage.setBatchId(exchange + ":" + routingKey + ":" + scheduledTime);
        delayMessage.setExchange(exchange);
        delayMessage.setRoutingKey(routingKey);
        delayMessage.setPayload(JsonUtil.writeString(message));
        delayMessage.setScheduledTime(scheduledTime);
        delayMessage.setStatus(DelayMessage.STATUS_PENDING);
        delayMessage.setCreatedTime(ZonedDateTime.now());
        delayMessage.setMessageType(wrapper.getPayload().getClass().getSimpleName());
        return delayMessage;
    }

    /**
     * 处理一批延迟消息（由 AsyncBatchProcessor 调用）
     */
    private void processBatch(List<DelayMessageWithContext> contextBatch) {
        if (contextBatch.isEmpty()) {
            return;
        }

        List<DelayMessage> batch = new ArrayList<>(contextBatch.size());
        for (DelayMessageWithContext msgContext : contextBatch) {
            msgContext.message.setCreatedTime(ZonedDateTime.now()); //重新设置创建时间，因为是异步的这里的时间才是最近数据库的保存的时间
            batch.add(msgContext.message);
        }

        // 使用第一个消息的追踪上下文
        DelayMessageWithContext firstMsg = contextBatch.get(0);
        String currentTraceId = MDC.get("traceId");

        try {
            // 临时设置追踪上下文
            firstMsg.setTraceContext();

            // 批量插入
            messageMapper.batchInsert(batch);

            log.info("异步批量插入延迟消息成功，数量: {}", batch.size());
        } catch (Exception e) {
            log.error("异步批量插入延迟消息失败", e);
        } finally {
            // 恢复原有追踪上下文
            restoreTraceContext(currentTraceId);
        }
    }


    /**
     * 恢复追踪上下文
     */
    private void restoreTraceContext(String originalTraceId) {
        if (originalTraceId != null) {
            MDC.put("traceId", originalTraceId);
        } else {
            MDC.remove("traceId");
            MDC.remove("spanId");
        }
    }

    @PreDestroy
    public void shutdown() {
        // 优雅关闭异步处理器，等待最多10秒
        asyncProcessor.shutdown(10000);
    }

    // 保持原有接口兼容性
    @Override
    public boolean cancelDelayMessage(String messageId) {
        try {
            // ShardingSphere会自动路由到正确的表
            DelayMessage delayMessage = messageMapper.selectByMessageId(messageId);
            if (delayMessage == null) {
                log.warn("取消延迟消息失败，消息不存在: messageId={}", messageId);
                return false;
            }

            if (delayMessage.getStatus() != DelayMessage.STATUS_PENDING) {
                log.warn("取消延迟消息失败，消息状态不正确: messageId={}, status={}", messageId, delayMessage.getStatus());
                return false;
            }

            // 更新状态为已取消，ShardingSphere自动路由
            int result = messageMapper.updateMessageStatus(delayMessage.getScheduledTime(), messageId, DelayMessage.STATUS_CANCELLED, ZonedDateTime.now());

            if (result > 0) {
                log.info("取消延迟消息成功: messageId={}", messageId);
                return true;
            } else {
                log.warn("取消延迟消息失败: messageId={}", messageId);
                return false;
            }

        } catch (Exception e) {
            log.error("取消延迟消息异常: messageId={}", messageId, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getMessageStatus(String messageId) {
        try {
            // ShardingSphere会自动路由到正确的表
            DelayMessage delayMessage = messageMapper.selectByMessageId(messageId);
            if (delayMessage == null) {
                return null;
            }

            Map<String, Object> status = new HashMap<>();
            status.put("messageId", delayMessage.getMessageId());
            status.put("messageType", delayMessage.getMessageType());
            status.put("exchange", delayMessage.getExchange());
            status.put("routingKey", delayMessage.getRoutingKey());
            status.put("scheduledTime", delayMessage.getScheduledTime());
            status.put("status", delayMessage.getStatus());
            status.put("statusText", getStatusText(delayMessage.getStatus()));
            status.put("createdTime", delayMessage.getCreatedTime());
            status.put("sentTime", delayMessage.getSentTime());

            return status;

        } catch (Exception e) {
            log.error("查询消息状态失败: messageId={}", messageId, e);
            return null;
        }
    }


    @Override
    public int cleanupSentMessages(int retentionDays) {
        try {
            ZonedDateTime beforeDate = ZonedDateTime.now().minus(retentionDays, ChronoUnit.DAYS);
            log.info("开始清理 {} 天前已发送的延迟消息，截止日期: {}", retentionDays, beforeDate);

            // 调用Mapper方法删除逻辑表中符合条件的数据
            // ShardingSphere将根据beforeDate路由到正确的物理表进行删除
            int deletedCount = messageMapper.deleteSentMessagesBeforeDate(beforeDate, DelayMessage.STATUS_SENT);

            if (deletedCount > 0) {
                log.info("清理历史延迟消息完成，共删除 {} 条记录，保留天数: {}", deletedCount, retentionDays);
            }

            return deletedCount;

        } catch (Exception e) {
            log.error("清理历史延迟消息失败", e);
            return 0;
        }
    }


    /**
     * 获取状态文本描述
     */
    private String getStatusText(int status) {
        switch (status) {
            case DelayMessage.STATUS_PENDING:
                return "待发送";
            case DelayMessage.STATUS_SENT:
                return "已发送";
            case DelayMessage.STATUS_CANCELLED:
                return "已取消";
            default:
                return "未知状态";
        }
    }
}
