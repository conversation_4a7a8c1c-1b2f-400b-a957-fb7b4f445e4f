package com.linzi.pitpat.framework.rabbitmq.service;


import com.linzi.pitpat.framework.rabbitmq.model.MessageWrapper;

import java.time.ZonedDateTime;
import java.util.Map;

/**
 * 简化延迟消息服务接口
 * 专门处理基于数据库的长期延迟消息，与Redis短期延迟方案区分
 */
public interface LongDelayMessageService {

    /**
     * 发送长期延迟消息（基于数据库实现）
     * 适用于需要长时间延迟的消息场景
     *
     * @param exchange    交换机名称
     * @param routingKey  路由键
     * @param message     消息对象
     * @param delayMillis 延迟时间（毫秒）
     * @param <T>         消息类型
     * @return 消息包装对象
     */
    <T> MessageWrapper<T> sendLongDelayMessage(String exchange, String routingKey, T message, long delayMillis);

    /**
     * 取消延迟消息
     *
     * @param messageId 消息ID
     * @return 是否成功
     */
    boolean cancelDelayMessage(String messageId);

    /**
     * 查询消息状态
     *
     * @param messageId 消息ID
     * @return 消息状态信息
     */
    Map<String, Object> getMessageStatus(String messageId);
    /**
     * 清理已发送的历史消息
     *
     * @param retentionDays 保留天数
     * @return 清理的消息数量
     */
    int cleanupSentMessages(int retentionDays);
}
