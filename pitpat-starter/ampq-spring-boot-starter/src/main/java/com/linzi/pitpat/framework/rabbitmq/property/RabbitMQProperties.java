package com.linzi.pitpat.framework.rabbitmq.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * RabbitMQ配置属性类
 */
@ConfigurationProperties(prefix = "spring.rabbitmq.custom")
@Data
public class RabbitMQProperties {

    /**
     * 要应用于所有队列和交换器名称的环境前缀。
     * 这可以实现环境隔离（例如："dev."、"test."）
     * 生产环境通常不设置前缀
     */
    private String environmentPrefix = "";

    /**
     * 用于启用/禁用环境前缀的标志
     */
    private boolean enableEnvironmentPrefix = true;

    private List<QueueProperties> queues = new ArrayList<>();
    private List<ExchangeProperties> exchanges = new ArrayList<>();
    private List<BindingProperties> bindings = new ArrayList<>();
    private Map<String, BindingProperties> bindingCache = new HashMap<>();

    @Data
    public static class QueueProperties {
        private String key;
        private String name;
        private boolean durable = true;
        private String deadLetterExchange;
        private String deadLetterRoutingKey;
        private long ttl = 0;
        private int maxLength = 0;
        private Map<String, Object> arguments = new HashMap<>();
    }

    @Data
    public static class ExchangeProperties {
        private String name;
        private String type = "direct";
        private boolean durable = true;
        private boolean delayed = false;
        private boolean autoDelete = false;
    }

    @Data
    public static class BindingProperties {
        private String queue;
        private String exchange;
        private String routingKey;
    }
}
