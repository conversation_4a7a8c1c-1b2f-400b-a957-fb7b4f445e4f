package com.linzi.pitpat.framework.rabbitmq;


import com.linzi.pitpat.framework.rabbitmq.property.RabbitMQProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.util.StringUtils;

/**
 * 生成具有环境感知能力的队列、交换器和路由键名称。
 * 该类为RabbitMQ资源名称应用特定环境的前缀，
 * 从而在不更改应用程序代码的情况下实现环境隔离。
 */
public class QueueNameGenerator {

    private final RabbitMQProperties properties;
    private final Environment environment;

    @Autowired
    public QueueNameGenerator(RabbitMQProperties properties, Environment environment) {
        this.properties = properties;
        this.environment = environment;
    }

    /**
     * 使用适当的环境前缀生成队列名称（如果启用）。
     *
     * @param baseName 基础队列名称
     * @return 具有环境感知的队列名称
     */
    public String queueName(String baseName) {
        if (!properties.isEnableEnvironmentPrefix()) {
            return baseName;
        }

        return getPrefix() + baseName;
    }

    /**
     * 使用适当的环境前缀生成交换器名称（如果启用）。
     *
     * @param baseName 基础交换器名称
     * @return 具有环境感知的交换器名称
     */
    public String exchangeName(String baseName) {
        if (!properties.isEnableEnvironmentPrefix()) {
            return baseName;
        }

        return getPrefix() + baseName;
    }

    /**
     * 根据当前激活的配置文件获取要使用的环境前缀。
     * 如果在属性中配置了自定义前缀，则优先使用该前缀。
     *
     * @return 环境前缀
     */
    private String getPrefix() {
        // 如果在属性中设置了显式前缀，则使用它
        if (StringUtils.hasText(properties.getEnvironmentPrefix())) {
            return properties.getEnvironmentPrefix() + ".";
        }

        // 否则从激活的配置文件中确定前缀
        String[] activeProfiles = environment.getActiveProfiles();
        if (activeProfiles.length > 0) {
            // 使用第一个激活的配置文件作为前缀
            String profile = activeProfiles[0];

            // 生产环境不添加前缀
            if ("prod".equals(profile) || "online".equals(profile)) {
                return "";
            }

            return profile + ".";
        }

        // 默认无前缀
        return "";
    }

    /**
     * 判断队列名称是否已经包含环境前缀。
     *
     * @param queueName 要检查的队列名称
     * @return 如果队列名称已经包含环境前缀则返回true
     */
    public boolean hasEnvironmentPrefix(String queueName) {
        String prefix = getPrefix();
        if (prefix.isEmpty()) {
            return true; // 如果不需要前缀，则认为已经有前缀
        }

        return queueName.startsWith(prefix);
    }

    /**
     * 通过移除环境前缀（如果存在）来标准化队列名称。
     * 这对于获取队列的基础名称很有用。
     *
     * @param queueName 要标准化的队列名称
     * @return 没有环境前缀的标准化队列名称
     */
    public String normalizeQueueName(String queueName) {
        String prefix = getPrefix();
        if (prefix.isEmpty()) {
            return queueName;
        }

        if (queueName.startsWith(prefix)) {
            return queueName.substring(prefix.length());
        }

        return queueName;
    }
}
