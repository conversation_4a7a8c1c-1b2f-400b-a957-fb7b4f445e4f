package com.linzi.pitpat.framework.rabbitmq.listener;

import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 消息监听器基类
 * 提供通用的消息处理逻辑
 */
@Slf4j
public abstract class MessageListener {

    /**
     * 处理消息
     *
     * @param message     RabbitMQ消息
     * @param deliveryTag
     * @param channel
     */
    protected void parseMessage(Message message, long deliveryTag, Channel channel) {
        String payload = new String(message.getBody(), StandardCharsets.UTF_8);
        String messageId = message.getMessageProperties().getMessageId();

        try {
            log.info("收到消息: messageId={}, payload={}", messageId, payload);
            // 记录日志
            if (log.isDebugEnabled()) {
                log.debug("收到消息: messageId={}, payload={}", messageId, payload);
            }

            // 处理消息
            handleMessage(payload);

        } catch (Exception e) {
            // 处理异常
            handleException(messageId, payload, e);
        } finally {
            try {
                channel.basicAck(deliveryTag, true);
            } catch (IOException e) {
                log.error("消息确认异常,", e);
            }
        }
    }

    /**
     * 子类实现此方法处理具体消息
     *
     * @param message
     * @throws Exception 处理异常
     */
    protected abstract void handleMessage(String message) throws Exception;

    /**
     * 统一异常处理
     *
     * @param messageId 原始消息
     * @param payload   消息体
     * @param e         异常
     */
    protected void handleException(String messageId, String payload, Exception e) {
        log.error("处理消息异常，messageId={}, payload={}", messageId, payload, e);
    }
}
