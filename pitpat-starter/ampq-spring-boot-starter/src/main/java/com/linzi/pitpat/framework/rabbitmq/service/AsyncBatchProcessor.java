package com.linzi.pitpat.framework.rabbitmq.service;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

/**
 * 异步批量处理器
 * 支持懒加载初始化，第一次使用时启动处理线程
 *
 * @param <T> 处理的消息类型
 */
@Slf4j
public class AsyncBatchProcessor<T> {

    private final String processorName;
    private final int batchSize;
    private final long batchTimeWindowMs;
    private final Consumer<List<T>> batchProcessor;

    private final BlockingQueue<T> messageBuffer = new LinkedBlockingQueue<>();
    private final AtomicBoolean running = new AtomicBoolean(false);
    private final AtomicBoolean initialized = new AtomicBoolean(false);
    private volatile Thread processorThread;

    /**
     * 构造函数
     *
     * @param processorName     处理器名称，用于线程命名和日志
     * @param batchSize         批量处理大小
     * @param batchTimeWindowMs 批量处理时间窗口（毫秒）
     * @param batchProcessor    批量处理逻辑
     */
    public AsyncBatchProcessor(String processorName, int batchSize, long batchTimeWindowMs,
                               Consumer<List<T>> batchProcessor) {
        this.processorName = processorName;
        this.batchSize = batchSize;
        this.batchTimeWindowMs = batchTimeWindowMs;
        this.batchProcessor = batchProcessor;
    }

    /**
     * 添加消息到处理队列
     * 首次调用时会触发懒加载初始化
     *
     * @param message 要处理的消息
     * @return 是否成功添加到队列
     */
    public boolean offer(T message) {
        // 懒加载初始化
        ensureInitialized();

        try {
            boolean success = messageBuffer.offer(message);
            if (!success) {
                log.warn("[{}] 消息缓冲区已满，消息可能丢失", processorName);
            }
            return success;
        } catch (Exception e) {
            log.error("[{}] 添加消息到缓冲区失败", processorName, e);
            return false;
        }
    }

    /**
     * 懒加载初始化处理线程
     */
    private void ensureInitialized() {
        if (!initialized.get()) {
            synchronized (this) {
                if (!initialized.get()) {
                    startProcessor();
                    initialized.set(true);
                }
            }
        }
    }

    /**
     * 启动异步处理线程
     */
    private void startProcessor() {
        if (running.compareAndSet(false, true)) {
            processorThread = new Thread(this::asyncBatchProcess, processorName + "-AsyncProcessor");
            processorThread.setDaemon(true);
            processorThread.start();
            log.info("[{}] 异步批量处理线程已启动", processorName);
        }
    }

    /**
     * 异步批量处理主循环
     */
    private void asyncBatchProcess() {
        List<T> batch = new ArrayList<>(batchSize);

        while (running.get()) {
            try {
                // 阻塞获取第一个消息
                T firstMessage = messageBuffer.take();
                batch.add(firstMessage);

                // 在时间窗口内尽可能收集更多消息
                long endTime = System.currentTimeMillis() + batchTimeWindowMs;
                while (batch.size() < batchSize && System.currentTimeMillis() < endTime) {
                    T message = messageBuffer.poll(endTime - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
                    if (message != null) {
                        batch.add(message);
                    } else {
                        break; // 超时，停止收集
                    }
                }

                // 批量处理消息
                if (!batch.isEmpty()) {
                    try {
                        batchProcessor.accept(new ArrayList<>(batch));
                        log.debug("[{}] 批量处理消息成功，数量: {}", processorName, batch.size());
                    } catch (Exception e) {
                        log.error("[{}] 批量处理消息失败，数量: {}", processorName, batch.size(), e);
                        // 处理失败，消息已经从队列中取出，这里可以考虑重试或记录到死信队列
                    }
                    batch.clear();
                }

            } catch (InterruptedException e) {
                log.info("[{}] 异步处理线程被中断，准备退出", processorName);
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("[{}] 异步批量处理出现异常", processorName, e);
                // 继续运行，不因单次异常中断服务
            }
        }

        // 处理剩余消息
        if (!batch.isEmpty()) {
            try {
                batchProcessor.accept(batch);
                log.info("[{}] 处理剩余消息完成，数量: {}", processorName, batch.size());
            } catch (Exception e) {
                log.error("[{}] 处理剩余消息时出现异常", processorName, e);
            }
        }

        log.info("[{}] 异步批量处理线程已退出", processorName);
    }

    /**
     * 获取队列中剩余消息数量
     */
    public int getQueueSize() {
        return messageBuffer.size();
    }

    /**
     * 检查处理器是否已初始化
     */
    public boolean isInitialized() {
        return initialized.get();
    }

    /**
     * 检查处理器是否正在运行
     */
    public boolean isRunning() {
        return running.get();
    }

    /**
     * 优雅关闭处理器
     *
     * @param timeoutMs 等待超时时间（毫秒）
     */
    public void shutdown(long timeoutMs) {
        if (!initialized.get()) {
            log.debug("[{}] 处理器未初始化，跳过关闭操作", processorName);
            return;
        }

        log.info("[{}] 开始关闭异步批量处理器...", processorName);
        running.set(false);

        if (processorThread != null && processorThread.isAlive()) {
            // 中断线程并等待其结束
            processorThread.interrupt();
            try {
                processorThread.join(timeoutMs);
                if (processorThread.isAlive()) {
                    log.warn("[{}] 异步处理线程在 {}ms 内未能正常退出", processorName, timeoutMs);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("[{}] 等待异步处理线程退出时被中断", processorName);
            }
        }

        // 处理剩余的消息
        List<T> remainingMessages = new ArrayList<>();
        T msg;
        while ((msg = messageBuffer.poll()) != null) {
            remainingMessages.add(msg);
        }

        if (!remainingMessages.isEmpty()) {
            log.info("[{}] 处理剩余的 {} 条消息", processorName, remainingMessages.size());
            try {
                batchProcessor.accept(remainingMessages);
            } catch (Exception e) {
                log.error("[{}] 处理剩余消息时出现异常", processorName, e);
            }
        }

        log.info("[{}] 异步批量处理器已关闭", processorName);
    }

    /**
     * 强制刷新当前缓冲区中的所有消息
     */
    public void flush() {
        if (!initialized.get() || messageBuffer.isEmpty()) {
            return;
        }

        List<T> currentMessages = new ArrayList<>();
        messageBuffer.drainTo(currentMessages);

        if (!currentMessages.isEmpty()) {
            try {
                batchProcessor.accept(currentMessages);
                log.info("[{}] 强制刷新处理消息完成，数量: {}", processorName, currentMessages.size());
            } catch (Exception e) {
                log.error("[{}] 强制刷新处理消息失败", processorName, e);
                // 将消息放回队列
                for (int i = currentMessages.size() - 1; i >= 0; i--) {
                    messageBuffer.offer(currentMessages.get(i));
                }
            }
        }
    }
}
