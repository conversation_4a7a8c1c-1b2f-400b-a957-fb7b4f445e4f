package com.linzi.pitpat.framework.rabbitmq.handler;


import com.linzi.pitpat.framework.rabbitmq.listener.MessageEventListener;
import com.linzi.pitpat.framework.rabbitmq.model.MessageEvent;

/**
 * 消息处理器接口
 * 用于处理本地同步和异步消息
 * @param <T> 消息内容类型
 */
public interface MessageHandler<T> {
    /**
     * 用户反射时获取当前接口上有没有注解，是否要异步执行
     * @see MessageEventListener#parseMessage
     */
    String methodName = "handleMessage";

    /**
     * 获取处理器支持的消息类型
     *
     * @return 消息类型
     */
    String getSupportedEventType();

    /**
     * 处理消息
     *
     * @param event 消息事件
     */
    void handleMessage(MessageEvent<T> event);
}
