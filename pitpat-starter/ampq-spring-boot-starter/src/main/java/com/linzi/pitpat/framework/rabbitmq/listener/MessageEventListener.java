package com.linzi.pitpat.framework.rabbitmq.listener;

import com.linzi.pitpat.framework.rabbitmq.handler.MessageHandler;
import com.linzi.pitpat.framework.rabbitmq.model.MessageEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class MessageEventListener {

    private final List<MessageHandler<?>> messageHandlerList;

    @EventListener
    public void parseMessage(MessageEvent<?> event) {
        if (Objects.isNull(event)) {
            log.error("MessageEvent 消息不能为空");
            return;
        }
        try {
            Optional<MessageHandler<?>> optional = messageHandlerList.stream().filter(item -> item.getSupportedEventType().equals(event.getEventType())).findFirst();
            optional.ifPresent(handler -> {
                log.info("Message event={}", event);
                processMessage(handler, event);
            });
            if (optional.isEmpty()) {
                log.warn("未找到消息处理器: eventType={}", event.getEventType());
            }
        } catch (Exception e) {
            handleException(event, e);
        }
    }

    /**
     * 处理消息，解决泛型类型安全问题
     *
     * @param handler 消息处理器
     * @param event   消息事件
     */
    @SuppressWarnings("unchecked")
    private <T> void processMessage(MessageHandler<T> handler, MessageEvent<?> event) {
        handler.handleMessage((MessageEvent<T>) event);
    }

    /**
     * 统一异常处理
     *
     * @param event 错误消息
     * @param e     异常
     */
    protected void handleException(MessageEvent<?> event, Exception e) {
        log.error("[handleMessage] {}. messageId={}, message={}", event.getEventType(), event.getMessageId(), e.getMessage(), e);
    }
}
