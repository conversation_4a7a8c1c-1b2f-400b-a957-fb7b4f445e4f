package com.linzi.pitpat.framework.rabbitmq.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.framework.rabbitmq.model.DelayMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * 简化延迟消息Mapper接口
 */
@Mapper
public interface DelayMessageMapper extends BaseMapper<DelayMessage> {

    /**
     * 查询长期延迟消息（分页）
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param limit     每页大小
     * @param offset    偏移量
     * @return 消息列表
     */
    @DS("sharding")
    List<DelayMessage> selectLongTermMessages(@Param("startTime") ZonedDateTime startTime, @Param("endTime") ZonedDateTime endTime, @Param("limit") int limit, @Param("offset") int offset);

    /**
     * 更新消息状态为已发送
     *
     * @param scheduledTime
     * @param messageId     消息ID
     * @param status        新状态
     * @param sentTime      发送时间
     * @return 更新行数
     */
    @Update("UPDATE zns_delay_queue_message " + "SET status = #{status}, sent_time = #{sentTime} " + "WHERE message_id = #{messageId} and scheduled_time=#{scheduledTime}")
    @DS("sharding")
    int updateMessageStatus(@Param("scheduledTime") ZonedDateTime scheduledTime, @Param("messageId") String messageId, @Param("status") int status, @Param("sentTime") ZonedDateTime sentTime);

    /**
     * 直接拼接物理表，不走代理
     * 更新消息状态为已发送
     *
     * @param messageIdList 消息ID
     * @param tablePostfix
     * @param status        新状态
     * @param sentTime      发送时间
     * @return 更新行数
     */
    int batchUpdateMessageStatusById(@Param("tablePostfix") String tablePostfix, @Param("messageIdList") List<Long> messageIdList, @Param("status") int status, @Param("sentTime") ZonedDateTime sentTime);

    /**
     * 根据消息ID查询消息
     *
     * @param messageId 消息ID
     * @return 消息实体
     */
    @Select("SELECT * FROM zns_delay_queue_message WHERE message_id = #{messageId}")
    @DS("sharding")
    DelayMessage selectByMessageId(@Param("messageId") String messageId);


    /**
     * 批量插入延迟消息
     *
     * @param messages 消息列表
     * @return 插入行数
     */
    @DS("sharding")
    int batchInsert(@Param("list") List<DelayMessage> messages);

    @DS("sharding")
    int deleteSentMessagesBeforeDate(@Param("beforeDate") ZonedDateTime beforeDate, @Param("status") int status);

    /**
     * 单条更新消息状态和发送时间（按ID和分片键更新，但不更新分片键本身）
     *
     * @param id            消息ID
     * @param scheduledTime 计划发送时间（分片键）
     * @param status        状态
     * @param sentTime      发送时间
     * @return 更新行数
     */
    @DS("sharding")
    int updateStatusById(@Param("id") Long id, @Param("scheduledTime") ZonedDateTime scheduledTime, @Param("status") Integer status, @Param("sentTime") ZonedDateTime sentTime);


}
