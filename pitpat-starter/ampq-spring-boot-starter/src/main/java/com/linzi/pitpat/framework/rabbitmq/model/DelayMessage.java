package com.linzi.pitpat.framework.rabbitmq.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * 简化延迟消息实体类
 * 用于基于数据库的长期延迟消息存储
 */
@Data
@TableName("zns_delay_queue_message")
public class DelayMessage {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 消息唯一标识
     */
    private String messageId;

    /**
     * 消息类型
     */
    private String messageType;

    /**
     * 交换机名称
     */
    private String exchange;

    /**
     * 路由键
     */
    private String routingKey;

    private String batchId;

    /**
     * 消息内容（JSON格式）
     */
    private String payload;

    /**
     * 计划发送时间
     */
    private ZonedDateTime scheduledTime;

    /**
     * 格式化 scheduledTime 之后的显示
     */
    @TableField(exist = false)
    private String tablePostfix;

    /**
     * 消息状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private ZonedDateTime createdTime;

    /**
     * 实际发送时间
     */
    private ZonedDateTime sentTime;

    // 状态常量
    /**
     * 待发送
     */
    public static final int STATUS_PENDING = 0;

    /**
     * 已发送
     */
    public static final int STATUS_SENT = 1;

    /**
     * 已取消
     */
    public static final int STATUS_CANCELLED = 2;

    /**
     * 已转移到MQ延迟队列
     */
    public static final int STATUS_TRANSFERRED = 3;

    /**
     * 发送失败
     */
    public static final int STATUS_FAILED = 4;

    /**
     * 已移入死信
     */
    public static final int STATUS_DEAD_LETTER = 5;

    private Integer retryCount;
}
