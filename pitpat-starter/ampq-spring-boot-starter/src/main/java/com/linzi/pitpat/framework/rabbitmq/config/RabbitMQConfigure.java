package com.linzi.pitpat.framework.rabbitmq.config;

import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.rabbit.config.DirectRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.ConditionalRejectingErrorHandler;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.support.RetryTemplate;

@Configuration
public class RabbitMQConfigure {

    //@Bean
    //public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
    //    RabbitTemplate template = new RabbitTemplate(connectionFactory);
    //    return template;
    //}
    //
    //@Bean
    //public DirectRabbitListenerContainerFactory directRabbitListenerContainerFactory(
    //        ConnectionFactory connectionFactory, RabbitProperties properties) {
    //
    //    DirectRabbitListenerContainerFactory factory = new DirectRabbitListenerContainerFactory();
    //    factory.setConnectionFactory(connectionFactory);
    //
    //    // 从配置文件中读取
    //    factory.setConsumersPerQueue(properties.getListener().getDirect().getConsumersPerQueue());
    //    factory.setPrefetchCount(properties.getListener().getDirect().getPrefetch());
    //
    //    // 错误处理器
    //    factory.setErrorHandler(new ConditionalRejectingErrorHandler());
    //
    //    // 消息确认模式
    //    factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
    //
    //    // 重试策略
    //    factory.setRetryTemplate(new RetryTemplate());
    //
    //    return factory;
    //}
}
