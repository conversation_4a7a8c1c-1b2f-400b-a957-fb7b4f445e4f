package com.linzi.pitpat.framework.rabbitmq.service.impl;

import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.framework.rabbitmq.QueueNameGenerator;
import com.linzi.pitpat.framework.rabbitmq.model.MessageEvent;
import com.linzi.pitpat.framework.rabbitmq.model.MessageWrapper;
import com.linzi.pitpat.framework.rabbitmq.service.DelayedMessageService;
import com.linzi.pitpat.framework.rabbitmq.service.LongDelayMessageService;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 消息服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QueueMessageServiceImpl implements QueueMessageService {

    private final RabbitTemplate rabbitTemplate;
    private final ApplicationEventPublisher eventPublisher;
    private final QueueNameGenerator nameGenerator;
    private final DelayedMessageService delayedMessageService;
    private final LongDelayMessageService longDelayMessageService;

    @Override
    public <T> MessageWrapper<T> sendMessage(String exchange, String routingKey, T payload) {
        MessageWrapper<T> wrapper = null;
        try {
            // 包装消息
            wrapper = MessageWrapper.ofMessage(payload, exchange, routingKey);

            // 发送消息
            String json = JsonUtil.writeString(payload);
            String exchangeName = nameGenerator.exchangeName(exchange);

            // 记录日志
            if (log.isDebugEnabled()) {
                log.debug("发送消息: exchange={}, routingKey={}, messageId={}, messageType={}",
                        exchange, routingKey, wrapper.getMessageId(), wrapper.getMessageType());
            }
            Message message = MessageBuilder.withBody(json.getBytes()).setMessageId(wrapper.getMessageId()).build();
            rabbitTemplate.convertAndSend(exchangeName, routingKey, message);
        } catch (Exception e) {
            handleException("发送消息失败", e, exchange, routingKey, wrapper);
        }
        return wrapper;
    }

    @Override
    public <T> MessageWrapper<T> sendDelayMessage(String exchange, String routingKey, T payload, Long delayMillis) {
        MessageWrapper<T> wrapper = null;
        try {
            // 包装消息
            wrapper = MessageWrapper.ofMessage(payload, exchange, routingKey, delayMillis);

            // 记录日志
            if (log.isDebugEnabled()) {
                log.debug("发送延迟消息: exchange={}, routingKey={}, messageId={}, messageType={}, delay={}ms",
                        exchange, routingKey, wrapper.getMessageId(), wrapper.getMessageType(), delayMillis);
            }

            // 发送延迟消息
            String json = JsonUtil.writeString(payload);
            String exchangeName = nameGenerator.exchangeName(exchange);

            Message message = MessageBuilder.withBody(json.getBytes()).setMessageId(wrapper.getMessageId()).build();
            rabbitTemplate.convertAndSend(exchangeName, routingKey, message, msg -> {
                msg.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
                msg.getMessageProperties().setDelay(delayMillis.intValue());
                return msg;
            });
        } catch (Exception e) {
            handleException("发送延迟消息失败", e, exchange, routingKey, wrapper);
        }
        return wrapper;
    }

    @Override
    public <T> MessageWrapper<T> sendDelayMessageShort(String exchange, String routingKey, T message, Long delayMillis) {
        // 委托给DelayedMessageService处理
        String exchangeName = nameGenerator.exchangeName(exchange);
        return delayedMessageService.sendShortDelayMessage(exchangeName, routingKey, message, delayMillis);
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> MessageEvent<T> sendMessageEvent(String eventType, T message) {
        MessageEvent<T> event = null;
        try {
            // 创建事件
            event = MessageEvent.of(eventType, message);
            // 记录日志
            if (log.isDebugEnabled()) {
                log.debug("准备本地同步消息: event={}", event);
            }
            eventPublisher.publishEvent(event);
        } catch (Exception e) {
            handleException("处理本地同步消息失败", e, null, eventType, message);
        }

        return event;
    }

    @Override
    public <T> MessageWrapper<T> sendDelayMessageLong(String exchange, String routingKey, T message, Long delayMillis) {
        // 委托给SimpleDelayMessageService处理
        //String exchangeName = nameGenerator.exchangeName(exchange);
        return longDelayMessageService.sendLongDelayMessage(exchange, routingKey, message, delayMillis);
    }

    @Override
    public boolean cancelDelayMessage(String messageId) {
        // 委托给SimpleDelayMessageService处理
        return longDelayMessageService.cancelDelayMessage(messageId);
    }

    @Override
    public Map<String, Object> getDelayMessageStatus(String messageId) {
        // 委托给SimpleDelayMessageService处理
        //return longDelayMessageService.getDelayMessageStatus(messageId);
        return null;
    }

    /**
     * 统一异常处理
     *
     * @param message    错误消息
     * @param e          异常
     * @param exchange   交换机
     * @param routingKey 路由键
     * @param payload    消息内容
     */
    private void handleException(String message, Exception e, String exchange, String routingKey, Object payload) {
        log.error("{}. exchange={}, routingKey={}, payload={}",
                message, exchange, routingKey, payload, e);
    }
}
