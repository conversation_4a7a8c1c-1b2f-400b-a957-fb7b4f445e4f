package com.linzi.pitpat.framework.rabbitmq;

import com.linzi.pitpat.framework.rabbitmq.property.RabbitMQProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.core.ExchangeBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import javax.annotation.PostConstruct;

/**
 * RabbitMQ 自动配置类 - 支持多环境配置
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(RabbitMQProperties.class)
public class RabbitMQAutoConfiguration {

    private final Environment environment;
    private final RabbitMQProperties properties;
    private final ConfigurableListableBeanFactory beanFactory;
    private final QueueNameGenerator nameGenerator;

    /**
     * 批量创建队列配置
     */
    @PostConstruct
    public void initializeQueues() {
        properties.getQueues().forEach(this::createQueueBean);
        properties.getExchanges().forEach(this::createExchangeBean);
        properties.getBindings().forEach(this::createBindingBean);
    }

    /**
     * 动态创建队列Bean
     */
    private void createQueueBean(RabbitMQProperties.QueueProperties queueConfig) {
        String queueName = nameGenerator.queueName(queueConfig.getName());
        QueueBuilder builder = QueueBuilder.durable(queueName);

        // 设置死信交换器
        if (queueConfig.getDeadLetterExchange() != null) {
            String exchangeName = nameGenerator.exchangeName(queueConfig.getDeadLetterExchange());
            builder.withArgument("x-dead-letter-exchange", exchangeName);
            builder.withArgument("x-dead-letter-routing-key", queueConfig.getDeadLetterRoutingKey());
        }

        // 设置TTL
        if (queueConfig.getTtl() > 0) {
            builder.withArgument("x-message-ttl", queueConfig.getTtl());
        }

        // 设置最大长度
        if (queueConfig.getMaxLength() > 0) {
            builder.withArgument("x-max-length", queueConfig.getMaxLength());
        }

        Queue queue = builder.build();
        // 注册到Spring容器
        log.info("Auto create Queue={}", queueName);
        beanFactory.registerSingleton(getQueueBeanName(queueConfig.getName()), queue);
    }

    /**
     * 动态创建交换器Bean
     */
    private void createExchangeBean(RabbitMQProperties.ExchangeProperties exchangeConfig) {
        String exchangeName = nameGenerator.queueName(exchangeConfig.getName());
        ExchangeBuilder builder = switch (exchangeConfig.getType().toLowerCase()) {
            case "topic" -> ExchangeBuilder.topicExchange(exchangeName)
                    .durable(exchangeConfig.isDurable());
            case "direct" -> ExchangeBuilder.directExchange(exchangeName)
                    .durable(exchangeConfig.isDurable());
            case "fanout" -> ExchangeBuilder.fanoutExchange(exchangeName)
                    .durable(exchangeConfig.isDurable());
            case "headers" -> ExchangeBuilder.headersExchange(exchangeName)
                    .durable(exchangeConfig.isDurable());
            default -> throw new IllegalArgumentException("不支持的交换器类型: " + exchangeConfig.getType());
        };
        // 是否延迟队列
        if (exchangeConfig.isDelayed()) {
            builder.delayed();
        }
        Exchange exchange = builder.build();
        log.info("Auto create Exchange={}, delayed={}", exchangeName, exchangeConfig.isDelayed());
        beanFactory.registerSingleton(getExchangeBeanName(exchangeConfig.getName()), exchange);
    }


    /**
     * 动态创建绑定Bean
     */
    private void createBindingBean(RabbitMQProperties.BindingProperties bindingConfig) {
        Queue queue = (Queue) beanFactory.getSingleton(getQueueBeanName(bindingConfig.getQueue()));
        Exchange exchange = (Exchange) beanFactory.getSingleton(getExchangeBeanName(bindingConfig.getExchange()));

        if (queue != null && exchange != null) {
            Binding binding = BindingBuilder.bind(queue).to(exchange)
                    .with(bindingConfig.getRoutingKey()).noargs();
            log.info("Auto bind queue={} with exchange={}", bindingConfig.getQueue(), binding.getExchange());
            beanFactory.registerSingleton(getBindingBeanName(bindingConfig.getQueue()), binding);
        }
    }

    private String getQueueBeanName(String name) {
        return name + "Queue";
    }

    private String getExchangeBeanName(String name) {
        return name + "Exchange";

    }

    private String getBindingBeanName(String name) {
        return name + "Binding";
    }

}
