package com.linzi.pitpat.framework.rabbitmq.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linzi.pitpat.framework.rabbitmq.model.DeadLetterMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.ZonedDateTime;

/**
 * 死信消息Mapper接口
 */
@Mapper
public interface DeadLetterMessageMapper extends BaseMapper<DeadLetterMessage> {

    /**
     * 插入死信消息
     *
     * @param deadLetter 死信消息
     * @return 影响行数
     */
    int insert(DeadLetterMessage deadLetter);

    /**
     * 统计最近一段时间内的死信消息数量
     *
     * @param startTime 开始时间
     * @return 死信消息数量
     */
    @Select("SELECT COUNT(*) FROM zns_delay_queue_dead_letter WHERE dead_time >= #{startTime}")
    int countRecentDeadLetters(@Param("startTime") ZonedDateTime startTime);
} 