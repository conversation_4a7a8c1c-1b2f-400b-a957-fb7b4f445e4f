/*
 *    Copyright (c) 2018-2025, song<PERSON>yuan All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the 霖梓控股 developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: songfayuan (<EMAIL>)
 */

package com.linzi.pitpat.framework.db.interceptor;


import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.google.common.hash.Hashing;
import com.linzi.pitpat.core.util.DingTalkUtils;
import com.linzi.pitpat.core.util.EnvUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.dto.DingTalkRequestDto;
import com.linzi.pitpat.framework.db.config.SqlOutOfTimeConfig;
import com.linzi.pitpat.framework.db.dto.ExceptionInfoDto;
import com.linzi.pitpat.framework.db.dto.GitRespDto;
import com.linzi.pitpat.framework.db.dto.ScopeDataDto;
import com.linzi.pitpat.framework.db.util.ReflectionUtils;
import com.linzi.pitpat.framework.db.util.StackTraceUtil;
import com.lz.mybatis.plugins.interceptor.baomidou.SqlParserHandler;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.ibatis.binding.BindingException;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ResultHandler;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Properties;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2018/1/19
 * 数据权限插件，guns
 */
@Slf4j
@Intercepts({@Signature(type = StatementHandler.class, method = "query", args = {Statement.class, ResultHandler.class})})
public class SqlSelectLogInterceptor extends SqlParserHandler implements Interceptor {

    @Value("${spring.profiles.active}")
    private String profile;

    @Value("${server.port}")
    private String port;

    @Value("${spring.application.name}")
    private String applicationName;

    @Resource
    private RedissonClient redissonClient;

    @Resource(name = "dbSlowSqlExecutor")
    public ThreadPoolTaskExecutor dbSlowSqlExecutor;

    public  static  String host = "localhost";
    static {
        try {
            InetAddress inetAddress = InetAddress.getLocalHost();
            host = inetAddress.getHostAddress();
        } catch (UnknownHostException e) {
            host = "127.0.0.1";
        }
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        StatementHandler statementHandler = PluginUtils.realTarget(invocation.getTarget());
        MetaObject metaObject = SystemMetaObject.forObject(statementHandler);
        MappedStatement mappedStatement = (MappedStatement) metaObject.getValue("delegate.mappedStatement");
        //  语句的sql  在其他地方
        if (!SqlCommandType.SELECT.equals(mappedStatement.getSqlCommandType())) {
            return invocation.proceed();
        }
        this.sqlParser(metaObject);
        // 先判断是不是SELECT操作
        BoundSql boundSql = (BoundSql) metaObject.getValue("delegate.boundSql");
        String originalSql = boundSql.getSql();
        Object parameterObject = boundSql.getParameterObject();
        String originMapeerId = mappedStatement.getId();
        String mapperdId = getMapperId(mappedStatement);
        Throwable throwable = new Throwable();
        String sqlCommandTypePre = mapperdId + " ";
        String peek0 = StackTraceUtil.peek();
        String callStackTrace = StackTraceUtil.getCallStackTrace();
        if (StringUtils.hasText(peek0)) {
            StackTraceElement[] stackTraceElements = throwable.getStackTrace();
            String[] classInfos = peek0.split(":");
            int i = 0;
            for (StackTraceElement stackTraceElement : stackTraceElements) {
                i++;
                if (stackTraceElement.getClassName().equals(classInfos[0]) && stackTraceElement.getMethodName().equals(classInfos[1])) {
                    sqlCommandTypePre = callStackTrace + ":" + stackTraceElement.getLineNumber() + ":" + mapperdId + " ";
                    break;
                }
                if (i > 100) {
                    break;
                }
            }
        }

        Configuration configuration = mappedStatement.getConfiguration();
        //查找参数中包含DataScope类型的参数
        ScopeDataDto dataScope = findDataScopeObject(parameterObject);
        long startTime = System.currentTimeMillis();
        if (dataScope == null) {
            metaObject.setValue("delegate.boundSql.sql", originalSql);
            Object result = invocation.proceed();
            long endTime = System.currentTimeMillis();
            long runTime = endTime - startTime;
            String showSql = SqlUpdateLogInterceptor.showSql(configuration, boundSql);
            // https://oapi.dingtalk.com/robot/send?access_token=f0b7999a0198aedf3cf96c5d23e8395f4f82618162dabc83667e40aa12a65dd4
            log.info(sqlCommandTypePre + " sqlexe=" + runTime + " " + showSql);
            if (runTime >= SqlOutOfTimeConfig.default_bear_second) {
                String key = "slow_sql_md5_" + sha256(showSql);
                RBucket<Object> bucket = redissonClient.getBucket(key);
                Object object = bucket.get();
                if (object == null) {
                    bucket.set("1", 30 * 60l, TimeUnit.SECONDS);                       // 如果慢sql  ，相同的SQL 30 分钟报一次吧。
                    // 如果zns_slow_sql_config表中配置了mapperId，并且容忍时间大于执行时间，则不提示
                    if (SqlOutOfTimeConfig.slowSqlConfigMap.containsKey(mapperdId) && SqlOutOfTimeConfig.slowSqlConfigMap.get(mapperdId) >= runTime) {
                        return result;
                    }

                    if (!EnvUtils.isOnline(profile)) {
                        log.info("mapperdId = " + mapperdId + ", containsKey = " + SqlOutOfTimeConfig.slowSqlConfigMap.containsKey(mapperdId)
                                + ", cacheTime = " + SqlOutOfTimeConfig.slowSqlConfigMap.get(mapperdId) + ", runTime = " + runTime);
                    }

                    //生产环境才上报慢日志, 慢日志以aws控制台为准，等 控制台的慢sql降到一个比较低的时间内，在打开该日志
                    if (EnvUtils.isOnline(profile)) {
                        reportDingTalkSlowSql(mapperdId, runTime, showSql, originMapeerId);
                    }

                }
            }
            return result;
        } else {
            String scopeName = dataScope.getScopeName();
            List<Integer> deptIds = dataScope.getDeptIds();
            if (StringUtils.hasText(scopeName) && deptIds != null && !deptIds.isEmpty()) {
                String join = "";
                originalSql = "select * from (" + originalSql + ") temp_data_scope where temp_data_scope." + scopeName + " in (" + join + ")";
                metaObject.setValue("delegate.boundSql.sql", originalSql);
            }

            String sql = SqlUpdateLogInterceptor.showSql(configuration, boundSql);
            log.info(sqlCommandTypePre + sql);
            return invocation.proceed();
        }
    }

    private void reportDingTalkSlowSql(String mapperdId, long runTime, String showSql, String originMapeerId) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        String currentDate = simpleDateFormat.format(new Date());
        Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
        dbSlowSqlExecutor.execute(new Runnable() {
            @Override
            public void run() {
                Optional.ofNullable(copyOfContextMap).ifPresent(MDC::setContextMap);
                try {
                    String text = "【注意】慢SQL时间 " + currentDate + " \n" +
                            "logNo : " + MDC.get("traceId") + "\n" +
                            "环境 : " + profile + "，服务器IP : " + host + "，port=" + port + "，application=" + applicationName + "\n" +
                            "MapperId : " + mapperdId + "\n" +
                            "执行时间 : " + runTime + "\n" +
                            "SQL : " + showSql + " \n" +
                            "如果你不想在群里报消息提醒，拿着【MapperId】配置到 zns_slow_sql_config 表，最好配置容忍时间!!! ";
                    String mobile = "";
                    ExceptionInfoDto exceptionInfo = getExceptionInfos(originMapeerId);
                    String body = JsonUtil.writeString(exceptionInfo);
                    //String result = HttpUtil.doPost("https://tkjdeploy.yijiesudai.com/api/jenkins/getGitPhone", body, 30000);
                    String response = post("https://tkjdeploy.yijiesudai.com/api/jenkins/getGitPhone", body);
                    log.info("sql get git info " + response);
                    if (StringUtils.hasText(response)) {
                        GitRespDto gitResp = JsonUtil.readValue(response, GitRespDto.class);
                        if (gitResp != null && Objects.equals(gitResp.getCode(), 200)) {
                            mobile = gitResp.getGitPhone();
                        }
                    }
                    DingTalkUtils.sendMsg(DingTalkRequestDto.of("f0b7999a0198aedf3cf96c5d23e8395f4f82618162dabc83667e40aa12a65dd4", null, text, mobile));
                } catch (Exception e) {
                    log.info("发送异常钉钉消息异常, msg={}", e.getMessage(), e);
                }
            }
        });
    }

    public static String sha256(String raw) {
        return Hashing.sha256().hashString(raw, StandardCharsets.UTF_8).toString();
    }

    public static ExceptionInfoDto getExceptionInfos(String mapperId) {
        ExceptionInfoDto exceptionInfo = new ExceptionInfoDto();
        String[] methods = mapperId.split("\\.");
        StringBuilder packageName = new StringBuilder();
        for (int j = 0; j < methods.length - 2; j++) {
            packageName.append(methods[j]);
            if (j < methods.length - 3) {
                packageName.append(".");
            }
        }
        String className = methods[methods.length - 2];
        String methodName = methods[methods.length - 1];
        exceptionInfo.setPackageName(packageName.toString());
        exceptionInfo.setClassName(className);
        exceptionInfo.setMethodName(methodName);
        return exceptionInfo;
    }


    @SuppressWarnings("unused")
    private static Object getParameterValue(String propertyName, Object obj) {

        Object value = null;

        try {
            value = ReflectionUtils.getObjectFieldValue(obj, propertyName);
        } catch (BindingException e2) {
            return null;
        }
        return value;
    }

    /**
     * 生成拦截对象的代理
     *
     * @param target 目标对象
     * @return 代理对象
     */
    @Override
    public Object plugin(Object target) {
        if (target instanceof StatementHandler) {
            return Plugin.wrap(target, this);
        }
        return target;
    }

    /**
     * mybatis配置的属性
     *
     * @param properties mybatis配置的属性
     */
    @Override
    public void setProperties(Properties properties) {

    }

    public static String getMapperId(MappedStatement mappedStatement) {
        try {
            String id = mappedStatement.getId();
            if (id.contains(".")) {
                String[] ids = id.split("\\.");
                return ids[ids.length - 2] + "." + ids[ids.length - 1];
            }
            return null;
        } catch (Exception e) {
            log.info("error when getMapperId", e);
        }
        return "";
    }

    /**
     * 查找参数是否包括DataScope对象
     *
     * @param parameterObj 参数列表
     * @return DataScope
     */
    private ScopeDataDto findDataScopeObject(Object parameterObj) {
        if (parameterObj instanceof ScopeDataDto) {
            return (ScopeDataDto) parameterObj;
        } else if (parameterObj instanceof Map) {
            for (Object val : ((Map<?, ?>) parameterObj).values()) {
                if (val instanceof ScopeDataDto) {
                    return (ScopeDataDto) val;
                }
            }
        }
        return null;
    }


    public static String post(String url, String bodyStr) {
        OkHttpClient client = new OkHttpClient();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, bodyStr);
        Request request = new Request.Builder()
                .url(url)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        Response response;
        String responseStr = "";
        int code;
        try {
            response = client.newCall(request).execute();
            responseStr = response.body().string();
            code = response.code();
        } catch (IOException e) {
            log.error("SqlSelectLogInterceptor#post--发送钉钉消息异常：", e);
            return e.getMessage();
        }
        if (code == 200) {
            return null;
        }
        return responseStr;
    }


}
