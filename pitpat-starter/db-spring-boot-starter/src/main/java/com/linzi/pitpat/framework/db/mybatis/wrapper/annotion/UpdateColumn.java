package com.linzi.pitpat.framework.db.mybatis.wrapper.annotion;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标记需要更新的字段的注解
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface UpdateColumn {
    /**
     * 是否强制更新(包括null值)
     */
    boolean forceUpdate() default false;
}
