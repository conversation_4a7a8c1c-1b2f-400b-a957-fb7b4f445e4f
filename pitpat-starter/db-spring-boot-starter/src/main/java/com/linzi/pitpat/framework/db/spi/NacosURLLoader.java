package com.linzi.pitpat.framework.db.spi;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.url.spi.ShardingSphereURLLoader;

import java.util.Properties;

/**
 * Nacos URL loader.
 */
@Slf4j
public final class NacosURLLoader implements ShardingSphereURLLoader{

    @SneakyThrows
    @Override
    public String load(final String configurationSubject, final Properties queryProps) {
        String dsConfig = NacosConfigMangerAware.getConfigManager().getConfigService()
                .getConfig(configurationSubject, queryProps.getProperty("group"), 3000);
        String ruleConfigFile = queryProps.getProperty("ruleConfig");
        if (!StringUtils.isBlank(ruleConfigFile)) {
            log.info("[sharding][初始化] 存在规则文件，开始获取拼接。ruleConfigFile:{}",ruleConfigFile);
            String rulConfig = NacosConfigMangerAware.getConfigManager().getConfigService()
                    .getConfig(ruleConfigFile, queryProps.getProperty("group"), 3000);
            log.info("[sharding][初始化] ruleConfig:{}", rulConfig);
            if (!StringUtils.isBlank(rulConfig)) {
                dsConfig = dsConfig + "\n" + rulConfig;
            }
        }
        return dsConfig;
    }

    @Override
    public Object getType() {
        return "nacos:";
    }
}
