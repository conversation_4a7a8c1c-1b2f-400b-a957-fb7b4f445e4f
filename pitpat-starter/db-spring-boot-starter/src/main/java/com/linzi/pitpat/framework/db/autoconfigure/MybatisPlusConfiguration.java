package com.linzi.pitpat.framework.db.autoconfigure;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.linzi.pitpat.framework.db.dto.MyBatisBaomidouServiceImpl;
import com.linzi.pitpat.framework.db.interceptor.SQLInsertBatchLogInterceptor;
import com.linzi.pitpat.framework.db.interceptor.SQLInsertLogInterceptor;
import com.linzi.pitpat.framework.db.interceptor.SqlSelectLogInterceptor;
import com.linzi.pitpat.framework.db.interceptor.SqlUpdateLogInterceptor;
import com.lz.mybatis.plugin.config.ResolverBeanPostProcessor;
import com.lz.mybatis.plugins.interceptor.DataEncryptScopeInterceptor;
import com.lz.mybatis.plugins.interceptor.DataPrepareEncryptScopeInterceptor;
import com.lz.mybatis.plugins.interceptor.EncryptTableConfig;
import com.lz.mybatis.plugins.interceptor.MapF2FInterceptor;
import com.lz.mybatis.plugins.interceptor.QueryDecryptScopeInterceptor;
import com.lz.mybatis.plugins.interceptor.RestoreDataScopeInterceptor;
import com.lz.mybatis.plugins.interceptor.RestoreQueryDataScopeInterceptor;
import com.lz.mybatis.plugins.interceptor.utils.PSpringContextUtils;
import com.lz.mybatis.plugins.interceptor.utils.t.PPTuple;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2020/12/12 10:36
 */
@Configuration
@Slf4j
public class MybatisPlusConfiguration {

    public MybatisPlusConfiguration() {
    }
    @PostConstruct
    public void postConstruct() {
        log.info("开始初始化表数据");
        EncryptTableConfig.tableConfig.put("zns_shopify_user", new PPTuple(List.of("email_address_en"),
                List.of()));
        EncryptTableConfig.tableConfig.put("zns_user", new PPTuple(List.of("email_address_en"),
                List.of()));
        EncryptTableConfig.tableConfig.put("zns_vip_user", new PPTuple(List.of("email_address_en"),
                List.of()));
        EncryptTableConfig.tableConfig.put("zns_user_email_sending_record", new PPTuple(List.of("email_address_en"),
                List.of()));
        EncryptTableConfig.tableConfig.put("zns_user_register_fail_record", new PPTuple(List.of("email_address_en"),
                List.of()));
        EncryptTableConfig.tableConfig.put("zns_reach_push_email", new PPTuple(List.of("email_address_en"),
                List.of()));
    }
    @Bean
    @ConditionalOnMissingBean
    public MybatisPlusInterceptor paginationInterceptor() {
        MybatisPlusInterceptor paginationInterceptor = new MybatisPlusInterceptor();
        paginationInterceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return paginationInterceptor;
    }


    //更新数据恢复
    @Bean("restoreDataScopeInterceptor")
    public RestoreDataScopeInterceptor restoreDataScopeInterceptor() {
        return new RestoreDataScopeInterceptor();
    }

    //查询数据恢复
    @Bean("restoreQueryDataScopeInterceptor")
    @DependsOn("restoreDataScopeInterceptor")
    public RestoreQueryDataScopeInterceptor restoreQueryDataScopeInterceptor() {
        return new RestoreQueryDataScopeInterceptor();
    }


    //结果集数据解密
    @Bean("queryDecryptScopeInterceptor")
    @DependsOn("restoreQueryDataScopeInterceptor")
    public QueryDecryptScopeInterceptor queryDecryptScopeInterceptor() {
        return new QueryDecryptScopeInterceptor();
    }


    @Bean("mapF2FInterceptor")
    public MapF2FInterceptor mapF2FInterceptor() {
        return new MapF2FInterceptor();
    }

    //打印日志
    @Bean("sqlLogInterceptor")
    public SqlUpdateLogInterceptor sqlLogInterceptor() {
        return new SqlUpdateLogInterceptor();
    }

    //打印日志
    @Bean("sQLInsertLogInterceptor")
    public SQLInsertLogInterceptor sQLInsertLogInterceptor() {
        return new SQLInsertLogInterceptor();
    }

    //打印日志
    @Bean
    public SQLInsertBatchLogInterceptor sqlBatchInsertResultLogInterceptor() {
        return new SQLInsertBatchLogInterceptor();
    }

    //打印日志
    @Bean("sqlSelectLogInterceptor")
    public SqlSelectLogInterceptor sqlSelectLogInterceptor() {
        return new SqlSelectLogInterceptor();
    }

    //数据加密
    @Bean("dataEncryptScopeInterceptor")
    @DependsOn("sqlLogInterceptor")
    public DataEncryptScopeInterceptor dataEncryptScopeInterceptor() {
        return new DataEncryptScopeInterceptor();
    }

    /**
     * 数据加密
     */
    @Bean("dataPrepareEncryptScopeInterceptor")
    public DataPrepareEncryptScopeInterceptor dataPrepareEncryptScopeInterceptor() {
        return new DataPrepareEncryptScopeInterceptor();
    }

    /**
     * 解析自定义sql，除了api、admin、queue、job ，其他服务无需自动装配
     */
    @Bean
    public ResolverBeanPostProcessor resolverBeanPostProcessor() {
        return new ResolverBeanPostProcessor(new MyBatisBaomidouServiceImpl());
    }

    /**
     * 定义线程池，发送钉钉消息时候用
     */
    @Bean("dbSlowSqlExecutor")
    public ThreadPoolTaskExecutor dbSlowSqlExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("dbSlowSqlExecutor-");
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors() + 1);
        executor.setQueueCapacity(1000);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }


    @Lazy(value = false)
    @Bean
    public PSpringContextUtils pSpringContextUtils() {
        return new PSpringContextUtils();
    }

}

