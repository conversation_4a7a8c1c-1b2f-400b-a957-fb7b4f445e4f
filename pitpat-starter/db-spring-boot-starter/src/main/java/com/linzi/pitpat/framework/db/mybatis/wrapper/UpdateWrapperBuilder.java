package com.linzi.pitpat.framework.db.mybatis.wrapper;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.base.CaseFormat;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.db.mybatis.wrapper.annotion.UpdateColumn;
import com.linzi.pitpat.lang.BaseDo;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class UpdateWrapperBuilder {
    // 缓存类的注解信息
    private static final Map<Class<?>, List<Field>> UPDATABLE_FIELDS_CACHE = new ConcurrentHashMap<>();

    /**
     * 构建动态更新条件
     *
     * @param entity 实体对象
     * @return 更新条件包装器
     */
    public static <T extends BaseDo> UpdateWrapper<T> build(T entity) {
        Class<?> entityClass = entity.getClass();
        UpdateWrapper<T> updateWrapper = new UpdateWrapper<>();

        // 获取需要更新的字段
        List<Field> fields = getUpdateFields(entityClass);

        for (Field field : fields) {
            try {
                field.setAccessible(true);
                Object value = field.get(entity);

                String fieldName = field.getName();
                if ("serialVersionUID".equals(fieldName) || "id".equals(fieldName)) {
                    continue;
                }
                UpdateColumn annotation = field.getAnnotation(UpdateColumn.class);

                // 根据注解配置决定是否更新
                if (Objects.nonNull(annotation) || value != null) {
                    if (Objects.nonNull(annotation) && Objects.isNull(value)) {
                       if(log.isDebugEnabled()) {
                           log.debug("触发强制更新, field={}", field);
                       }
                    }
                    // 使用lambda表达式构建更新条件
                    updateWrapper.set(getEntityFieldFunction(field.getName()), value);
                }
            } catch (Exception e) {
                log.error("构建更新条件失败,字段={}", field.getName(), e);
                throw new BaseException("构建更新条件失败", e);
            }
        }
        //fill condition
        if (Objects.isNull(entity.getId()) || entity.getId() <= 0) {
            throw new BaseException("构建更新条件失败, id 为空或不合法， id=" + entity.getId());
        }
        updateWrapper.eq("id", entity.getId());
        //fill modified time
        updateWrapper.set("gmt_modified", ZonedDateTime.now());

        if(log.isDebugEnabled()) {
            log.debug("getTargetSql={}", updateWrapper.getTargetSql());
            log.debug("getSqlSet={}",  updateWrapper.getSqlSet());
        }
        return updateWrapper;
    }

    /**
     * 获取类中标记为需要更新的字段
     */
    private static List<Field> getUpdateFields(Class<?> clazz) {
        return UPDATABLE_FIELDS_CACHE.computeIfAbsent(clazz, k -> {
            List<Field> fields = new ArrayList<>();
            Class<?> currentClass = clazz;

            // 处理继承关系
            while (currentClass != null && currentClass != Object.class) {
                //if (field.isAnnotationPresent(UpdateColumn.class)) {
                //}
                fields.addAll(Arrays.asList(currentClass.getDeclaredFields()));
                currentClass = currentClass.getSuperclass();
            }

            return fields;
        });
    }


    /**
     * 获取实体类对应的字段Function
     * 需要实体类有对应的getter方法
     */
    private static String getEntityFieldFunction(String fieldName) {
        return CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, fieldName);
    }
}
