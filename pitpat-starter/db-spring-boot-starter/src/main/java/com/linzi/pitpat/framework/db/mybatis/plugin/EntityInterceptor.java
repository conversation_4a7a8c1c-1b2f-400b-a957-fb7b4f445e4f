package com.linzi.pitpat.framework.db.mybatis.plugin;

import com.linzi.pitpat.lang.BaseDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Intercepts({@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})})
public class EntityInterceptor implements Interceptor {
    private final ThreadLocal<ZonedDateTime> zonedDateTimeLocal = ThreadLocal.withInitial(ZonedDateTime::now);
    private final ThreadLocal<Date> dateLocal = ThreadLocal.withInitial(Date::new);

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        Object object = invocation.getArgs()[1];

        try {
            //Mybatis plus
            if (object instanceof Map<?, ?> parameter) {
                Object entity = parameter.getOrDefault("param1", null);
                if (entity == null) {
                    entity = parameter.getOrDefault("et", null);
                }
                if(entity instanceof Collection<?>){
                    if(log.isDebugEnabled()){
                        log.debug("sql 不符合规范，忽略填充。 id={}", mappedStatement.getId());
                    }
                    return invocation.proceed();
                }
                //仅处理 继承 BaseDo的类
                if (Objects.nonNull(entity)) {
                    MetaObject metaObject = SystemMetaObject.forObject(entity);
                    if (entity instanceof BaseDo) {
                        if (isUpdate(mappedStatement)) {
                            setFieldValue(metaObject, "gmtModified");
                        }
                        if (isInsert(mappedStatement)) {
                            setFieldValue(metaObject, "gmtCreate");
                            setFieldValue(metaObject, "gmtModified");
                        }
                    } else {
                        if (isUpdate(mappedStatement)) {
                            setDateFieldValue(metaObject, "gmtModified");
                        }
                        if (isInsert(mappedStatement)) {
                            setDateFieldValue(metaObject, "gmtCreate");
                            setDateFieldValue(metaObject, "gmtModified");
                        }
                    }
                }
            }else {
                //Mybatis insert
                if (isInsert(mappedStatement)) {
                    if (Objects.nonNull(object)) {
                        MetaObject metaObject = SystemMetaObject.forObject(object);
                        if (object instanceof BaseDo) {
                            setFieldValue(metaObject, "gmtCreate");
                            setFieldValue(metaObject, "gmtModified");
                        } else {
                            setDateFieldValue(metaObject, "gmtCreate");
                            setDateFieldValue(metaObject, "gmtModified");
                        }
                    }
                }
                //Mybatis update
                if (isUpdate(mappedStatement)) {
                    MetaObject metaObject = SystemMetaObject.forObject(object);
                    if (object instanceof BaseDo) {
                        setFieldValue(metaObject, "gmtModified");
                    } else {
                        setDateFieldValue(metaObject, "gmtModified");
                    }
                }
            }
        } catch (Exception e) {
            log.info("自动填充日期报错，msg={}", e.getMessage(), e);
        } finally {
            clearThreadLocal();
        }
        return invocation.proceed();
    }

    /**
     * //清理线程
     */
    private void clearThreadLocal() {
        dateLocal.remove();
        zonedDateTimeLocal.remove();
    }

    private boolean isUpdate(MappedStatement mappedStatement) {
        return SqlCommandType.UPDATE.equals(mappedStatement.getSqlCommandType());
    }

    private boolean isInsert(MappedStatement mappedStatement) {
        return SqlCommandType.INSERT.equals(mappedStatement.getSqlCommandType());
    }


    private void setFieldValue(MetaObject metaObject, String field) {
        if (metaObject.hasSetter(field)
                && (Objects.equals("gmtModified", field) || Objects.isNull(metaObject.getValue(field)))) {
            metaObject.setValue(field, zonedDateTimeLocal.get());
        }
    }

    /**
     * 兼容旧的 Date 类型
     *
     * @param metaObject
     * @param field
     */
    private void setDateFieldValue(MetaObject metaObject, String field) {
        if (metaObject.hasSetter(field)
                && (Objects.equals("gmtModified", field) || Objects.isNull(metaObject.getValue(field)))) {
            metaObject.setValue(field, dateLocal.get());
        }
    }


    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

}
