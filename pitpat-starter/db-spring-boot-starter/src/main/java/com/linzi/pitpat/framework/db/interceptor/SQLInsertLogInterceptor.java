/*
 *    Copyright (c) 2018-2025, song<PERSON>yuan All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the 霖梓控股 developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: songfayuan (<EMAIL>)
 */

package com.linzi.pitpat.framework.db.interceptor;

import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.linzi.pitpat.framework.db.util.StackTraceUtil;
import com.lz.mybatis.plugins.interceptor.baomidou.SqlParserHandler;
import com.lz.mybatis.plugins.interceptor.utils.PSqlParseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.apache.ibatis.session.Configuration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;

import java.sql.Statement;
import java.util.Properties;

/**
 * 单条数据插入拦截器
 *
 * <AUTHOR>
 * @date 2018/1/19
 * 数据权限插件，guns
 */
@Slf4j
@Intercepts({@Signature(type = StatementHandler.class, method = "update", args = {Statement.class})})
public class SQLInsertLogInterceptor extends SqlParserHandler implements Interceptor {
    @Value("${spring.profiles.active}")
    private String profile;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        StatementHandler statementHandler = PluginUtils.realTarget(invocation.getTarget());
        MetaObject metaObject = SystemMetaObject.forObject(statementHandler);
        MappedStatement mappedStatement = (MappedStatement) metaObject.getValue("delegate.mappedStatement");
        if (!SqlCommandType.INSERT.equals(mappedStatement.getSqlCommandType())) {
            return invocation.proceed();
        }
        this.sqlParser(metaObject);
        // 先判断是不是SELECT操作
        BoundSql boundSql = (BoundSql) metaObject.getValue("delegate.boundSql");
        String mapperdId = PSqlParseUtil.getMapperId(mappedStatement);
        Configuration configuration = mappedStatement.getConfiguration();
        Object result = invocation.proceed();
        Object parameterObject = boundSql.getParameterObject();
        Throwable throwable = new Throwable();
        String sqlCommandTypePre = mapperdId + " ";
        String peek0 = StackTraceUtil.peek();
        String callStackTrace = StackTraceUtil.getCallStackTrace();
        if (StringUtils.hasText(peek0)) {
            StackTraceElement[] stackTraceElements = throwable.getStackTrace();
            String classInfos[] = peek0.split(":");
            int i = 0;
            for (StackTraceElement stackTraceElement : stackTraceElements) {
                i++;
                if (stackTraceElement.getClassName().equals(classInfos[0]) && stackTraceElement.getMethodName().equals(classInfos[1])) {
                    sqlCommandTypePre = callStackTrace + ":" + stackTraceElement.getLineNumber() + ":" + mapperdId + " ";
                    break;
                }
                if (i > 100) {
                    break;
                }
            }
        }

        sqlCommandTypePre = sqlCommandTypePre + " ";
        String sql = "";
        try {
            if (!sqlCommandTypePre.startsWith("DelayMessageMapper")) {
                sql = SqlUpdateLogInterceptor.showSql(configuration, boundSql);
                MetaObject insertObject = SystemMetaObject.forObject(parameterObject);
                Object id = insertObject.getValue("id");
                sql = sql + " | insert_id=" + id;
            }
        } catch (Exception e) {
            log.info("sql批量insert,msg:{}", e.getMessage());
        }
        log.info(sqlCommandTypePre + sql);
        return result;
    }

    /**
     * 生成拦截对象的代理
     *
     * @param target 目标对象
     * @return 代理对象
     */
    @Override
    public Object plugin(Object target) {
        if (target instanceof StatementHandler) {
            //使用MyBatis提供的Plugin类生成代理对象
            return Plugin.wrap(target, this);
        }
        return target;
    }

    /**
     * @param properties mybatis获取插件的属性，我们在MyBatis配置文件里配置的
     */
    @Override
    public void setProperties(Properties properties) {

    }


}
