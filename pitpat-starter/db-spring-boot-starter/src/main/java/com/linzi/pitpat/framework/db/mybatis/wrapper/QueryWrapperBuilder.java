package com.linzi.pitpat.framework.db.mybatis.wrapper;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.CaseFormat;
import com.linzi.pitpat.lang.OrderItem;
import com.linzi.pitpat.lang.Query;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 查询条件构建器
 * 自动根据查询对象的字段值构建 MyBatis-Plus 的 QueryWrapper
 * 参考 https://baomidou.com/guides/wrapper/#orderby
 */
@Slf4j
public class QueryWrapperBuilder {
    private static final List<String> doSuperClassFields = List.of("isDelete", "gmtCreate", "gmtModified");
    private static final List<String> querySuperClassFields = List.of("orders");

    // 缓存字段名到数据库列名的映射
    private static final Map<String, String> COLUMN_CACHE = new ConcurrentHashMap<>();

    /**
     * 比较操作符枚举
     */
    private enum CompareOperator {
        EQ("Eq", "等于"),
        NE("Ne", "不等于"),
        GT("Gt", "大于"),
        GE("Ge", "大于等于"),
        LT("Lt", "小于"),
        LE("Le", "小于等于"),
        List("List", "范围操作"),
        Like("Like", "模糊匹配"),
        LikeRight("LikeRight", "左匹配"),
        LikeLeft("LikeLeft", "右匹配");

        private final String suffix;
        private final String desc;

        CompareOperator(String suffix, String desc) {
            this.suffix = suffix;
            this.desc = desc;
        }
    }

    /**
     * 构建查询条件
     *
     * @param query       查询对象
     * @param entityClass 实体类class
     * @return QueryWrapper
     */
    public static <T, Q extends Query> QueryWrapper<T> build(Q query, Class<T> entityClass) {
        QueryWrapper<T> wrapper = new QueryWrapper<>();

        if (query == null) {
            return wrapper;
        }

        // 获取查询对象的所有字段
        Field[] fields = query.getClass().getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            try {
                // 获取字段值
                Object value = field.get(query);

                // 跳过空值
                if (value == null) {
                    continue;
                }
                //跳过空字符串
                if (value instanceof String strValue) {
                    if (!StringUtils.hasText(strValue)) {
                        continue;
                    }
                }

                //跳过静态字段
                if (Modifier.isStatic(field.getModifiers())) {
                    if (log.isDebugEnabled()) {
                        log.debug("ignore static field, name={}", field.getName());
                    }
                    continue;
                }

                // 获取对应的实体类字段名
                String fieldName = field.getName();
                // 处理比较操作符
                buildCondition(wrapper, entityClass, fieldName, value);

            } catch (IllegalAccessException e) {
                throw new RuntimeException("构建查询条件失败", e);
            }
        }

        // 处理排序
        handleOrdering(wrapper, query);
        if (log.isDebugEnabled()) {
            log.debug("getTargetSql={}", wrapper.getTargetSql());
        }
        return wrapper;
    }

    /**
     * 根据字段名称和值构建查询条件
     */
    private static <T> void buildCondition(QueryWrapper<T> wrapper,
                                           Class<T> entityClass, String fieldName, Object value) {
        if (log.isDebugEnabled()) {
            log.debug("fieldName={},value={}", fieldName, value);
        }
        String column = getColumnName(entityClass, fieldName);

        // 跳过空值
        if (column == null) {
            return;
        }
        // 处理集合类型
        if (value instanceof Collection) {
            if (!CollectionUtils.isEmpty((Collection<?>) value)) {
                wrapper.in(column, (Collection<?>) value);
            }
            return;
        }
        // 处理数组类型
        if (value.getClass().isArray()) {
            Object[] arrayValue = (Object[]) value;
            if (arrayValue.length > 0) {
                //String column = getColumnName(entityClass, fieldName);
                wrapper.in(column, Arrays.asList(arrayValue));
            }
            return;
        }

        // 处理比较操作符
        for (CompareOperator op : CompareOperator.values()) {
            if (fieldName.endsWith(op.suffix)) {
                //String realFieldName = fieldName.substring(0, fieldName.length() - op.suffix.length());
                //column = getColumnName(entityClass, realFieldName);
                applyOperator(wrapper, op, column, value);
                return;
            }
        }
        // 默认使用等值查询
        wrapper.eq(column, value);

        //// 处理字符串类型
        //if (value instanceof String strValue) {
        //    if (StringUtils.hasText(strValue)) {
        //        //String column = getColumnName(entityClass, fieldName);
        //
        //        // 处理模糊查询
        //        if (fieldName.endsWith("Like")) {
        //            //String realFieldName = fieldName.substring(0, fieldName.length() - 4);
        //            //column = getColumnName(entityClass, realFieldName);
        //            wrapper.like(column, strValue);
        //            return;
        //        }
        //
        //        // 处理比较操作符
        //        for (CompareOperator op : CompareOperator.values()) {
        //            if (fieldName.endsWith(op.suffix)) {
        //               /* String realFieldName = fieldName.substring(0,   fieldName.length() - op.suffix.length());
        //                column = getColumnName(entityClass, realFieldName);*/
        //                applyOperator(wrapper, op, column, strValue);
        //                return;
        //            }
        //        }
        //
        //        // 默认使用等值查询
        //        wrapper.eq(column, strValue);
        //    }
        //    return;
        //}
        //
        //// 处理数值类型
        //if (value instanceof Number) {
        //    //String column = getColumnName(entityClass, fieldName);
        //
        //    // 处理比较操作符
        //    for (CompareOperator op : CompareOperator.values()) {
        //        if (fieldName.endsWith(op.suffix)) {
        //            //String realFieldName = fieldName.substring(0, fieldName.length() - op.suffix.length());
        //            //column = getColumnName(entityClass, realFieldName);
        //            applyOperator(wrapper, op, column, value);
        //            return;
        //        }
        //    }
        //
        //    // 默认使用等值查询
        //    wrapper.eq(column, value);
        //}
        //
        //// 处理日起类型
        //if (value instanceof Temporal || value instanceof Date) {
        //    //String column = getColumnName(entityClass, fieldName);
        //
        //    // 处理比较操作符
        //    for (CompareOperator op : CompareOperator.values()) {
        //        if (fieldName.endsWith(op.suffix)) {
        //            //String realFieldName = fieldName.substring(0, fieldName.length() - op.suffix.length());
        //            //column = getColumnName(entityClass, realFieldName);
        //            applyOperator(wrapper, op, column, value);
        //            return;
        //        }
        //    }
        //    // 默认使用等值查询
        //    wrapper.eq(column, value);
        //}
        //// boolean、 enum 等默认使用等值查询
        //wrapper.eq(column, value);
    }

    /**
     * 应用操作符
     */
    private static <T> void applyOperator(QueryWrapper<T> wrapper,
                                          CompareOperator op, String column, Object value) {
        switch (op) {
            case EQ:
                wrapper.eq(column, value);
                break;
            case NE:
                wrapper.ne(column, value);
                break;
            case GT:
                wrapper.gt(column, value);
                break;
            case GE:
                wrapper.ge(column, value);
                break;
            case LT:
                wrapper.lt(column, value);
                break;
            case LE:
                wrapper.le(column, value);
                break;
            case List:
                wrapper.in(column, value);
                break;
            case Like:
                wrapper.like(column, value);
                break;
            case LikeLeft:
                wrapper.likeLeft(column, value);
                break;
            case LikeRight:
                wrapper.likeRight(column, value);
                break;
        }
    }

    /**
     * 获取数据库列名
     * 优先使用@TableField注解,否则将驼峰命名转换为下划线格式
     */
    private static String getColumnName(Class<?> entityClass, String fieldName) {

        for (CompareOperator operator : CompareOperator.values()) {
            if (fieldName.endsWith(operator.suffix)) {
                fieldName = fieldName.substring(0, fieldName.length() - operator.suffix.length());
                if (log.isDebugEnabled()) {
                    log.debug("getRealFieldName, realFieldName={}", fieldName);
                }
                break;
            }
        }
        // 先从缓存中查找
        String cacheKey = entityClass.getName() + "." + fieldName;
        String columnName = COLUMN_CACHE.get(cacheKey);
        if (columnName != null) {
            return columnName;
        }

        try {
            Field field = findFieldInHierarchy(entityClass, fieldName, doSuperClassFields);
            // 检查是否有TableField注解
            TableField tableField = field.getAnnotation(TableField.class);
            if (tableField != null && StringUtils.hasText(tableField.value())) {
                columnName = tableField.value();
            } else {
                // 驼峰转下划线
                columnName = snakeCase(fieldName);
            }

            // 放入缓存
            COLUMN_CACHE.put(cacheKey, columnName);
            return columnName;
        } catch (Exception e) {
            log.error("实体类: {} 缺少字段:{}", entityClass.getSimpleName(), fieldName, e);
        }
        return null;
    }

    /**
     * 在类层次结构中查找字段
     */
    private static Field findFieldInHierarchy(Class<?> currentClass, String fieldName, List<String> superClassFields) {
        while (currentClass != null && currentClass != Object.class) {
            try {
                return currentClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                // 如果提供了父类字段集合且不包含目标字段,直接返回
                if (superClassFields != null && !superClassFields.contains(fieldName)) {
                    if (log.isDebugEnabled()) {
                        log.debug("Field {} not found in superclass fields set", fieldName);
                    }
                    return null;
                }
                // 继续查找父类
                currentClass = currentClass.getSuperclass();
            } catch (SecurityException e) {
                log.error("Security exception while accessing field: {}", fieldName, e);
                return null;
            }
        }
        return findFieldInInterfaces(currentClass, fieldName);
    }

    /**
     * 在接口中查找字段
     */
    private static Field findFieldInInterfaces(Class<?> clazz, String fieldName) {
        if (clazz == null) {
            return null;
        }

        // 查找当前类的所有接口
        for (Class<?> iface : clazz.getInterfaces()) {
            try {
                return iface.getDeclaredField(fieldName);
            } catch (NoSuchFieldException | SecurityException e) {
                // 继续查找接口的父接口
                Field field = findFieldInInterfaces(iface, fieldName);
                if (field != null) {
                    return field;
                }
            }
        }
        return null;
    }


    /**
     * 处理排序
     */
    private static <T, Q extends Query> void handleOrdering(QueryWrapper<T> wrapper, Q query) {
        try {
            //Field ordersField = query.getClass().getDeclaredField("orders");
            Field ordersField = findFieldInHierarchy(query.getClass(), "orders", querySuperClassFields);
            if (Objects.isNull(ordersField)) {
                return;
            }
            ordersField.setAccessible(true);
            Collection<?> orders = (Collection<?>) ordersField.get(query);
            if (Objects.nonNull(orders)) {
                orders.forEach(item -> {
                    if (item instanceof OrderItem orderItem) {
                        wrapper.orderBy(true, orderItem.isAsc(), snakeCase(orderItem.getColumn()));
                    }
                });
            }
        } catch (IllegalAccessException e) {
            log.error("没有orders字段: msg={}", e.getMessage(), e);
        }
    }

    private static String snakeCase(String column) {
        String s = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, column);
        return "`" + s + "`";
    }
}
