package com.linzi.pitpat.framework.db.util;

import com.linzi.pitpat.framework.db.dto.ArrayStackDto;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

public class StackTraceUtil {

    public final static ThreadLocal<ArrayStackDto> arrayStackThreadLocal = new ThreadLocal<>();
    public final static ThreadLocal<String> stackTraceThreadLocal = new ThreadLocal<>();

    public static String peek() {
        ArrayStackDto arrayStack = arrayStackThreadLocal.get();

        if (!CollectionUtils.isEmpty(arrayStack)) {
            return (String) arrayStack.peek();
        }
        return "";
    }

    public static String getCallStackTrace() {
        String stackTrace = stackTraceThreadLocal.get();
        if (!StringUtils.hasText(stackTrace)) {
            stackTrace = "";
        }
        return stackTrace;
    }
}
