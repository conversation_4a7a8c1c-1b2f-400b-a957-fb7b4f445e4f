/*
 *    Copyright (c) 2018-2025, song<PERSON>yuan All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the 霖梓控股 developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: songfayuan (<EMAIL>)
 */

package com.linzi.pitpat.framework.db.interceptor;

import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.framework.db.util.StackTraceUtil;
import com.lz.mybatis.plugins.interceptor.baomidou.SqlParserHandler;
import com.lz.mybatis.plugins.interceptor.utils.PSqlParseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.BatchResult;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Properties;

/**
 * 批量插入拦截器实现
 *
 * <AUTHOR>
 * @date 2018/1/19
 * 数据权限插件，guns
 */
@Slf4j
@Intercepts({@Signature(type = Executor.class, method = "flushStatements", args = {})})
public class SQLInsertBatchLogInterceptor extends SqlParserHandler implements Interceptor {

    @Value("${spring.profiles.active}")
    private String profile;

    @Value("${spring.application.name}")
    private String applicationName;

    public static String host = "localhost";

    static {
        try {
            InetAddress inetAddress = InetAddress.getLocalHost();
            host = inetAddress.getHostAddress();
        } catch (UnknownHostException e) {
            host = "127.0.0.1";
        }
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object result = invocation.proceed();
        try {
            Throwable throwable = new Throwable();
            String sqlCommandTypePre = " ";
            String peek0 = StackTraceUtil.peek();
            String callStackTrace = StackTraceUtil.getCallStackTrace();
            if (StringUtils.hasText(peek0)) {
                StackTraceElement[] stackTraceElements = throwable.getStackTrace();
                String classInfos[] = peek0.split(":");
                int i = 0;
                for (StackTraceElement stackTraceElement : stackTraceElements) {
                    i++;
                    if (stackTraceElement.getClassName().equals(classInfos[0]) && stackTraceElement.getMethodName().equals(classInfos[1])) {
                        sqlCommandTypePre = callStackTrace + ":" + stackTraceElement.getLineNumber();
                        break;
                    }
                    if (i > 100) {
                        break;
                    }
                }
            }
            if (result instanceof ArrayList && result != null && ((ArrayList<?>) result).size() > 0) {
                List<BatchResult> results = (List<BatchResult>) result;
                for (BatchResult batchResult : results) {
                    String mapperdId = PSqlParseUtil.getMapperId(batchResult.getMappedStatement());
                    List<Object> list = batchResult.getParameterObjects();
                    for (Object l : list) {
                        try {
                            String sql = batchResult.getSql();
                            String toLowerSql = sql.toLowerCase().trim();
                            if (toLowerSql.startsWith("update")) {          //如果是update开头的，则过滤掉
                                continue;
                            }
                            printBatchSql(batchResult, l, sqlCommandTypePre + ":" + mapperdId);
                        } catch (Exception e) {
                            log.info("异常 ", e);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("SQLBatchInsertResultLogInterceptor方法处理异常异常", e);
        }
        return result;
    }

    public void printBatchSql(BatchResult batchResult, Object entity, String pre) {
        String originSql = batchResult.getSql();
        Method[] methods = entity.getClass().getDeclaredMethods();
        StringBuffer sqlColumn = new StringBuffer("(");
        StringBuffer sqlValues = new StringBuffer("(");
        Long id = null;
        for (Method method : methods) {
            StringBuffer error = new StringBuffer();
            try {
                String methodName = method.getName();
                if (methodName.contains("getId")) {
                    id = MapUtil.getLong(method.invoke(entity));
                    continue;
                }
                if (methodName.startsWith("get")) {
                    error.append("methodName = " + methodName);
                    Object o = method.invoke(entity);
                    if (o != null) {
                        error.append(", o = ").append(o);
                        String column = methodName.substring(3);
                        error.append(", column= " + column);
                        String db_column = toUnderScoreCase(column);
                        error.append(", db_column= " + db_column);
                        sqlColumn.append(" ").append(db_column).append(" ").append(",");
                        String parameterValue = SqlUpdateLogInterceptor.getParameterValue(o);
                        error.append(", parameterValue= " + parameterValue);
                        sqlValues.append(" ").append(parameterValue).append(" ").append(",");
                    }
                }
            } catch (Exception e) {
                log.error("SQLBatchInsertResultLogInterceptor异常实体内容为={} ", entity, e);
            }
        }


        String sqlPre = originSql.substring(0, originSql.indexOf("("));
        sqlColumn.deleteCharAt(sqlColumn.length() - 1);
        sqlValues.deleteCharAt(sqlValues.length() - 1);
        sqlColumn.append(")");
        sqlValues.append(")");
        StringBuffer sb = new StringBuffer();
        sb.append(sqlPre).append(sqlColumn).append(" VALUES ").append(sqlValues);
        String sql = pre + " " + sb + " | batch_insert_id=" + id;
        String traceId = String.format("%s_%s_%s", applicationName, host, MDC.get("traceId"));

        log.debug("批量插入 SQL - traceId={}, sql={}", traceId, sql);
    }

    /**
     * 下划线转驼峰命名
     */
    public static String toUnderScoreCase(String str) {
        if (str == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        // 前置字符是否大写
        boolean preCharIsUpperCase = true;
        // 当前字符是否大写
        boolean curreCharIsUpperCase = true;
        // 下一字符是否大写
        boolean nexteCharIsUpperCase = true;
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (i > 0) {
                preCharIsUpperCase = Character.isUpperCase(str.charAt(i - 1));
            } else {
                preCharIsUpperCase = false;
            }

            curreCharIsUpperCase = Character.isUpperCase(c);

            if (i < (str.length() - 1)) {
                nexteCharIsUpperCase = Character.isUpperCase(str.charAt(i + 1));
            }

            if (preCharIsUpperCase && curreCharIsUpperCase && !nexteCharIsUpperCase) {
                sb.append("_");
            } else if ((i != 0 && !preCharIsUpperCase) && curreCharIsUpperCase) {
                sb.append("_");
            }
            sb.append(Character.toLowerCase(c));
        }

        return sb.toString();
    }


    /**
     * 生成拦截对象的代理
     *
     * @param target 目标对象
     * @return 代理对象
     */
    @Override
    public Object plugin(Object target) {
        if (target instanceof Executor) {
            //使用MyBatis提供的Plugin类生成代理对象
            return Plugin.wrap(target, this);
        }
        return target;
    }

    /**
     * @param properties mybatis获取插件的属性，我们在MyBatis配置文件里配置的
     */
    @Override
    public void setProperties(Properties properties) {

    }


    public static void main(String[] args) {
        String methodName = "ge";
        String column = methodName.substring(3);
        System.out.println(column);
    }

}
