/*
 *    Copyright (c) 2018-2025, song<PERSON>yuan All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the 霖梓控股 developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: songfayuan (<EMAIL>)
 */

package com.linzi.pitpat.framework.db.interceptor;


import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.framework.db.dto.ScopeDataDto;
import com.linzi.pitpat.framework.db.util.ReflectionUtils;
import com.linzi.pitpat.framework.db.util.StackTraceUtil;
import com.lz.mybatis.plugins.interceptor.baomidou.SqlParserHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.binding.BindingException;
import org.apache.ibatis.builder.SqlSourceBuilder;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 单条数据更新和批量更新拦截器实现
 *
 * <AUTHOR>
 * @date 2018/1/19
 * 数据权限插件，guns
 */
@Slf4j
@Intercepts({@Signature(type = StatementHandler.class, method = "parameterize", args = {Statement.class})})
public class SqlUpdateLogInterceptor extends SqlParserHandler implements Interceptor {
    private static final Pattern pattern = Pattern.compile("\\?");

    @Value("${spring.profiles.active}")
    private String profile;

    @Value("${spring.application.name}")
    private String applicationName;

    public static String host = "localhost";

    static {
        try {
            InetAddress inetAddress = InetAddress.getLocalHost();
            host = inetAddress.getHostAddress();
        } catch (UnknownHostException e) {
            host = "127.0.0.1";
        }
    }

    public static String staticProfile;
    public static String staticAppName;

    @PostConstruct
    public void init() {
        // 初始化激活的Profile
        staticProfile = profile;
        // 初始化应用名称（带默认值）
        staticAppName = applicationName;
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        StatementHandler statementHandler = PluginUtils.realTarget(invocation.getTarget());
        MetaObject metaObject = SystemMetaObject.forObject(statementHandler);
        MappedStatement mappedStatement = (MappedStatement) metaObject.getValue("delegate.mappedStatement");
        // insert 语句的sql  在其他地方
        if (SqlCommandType.INSERT.equals(mappedStatement.getSqlCommandType())
                || SqlCommandType.SELECT.equals(mappedStatement.getSqlCommandType())) {
            return invocation.proceed();
        }

        this.sqlParser(metaObject);
        // 先判断是不是SELECT操作
        BoundSql boundSql = (BoundSql) metaObject.getValue("delegate.boundSql");
        String mapperdId = getMapperId(mappedStatement);
        Throwable throwable = new Throwable();
        String sqlCommandTypePre = mapperdId + " ";
        String peek0 = StackTraceUtil.peek();
        String callStackTrace = StackTraceUtil.getCallStackTrace();
        if (StringUtils.hasText(peek0)) {
            StackTraceElement[] stackTraceElements = throwable.getStackTrace();
            String classInfos[] = peek0.split(":");
            int i = 0;
            for (StackTraceElement stackTraceElement : stackTraceElements) {
                i++;
                if (stackTraceElement.getClassName().equals(classInfos[0]) && stackTraceElement.getMethodName().equals(classInfos[1])) {
                    sqlCommandTypePre = callStackTrace + ":" + stackTraceElement.getLineNumber() + ":" + mapperdId + " ";
                    break;
                }
                if (i > 100) {
                    break;
                }
            }
        }
        Configuration configuration = mappedStatement.getConfiguration();
        String sql = "";
        if (!sqlCommandTypePre.startsWith("DelayMessageMapper")) {
            sql = showSql(configuration, boundSql);
        }
        log.info("{}{}", sqlCommandTypePre, sql);
        return invocation.proceed();

    }

    public static String getParameterValue(Object obj) {
        String value = null;
        if (obj instanceof String) {
            String temp = obj.toString();
            if (temp.indexOf("'") >= 0) {
                temp = temp.replaceAll("'", "''");
            }
            value = "'" + temp + "'";
        } else if (obj instanceof Date date) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());

            value = "'" + formatter.format(zonedDateTime) + "'";
        } else if (obj instanceof LocalDateTime date) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                ZonedDateTime zonedDateTime = ZonedDateTime.of(date, ZoneId.systemDefault());

                value = "'" + formatter.format(zonedDateTime) + "'";
            } catch (Exception e) {
                log.error("LocalDateTime 转化异常", e);
            }
        } else if (obj instanceof ZonedDateTime date) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                value = "'" + date.format(formatter.withZone(ZoneId.systemDefault())) + "'";
            } catch (Exception e) {
                log.error("ZonedDateTime 转化异常", e);
            }
        } else {
            if (obj != null) {
                value = obj.toString();
            } else {
                value = "''";
            }
        }
        return value;
    }


    @SuppressWarnings("unused")
    private static Object getParameterValue(String propertyName, Object obj) {

        Object value = null;

        try {
            value = ReflectionUtils.getObjectFieldValue(obj, propertyName);
        } catch (BindingException e2) {
            return null;
        }
        return value;
    }

    /**
     * 生成拦截对象的代理
     *
     * @param target 目标对象
     * @return 代理对象
     */
    @Override
    public Object plugin(Object target) {
        if (target instanceof StatementHandler) {
            return Plugin.wrap(target, this);
        }
        return target;
    }

    /**
     * mybatis配置的属性
     *
     * @param properties mybatis配置的属性
     */
    @Override
    public void setProperties(Properties properties) {

    }

    public static String getMapperId(MappedStatement mappedStatement) {
        try {
            String id = mappedStatement.getId();
            if (id.contains(".")) {
                String ids[] = id.split("\\.");
                return ids[ids.length - 2] + "." + ids[ids.length - 1];
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
        }
        return "";
    }


    /**
     * 查找参数是否包括DataScope对象
     *
     * @param parameterObj 参数列表
     * @return DataScope
     */
    private ScopeDataDto findDataScopeObject(Object parameterObj) {
        if (parameterObj instanceof ScopeDataDto) {
            return (ScopeDataDto) parameterObj;
        } else if (parameterObj instanceof Map) {
            for (Object val : ((Map<?, ?>) parameterObj).values()) {
                if (val instanceof ScopeDataDto) {
                    return (ScopeDataDto) val;
                }
            }
        }
        return null;
    }


    public static String showSql(Configuration configuration, BoundSql boundSql) {
        String boundSqlStr = null;
        String sql = null;
        Map<String, String> listMap = new HashMap<>();
        StringBuffer errorInfo = new StringBuffer();
        try {
            Object parameterObject = boundSql.getParameterObject();
            List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
            boundSqlStr = boundSql.getSql();
            //sql = boundSql.getSql().replaceAll("[\\s]+", " ");//性能太差，可能会导致频繁的 GC和 cpu 高占用
            sql =  SqlSourceBuilder.removeExtraWhitespaces(boundSql.getSql());
            if (!parameterMappings.isEmpty() && parameterObject != null) {
                TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
                if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
                    errorInfo.append("0. 有 hasTypeHandler 为true ");
                    String value = getParameterValue(parameterObject);
                    if (value.contains("?")) {
                        errorInfo.append("value contains ? value = " + value);
                        String key = getUserPoolOrder();
                        listMap.put(key, value);
                        value = key;
                    }
                    //sql = sql.replaceFirst("\\?", value);
                    sql = pattern.matcher(sql).replaceFirst(value);
                    errorInfo.append("replaceFirst after sql = " + sql);
                } else {
                    MetaObject metaObject = configuration.newMetaObject(parameterObject);
                    for (ParameterMapping parameterMapping : parameterMappings) {
                        String propertyName = parameterMapping.getProperty();
                        if (metaObject.hasGetter(propertyName)) {
                            errorInfo.append("1. propertyName = " + propertyName + ",hasGetter method ,  ");
                            Object obj = metaObject.getValue(propertyName);
                            String value = getParameterValue(obj);
                            errorInfo.append(" value = " + value);
                            if (value.contains("?")) {
                                errorInfo.append(", has ? ");
                                String key = getUserPoolOrder();
                                listMap.put(key, value);
                                value = key;
                            }
                            if (value.contains("$")) {
                                errorInfo.append(", has $ ");
                                value = Matcher.quoteReplacement(value);
                            }
                            //sql = sql.replaceFirst("\\?", value);
                            sql = pattern.matcher(sql).replaceFirst(value);
                            errorInfo.append("\n");
                        } else if (boundSql.hasAdditionalParameter(propertyName)) {
                            errorInfo.append("2. propertyName = " + propertyName + ", hasAdditionalParameter ");
                            Object obj = boundSql.getAdditionalParameter(propertyName);
                            String value = getParameterValue(obj);
                            errorInfo.append(" value = " + value);
                            if (value.contains("?")) {
                                errorInfo.append("has ? ");
                                String key = getUserPoolOrder();
                                listMap.put(key, value);
                                value = key;
                            }
                            sql = pattern.matcher(sql).replaceFirst(value);
                            //sql = sql.replaceFirst("\\?", value);
                            errorInfo.append("\n");
                        }
                    }
                }
            }

            if (!listMap.isEmpty()) {
                for (Map.Entry<String, String> m : listMap.entrySet()) {
                    sql = sql.replace(m.getKey(), m.getValue());
                }
            }
            return SqlSourceBuilder.removeExtraWhitespaces(sql);
        } catch (Exception e) {
            log.error("showSql exception boundSqlStr = " + boundSqlStr + ",sql=" + sql + ",listMap=" + JsonUtil.writeString(listMap) +
                    " ,errorInfo= " + errorInfo.toString(), e);
        }
        return "";
    }

    public static String getUserPoolOrder() {
        StringBuilder sb = new StringBuilder();
        try {
            sb.append(staticProfile).append("_");
            sb.append(staticAppName).append("_");
            sb.append(host).append("_");
            sb.append(MDC.get("traceId"));
        } catch (Exception e) {
            log.error("getUserPoolOrder异常", e);
        }
        return sb.toString();
    }


}
