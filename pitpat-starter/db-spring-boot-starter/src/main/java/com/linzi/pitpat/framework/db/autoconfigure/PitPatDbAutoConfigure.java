package com.linzi.pitpat.framework.db.autoconfigure;

import com.alibaba.cloud.nacos.NacosConfigManager;
import com.linzi.pitpat.framework.db.mybatis.plugin.EntityInterceptor;
import com.linzi.pitpat.framework.db.spi.NacosConfigManagerBeanProcessor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PitPatDbAutoConfigure {

    @Bean
    public NacosConfigManagerBeanProcessor configManagerBeanProcessor(NacosConfigManager configManager) {
        return new NacosConfigManagerBeanProcessor(configManager);
    }

    @Bean
    public EntityInterceptor entityInterceptor() {
        return new EntityInterceptor();
    }
}
