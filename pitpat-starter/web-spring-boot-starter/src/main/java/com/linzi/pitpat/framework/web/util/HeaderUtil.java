package com.linzi.pitpat.framework.web.util;

import com.linzi.pitpat.core.constants.Constants;
import com.linzi.pitpat.core.util.AesUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;

@Slf4j
public class HeaderUtil {

    public static String getEmail(HttpServletRequest request) {
        String emailAddress = request.getHeader("emailAddress");
        String token = request.getHeader("token");
        if (!StringUtils.hasText(emailAddress)) {
            emailAddress = request.getHeader("email");
        }
        // 如果邮箱存在
        if(StringUtils.hasText(emailAddress) && emailAddress.contains("@")){
            return emailAddress;
        }
        return dataDecryption(emailAddress, token);
    }

    /**
     * 如果邮箱加密，需要解密后在返回
     * @param paramStr
     * @param token
     * @return
     */
    private static String dataDecryption(String paramStr, String token) {
        String aesKey = Constants.defaultAesKey;
        if (StringUtils.hasText(token)) {
            aesKey = token.substring(0, 16);
        }
        try{
            if(!StringUtils.hasText(paramStr)){
                return paramStr;
            }
            paramStr = AesUtil.aesDecryptString(paramStr, aesKey);
        }catch (Exception e){
            log.error("解密失败！",e);
        }
        log.info("解密得到字符串 " +  paramStr);
        return paramStr;
    }
}
