package com.linzi.pitpat.framework.web;

import lombok.SneakyThrows;
import org.springframework.util.ResourceUtils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.Scanner;
import java.util.Set;
import java.util.TreeMap;

public class I18nWrite {
    @SneakyThrows
    public static void main(String[] args) {
        String path = ResourceUtils.getURL("classpath:").getPath();
        System.out.println(path);
        String baseDir = path.substring(0,path.indexOf("/web-spring-boot-starter"));

        String i18nDir = baseDir+"//web-spring-boot-starter//src//main//resources//i18n";
        i18nDir = i18nDir.replaceAll("//",  File.separator);
        File dir = new File(i18nDir);
        Map<String,File> i18nFileMap = new HashMap<>();
        if (!dir.exists() || dir.isFile()) {
            System.out.println("请输入i18n文件夹");
        }
        for (File file : dir.listFiles()) {
            String name = file.getName();
            if(!name.endsWith("properties")){
                continue;
            }
            String subName = name.substring(0, name.indexOf("."));
            if(!name.contains("_")){
                i18nFileMap.put("en_US_default",file);
                System.out.println("en_US_default"+" "+name);
            }else{
                String key = subName.substring(name.indexOf("_")+1);
                i18nFileMap.put(key,file);
                System.out.println(key+" "+name);
            }

        }
        Scanner scanner = new Scanner(System.in);
        System.out.println("请输入key");
        while (scanner.hasNext()) {
            String key = scanner.nextLine();
            System.out.println("请输入值");
            String val = scanner.nextLine();
            String[] split = val.split("\t");
            if(split.length==i18nFileMap.size()-1){
                String[] country = {"zh_CN","en_US","fr_CA","de_DE","it_IT",
                        "es_ES","pt_PT","th_TH"};
                for (int i = 0; i < split.length; i++) {

                    write(key, split[i], i18nFileMap.get(country[i]));
                    if(country[i].equals("en_US")){
                        write(key,split[i],i18nFileMap.get("en_US_default"));
                    }
                }
            }else{
                System.out.println("数量不对");
            }
            System.out.println("请输入key");
        }
    }
    private static void write(String key,String val,File file){
        System.out.println(key);
        System.out.println(val);
        System.out.println(file.getAbsoluteFile());
        Properties properties = loadProperties(file.getAbsolutePath());
        properties.put(key,val);

        // 使用 TreeMap 来排序
        Map<String, String> sortedPropertiesB = new TreeMap<>();
        for (String k : properties.stringPropertyNames()) {
            sortedPropertiesB.put(k, properties.getProperty(k));
        }

        try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file), StandardCharsets.UTF_8))) {
            Set<String> sortedKeys = sortedPropertiesB.keySet();
            for (String sortedKey : sortedKeys) {
                String s = sortedPropertiesB.get(sortedKey);
                s =s.replace("\n", "\\n");
                writer.write(sortedKey + "=" + s);
                writer.newLine();
            }
            System.out.println(file.getName()+"已更新并排序");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    private static Properties loadProperties(String filePath) {
        Properties properties = new Properties();
        try (InputStream input = new FileInputStream(filePath)) {
            properties.load(new InputStreamReader(input, StandardCharsets.UTF_8));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return properties;
    }

}