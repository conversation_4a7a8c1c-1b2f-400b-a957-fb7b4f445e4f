package com.linzi.pitpat.framework.web.util;

import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.util.StringUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;

@Slf4j
public class OrderUtil {

    public static final ThreadLocal<String> threadLocalOldNO = new ThreadLocal();
    public  static  String host = "localhost";
    static {
        try {
            InetAddress inetAddress = InetAddress.getLocalHost();
            host = inetAddress.getHostAddress();
        } catch (UnknownHostException e) {
            host = "127.0.0.1";
        }
    }
    public static String getUserPoolOrder() {
        StringBuilder sb = new StringBuilder();
        try {
            sb.append(SpringContextUtils.getActiveProfile()).append("_");
            sb.append(SpringContextUtils.getApplicationName()).append("_");
            sb.append(host).append("_");
            sb.append(MDC.get("traceId"));
        } catch (Exception e) {
            log.error("getUserPoolOrder异常",e);
        }
        return sb.toString();
    }


    public static String getUniqueCode(String pre) {
        SimpleDateFormat dateformat = new SimpleDateFormat("SSSyyyyMMddHHmmss");
        StringBuffer sb = new StringBuffer();
        sb.append(pre);
        return sb.
                append((int) (Math.random() * 1000)).append(dateformat.format(System.currentTimeMillis())).toString();
    }



    public static String getUniqueCode() {
        SimpleDateFormat dateformat = new SimpleDateFormat("SSSyyyyMMddHHmmss");
        StringBuffer sb = new StringBuffer();
        return sb.
                append((int) (Math.random() * 1000)).append(dateformat.format(System.currentTimeMillis())).toString();
    }


    public static String addLogNo() {
      return addLogNo(null, null);
    }

    public static String addLogNo(String traceId, Long time) {
        try {
            if (traceId == null) {
                traceId = getUserPoolOrder();
            }
            if (time == null) {
                time = System.currentTimeMillis();
            }
//            ch.qos.logback.classic.Logger.inheritableThreadLocalNo.set(traceId);
//            ch.qos.logback.classic.Logger.inheritableThreadLocalTime.set(time);
            threadLocalOldNO.remove();
            return traceId;
        } catch (Exception e) {
            log.error(" add log error ", e);
        }
        return null;
    }

    public static void addLogNo(String traceId) {
        try {
//            if (traceId == null) {
                traceId = getUserPoolOrder();
//            }
//            ch.qos.logback.classic.Logger.inheritableThreadLocalNo.set(traceId);
            threadLocalOldNO.remove();
        } catch (Exception e) {
            log.error(" add log error ", e);
        }
    }


    public static String getLogNo() {
//        String logNo = ch.qos.logback.classic.Logger.inheritableThreadLocalNo.get();
//        if (!StringUtils.hasText(logNo)) {
            return getUserPoolOrder();
//        }
//        return logNo;
    }

    public static String append(Object obj) {
        String logNo = "";//ch.qos.logback.classic.Logger.inheritableThreadLocalNo.get();
        if (!StringUtils.hasText(logNo)) {
            logNo = getUserPoolOrder();
            threadLocalOldNO.remove();
        }
        String oldNo = threadLocalOldNO.get();
        if (!StringUtils.hasText(oldNo)) {
            threadLocalOldNO.set(logNo);
        }
//        ch.qos.logback.classic.Logger.inheritableThreadLocalNo.set(oldNo + "_" + obj);
        return logNo;
    }



    public static String getBatchNo() {
        SimpleDateFormat dateformat = new SimpleDateFormat("SSSyyyyMMddHHmmss");
        StringBuffer sb = new StringBuffer();
        return sb.
                append((int) (Math.random() * 1000)).append(dateformat.format(System.currentTimeMillis())).toString();
    }


    public static Long getExeTime() {
//        Long time = Logger.inheritableThreadLocalTime.get();
//        if (time == null) {
            return System.currentTimeMillis();
//        }
//        return time;

    }


    public static void removeLogNo() {
        try {
//            ch.qos.logback.classic.Logger.inheritableThreadLocalNo.remove();
//            ch.qos.logback.classic.Logger.inheritableThreadLocalTime.remove();
        } catch (Exception e) {
            log.error(" remove log error ", e);
        }
    }

    public static void main(String[] args) {
        String a =  OrderUtil.getUserPoolOrder();
        System.out.println(a);

    }
}
