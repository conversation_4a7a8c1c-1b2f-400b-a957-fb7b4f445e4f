package com.linzi.pitpat.framework.web.util;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.securitytoken.AWSSecurityTokenService;
import com.amazonaws.services.securitytoken.AWSSecurityTokenServiceClientBuilder;
import com.amazonaws.services.securitytoken.model.GetSessionTokenRequest;
import com.amazonaws.services.securitytoken.model.GetSessionTokenResult;
import com.linzi.pitpat.core.util.NanoId;
import com.linzi.pitpat.framework.web.Resp.TemporaryCredentialsS3;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.Tika;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProviderChain;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;
import software.amazon.awssdk.services.s3.model.S3Exception;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedPutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@Slf4j
public class AwsUtil {
    private static String accessKey;
    private static String secretKey;
    private static String region;
    //  bucket
    private static String bucket;
    private static String baseUrl;

    public static void setAccessKey(String accessKey) {
        AwsUtil.accessKey = accessKey;
    }

    public static void setSecretKey(String secretKey) {
        AwsUtil.secretKey = secretKey;
    }

    public static void setBucket(String bucket) {
        AwsUtil.bucket = bucket;
    }

    public static void setRegion(String region) {
        AwsUtil.region = region;
    }

    public static void setBaseUrl(String baseUrl) {
        AwsUtil.baseUrl = baseUrl;
    }

    /**
     * 获取S3Client对象
     *
     * @return s3
     */
    private static S3Client getAmazonS3() {
        return S3Client.builder()
                .credentialsProvider(getAwsCredentialsProviderChain())
                .region(Region.of(region))
                .build();
    }

    // 获取aws供应商凭据
    private static AwsCredentialsProviderChain getAwsCredentialsProviderChain(){
        return AwsCredentialsProviderChain
                .builder()
                .addCredentialsProvider(new AwsCredentialsProvider() {
                    @Override
                    public AwsCredentials resolveCredentials() {
                        return AwsBasicCredentials.create(accessKey, secretKey);
                    }
                }).build();
    }

    public static String putS3Object(String objectKey, String objectPath) {
        try {
            S3Client s3 = getAmazonS3();
            PutObjectRequest putOb = PutObjectRequest.builder()
                    .bucket(bucket)
                    .key(objectKey)
                    .build();
            PutObjectResponse response = s3.putObject(putOb, RequestBody.fromBytes(getObjectFile(objectPath)));
            return response.eTag();
        } catch (S3Exception e) {
            System.err.println(e.getMessage());
        }
        return "";
    }

    private static byte[] getObjectFile(String filePath) {
        FileInputStream fileInputStream = null;
        byte[] bytesArray = null;
        try {
            File file = new File(filePath);
            bytesArray = new byte[(int) file.length()];
            fileInputStream = new FileInputStream(file);
            fileInputStream.read(bytesArray);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return bytesArray;
    }

    public static byte[] getObjectFile(MultipartFile file) {
        try {
            return file.getBytes();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Map<String, Object> putS3Object(MultipartFile file, String fileFolder) {
        return putS3Object(getObjectFile(file),file.getOriginalFilename(), fileFolder);
    }

    public static Map<String, Object> putS3Object(byte[] file, String filename, String fileFolder) {
        Map<String, Object> map = new HashMap<>();
        map.put("url","");
        try {
            S3Client s3 = getAmazonS3();
            String fileName = getFileName(filename);
            map.put("fileName",fileName);
            String originalFilename = fileFolder +"/"+fileName;

            Tika tika = new Tika();
            String minetype = tika.detect(originalFilename);
            PutObjectRequest putOb = PutObjectRequest.builder()
                    .bucket(bucket)
                    .key(originalFilename)
                    .contentType(minetype)
                    .build();
            s3.putObject(putOb, RequestBody.fromBytes(file));
            //访问路径
            map.put("url",baseUrl + originalFilename);


            return map;
        } catch (S3Exception e) {
            log.error("putS3Object error,e={}",e.getMessage());
        }
        return map;
    }

    public static Map<String, Object> putFileNameS3Object(byte[] file, String fileName, String fileFolder) {
        Map<String, Object> map = new HashMap<>();
        map.put("url","");
        try {
            S3Client s3 = getAmazonS3();
            map.put("fileName",fileName);
            String originalFilename = fileFolder +"/"+fileName;
            Tika tika = new Tika();
            String minetype = tika.detect(originalFilename);
            PutObjectRequest putOb = PutObjectRequest.builder()
                    .bucket(bucket)
                    .key(originalFilename)
                    .contentType(minetype)
                    .build();
            s3.putObject(putOb, RequestBody.fromBytes(file));
            //访问路径
            map.put("url",baseUrl + originalFilename);


            return map;
        } catch (S3Exception e) {
            log.error("putS3Object error,e={}",e.getMessage());
        }
        return map;
    }


    public static String upload2Cloud(byte[] file, String fileName, String fileFolder) {
        try {
            S3Client s3 = getAmazonS3();
            String originalFilename = fileFolder + "/" + fileName;
            Tika tika = new Tika();
            String minetype = tika.detect(originalFilename);
            PutObjectRequest putOb = PutObjectRequest.builder()
                    .bucket(bucket)
                    .key(originalFilename)
                    .contentType(minetype)
                    .build();
            s3.putObject(putOb, RequestBody.fromBytes(file));
            //访问路径
            String url = baseUrl + originalFilename;
            return url;
        } catch (S3Exception e) {
            log.error("upload2Cloud error,e={}",e.getMessage());
        }
        return null;
    }

    private static String getFileName(String oldFileName) {
        String newFileName = NanoId.randomNanoId();
        if (oldFileName.lastIndexOf(".") >= 0) {
            newFileName = newFileName + oldFileName.substring(oldFileName.lastIndexOf("."));
        }
        return newFileName;
    }

    public static void deleteS3Object(String originalFilename) {
        S3Client s3 = getAmazonS3();
        DeleteObjectRequest deleteObjectRequest = DeleteObjectRequest.builder().bucket(bucket).key(originalFilename).build();
        s3.deleteObject(deleteObjectRequest);
    }

    private static S3Presigner getS3Presigner() {
        return  S3Presigner.builder()
                .region(Region.of(region))
                .credentialsProvider(getAwsCredentialsProviderChain())
                .build();
    }

    public static String preview(String key) {
        S3Client s3 = getAmazonS3();
        GetObjectRequest getObjectRequest = GetObjectRequest.builder().bucket(bucket).key(key).build();
        GetObjectPresignRequest getObjectPresignRequest = GetObjectPresignRequest.builder()
                .signatureDuration(Duration.ofDays(7L))
                .getObjectRequest(getObjectRequest)
                .build();
        PresignedGetObjectRequest presignedGetObjectRequest =
                getS3Presigner().presignGetObject(getObjectPresignRequest);
        log.info(presignedGetObjectRequest.url().toString());
        return presignedGetObjectRequest.url().toString();
    }

    public static String createPreSignedPutUrl(String keyName) {
        try (S3Presigner preSigner =
                     S3Presigner.builder().region(Region.of(region)).credentialsProvider(getAwsCredentialsProviderChain())
                             .build()) {
            PutObjectRequest objectRequest = PutObjectRequest.builder()
                    .bucket(bucket)
                    .key(keyName)
                    .build();
            PutObjectPresignRequest preSignRequest = PutObjectPresignRequest.builder()
                    .signatureDuration(Duration.ofMinutes(30))  // The URL will expire in 10 minutes.
                    .putObjectRequest(objectRequest)
                    .build();
            PresignedPutObjectRequest preSignedRequest = preSigner.presignPutObject(preSignRequest);
            log.info("PreSigned URL: [{}]", preSignedRequest.url().toString());
            log.info("HTTP method: [{}]", preSignedRequest.httpRequest().method());
            return preSignedRequest.url().toExternalForm();
        }
    }



    public static String createPreSignedGetUrl(String keyName) {
        try (S3Presigner preSigner =
                     S3Presigner.builder().region(Region.of(region)).credentialsProvider(getAwsCredentialsProviderChain())
                             .build()) {
            GetObjectRequest objectRequest = GetObjectRequest.builder()
                    .bucket(bucket)
                    .key(keyName)
                    .build();
            GetObjectPresignRequest preSignRequest = GetObjectPresignRequest.builder()
                    .signatureDuration(Duration.ofMinutes(60))  // The URL will expire in 10 minutes.
                    .getObjectRequest(objectRequest)
                    .build();
            PresignedGetObjectRequest preSignedRequest = preSigner.presignGetObject(preSignRequest);
            log.info("PreSigned URL: [{}]", preSignedRequest.url().toString());
            log.info("HTTP method: [{}]", preSignedRequest.httpRequest().method());
            return preSignedRequest.url().toExternalForm();
        }
    }

    public static TemporaryCredentialsS3 getCredentialsS3(){
        BasicAWSCredentials awsCredentials = new BasicAWSCredentials(accessKey, secretKey);
        // 创建 STS 客户端
        AWSSecurityTokenService stsClient = AWSSecurityTokenServiceClientBuilder.standard().
                withCredentials(new AWSStaticCredentialsProvider(awsCredentials))
                .withRegion(region)  // 设置区域
                .build();
        // 设置临时凭证的有效期（单位为秒）
        int durationSeconds = 3600; // 1小时有效期
        GetSessionTokenRequest sessionTokenRequest = new GetSessionTokenRequest()
                .withDurationSeconds(durationSeconds);
        // 获取临时凭证
        GetSessionTokenResult sessionTokenResult = stsClient.getSessionToken(sessionTokenRequest);
        TemporaryCredentialsS3 temporaryCredentialsS3 = new TemporaryCredentialsS3();
        temporaryCredentialsS3.setTemporaryAccessKey(sessionTokenResult.getCredentials().getAccessKeyId());
        temporaryCredentialsS3.setTemporarySecretKey(sessionTokenResult.getCredentials().getSecretAccessKey());
        temporaryCredentialsS3.setSessionToken(sessionTokenResult.getCredentials().getSessionToken());
        temporaryCredentialsS3.setBucketName(bucket);
        temporaryCredentialsS3.setBaseUrl(baseUrl);
        return temporaryCredentialsS3;
    }
}
