package com.linzi.pitpat.framework.web.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Slf4j
public class RestTemplateUtil {

    private static RestTemplate restTemplate;

    public static void setRestTemplate(RestTemplate restTemplate) {
        RestTemplateUtil.restTemplate = restTemplate;
    }

    public static String get(String url) {
        return get(url, null);
    }

    public static String get(String url, Map<String, String> headers) {
        return request(url, HttpMethod.GET, null, headers);
    }

    public static <T> String post(String url, T body) {
        return post(url, body, null);
    }

    public static <T> String postRetry(String url, T body) {
        return request(url, HttpMethod.POST, body, Map.of("X-Retry", "3", "X-Retry-Delay", "5"));
    }

    public static <T> String post(String url, T body, Map<String, String> headers) {
        return request(url, HttpMethod.POST, body, headers);
    }

    public static <T> String put(String url, T body) {
        return request(url, HttpMethod.PUT, body, null);
    }

    public static <T> String put(String url, T body, Map<String, String> headers) {
        return request(url, HttpMethod.PUT, body, headers);
    }

    /**
     * 如果要支持重试，请在header中设置X-Retry和X-Retry-Delay
     * @param url
     * @param method
     * @param body
     * @param headers
     * @return
     * @param <T>
     */
    public static <T> String request(String url, HttpMethod method, T body, Map<String, String> headers) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);

        if (!CollectionUtils.isEmpty(headers)) {
            headers.keySet().forEach(key -> httpHeaders.set(key, headers.get(key)));
        }

        HttpEntity<T> httpEntity = new HttpEntity<>(body, httpHeaders);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("request for {}, url:{} with req :{}", method, url, httpEntity);
        ResponseEntity<String> response = restTemplate.exchange(url, method, httpEntity, String.class);
        stopWatch.stop();
        log.info("response for {} url:{} with resp :{},time:{}", method, url, response,stopWatch.getTotalTimeMillis());
        return response.getBody();
    }
}
