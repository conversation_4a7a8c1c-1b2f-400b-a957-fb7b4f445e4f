package com.linzi.pitpat.framework.web.util;

import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;

import java.util.Locale;

/**
 * <AUTHOR>
 * @Date 2023/11/28 11:17
 * @Description 国际化消息工具类
 */
@Slf4j
public class I18nMsgUtils {

    private static final MessageSource messageSource;

    private static final MessageSource i18nMessageSourceDirect;

    static {
        messageSource = SpringContextUtils.getBean("messageSource", MessageSource.class);
        log.info("init messageSource={}", messageSource);
        i18nMessageSourceDirect = SpringContextUtils.getBean("i18nMessageSourceDirect", ResourceBundleMessageSource.class);
        log.info("load i18nMessageSourceDirect={}", i18nMessageSourceDirect);
    }


    public static String getMessage(String code, Object... args) {
        try {
            return messageSource.getMessage(code, args, LocaleContextHolder.getLocale());
        } catch (Exception e) {
            log.error("getMessage error, e={}, code={}, args={}", e.getMessage(), code, args, e);
            return getI18nDirect(LocaleContextHolder.getLocale(), code, args);
        }
    }

    public static String getLangMessage(String langCode, String code, Object... args) {
        Locale locale = Locale.US;
        try {
            try {
                String[] split = langCode.split("_");
                locale = new Locale(split[0], split[1]);
            } catch (Exception e) {
                log.info("解析地区错误,使用回退策略重新解析, msg={}", e.getMessage());
                locale = new Locale(langCode);
            }
            return messageSource.getMessage(code, args, locale);
        } catch (Exception e) {
            log.error("getMessage error, e={}, code={}, args={}", e.getMessage(), code, args, e);
            return getI18nDirect(locale, code, args);
        }
    }

    /**
     * 获取语言code：en_US
     *
     * @return
     */
    public static String getLangCode() {
        return LocaleContextHolder.getLocale().toString();
    }

    public static String getI18nDirect(Locale locale, String code, Object... args) {
        try {
            return i18nMessageSourceDirect.getMessage(code, args, locale);
        } catch (Exception e) {
            log.error("i18nMessageSourceDirect getMessage error, e={}, code={}, args={}", e.getMessage(), code, args, e);
            return code;
        }
    }
}
