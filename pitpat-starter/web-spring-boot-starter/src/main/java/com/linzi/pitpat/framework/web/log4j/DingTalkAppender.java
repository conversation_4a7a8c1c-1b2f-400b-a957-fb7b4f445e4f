package com.linzi.pitpat.framework.web.log4j;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.linzi.pitpat.core.util.DingTalkUtils;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.util.MD5Util;
import com.linzi.pitpat.core.util.MapUtil;
import com.linzi.pitpat.core.util.StringUtil;
import com.linzi.pitpat.core.util.dto.DingTalkRequestDto;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.framework.redis.util.RedisUtil;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import com.linzi.pitpat.framework.web.entity.GitResp;
import com.linzi.pitpat.framework.web.task.TransmittableDecorator;
import com.linzi.pitpat.framework.web.util.OrderUtil;
import com.linzi.pitpat.framework.web.util.RestTemplateUtil;
import com.linzi.pitpat.lang.ExceptionInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.logging.log4j.core.Appender;
import org.apache.logging.log4j.core.Core;
import org.apache.logging.log4j.core.Filter;
import org.apache.logging.log4j.core.Layout;
import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.appender.AbstractAppender;
import org.apache.logging.log4j.core.config.Property;
import org.apache.logging.log4j.core.config.plugins.Plugin;
import org.apache.logging.log4j.core.config.plugins.PluginAttribute;
import org.apache.logging.log4j.core.config.plugins.PluginElement;
import org.apache.logging.log4j.core.config.plugins.PluginFactory;
import org.apache.logging.log4j.core.layout.PatternLayout;
import org.slf4j.MDC;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 钉钉日志推送Appender - 简化版
 * 仅支持直接发送错误消息，无批量发送功能
 */
@Slf4j
@Plugin(name = "DingTalk", category = Core.CATEGORY_NAME, elementType = Appender.ELEMENT_TYPE)
public class DingTalkAppender extends AbstractAppender {

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final TransmittableThreadLocal<ExceptionInfo> inheritableThreadLocalExceptionInfo = new TransmittableThreadLocal<>();

    protected DingTalkAppender(String name, Filter filter, Layout<? extends Serializable> layout, boolean ignoreExceptions, Property[] properties) {
        super(name, filter, layout, ignoreExceptions, properties);
    }


    @Override
    public void append(LogEvent event) {
        // 直接发送每个日志事件
        sendSingleEvent(event);
    }

    /**
     * 发送单个日志事件
     */
    private void sendSingleEvent(LogEvent event) {
        try {
            if (event.getThrown() == null) {
                return;
            }
            if (event.getThrown() instanceof BaseException) {
                return;
            }
            if (event.getThrown() instanceof ClientAbortException) {
                return;
            }
            if (Objects.isNull(SpringContextUtils.getActiveProfile())) {
                return;
            }
            if(SpringContextUtils.isDev()){
                return;
            }

            RedisUtil redisUtil = SpringContextUtils.getBean(RedisUtil.class);

            String stackTrace = getStackTrace(event.getThrown());
            ExceptionInfo exceptionInfo = getExceptionByLine(stackTrace);
            String encode = MD5Util.sha256(exceptionInfo.getClassName() + exceptionInfo.getMethodName() + exceptionInfo.getLine());

            Object obj = redisUtil.get(encode);
            if (obj != null) {
                return;
            }

            String message = formatLogMessage(event, stackTrace);

            dingtalkExecutor().execute(() -> {
                sendDingTalkMsg(redisUtil, message, encode, exceptionInfo);
            });
        } catch (Exception e) {
            System.err.println("处理日志事件失败: " + e.getMessage());
        }
    }

    /**
     * 格式化日志消息为Markdown格式
     */
    private String formatLogMessage(LogEvent event, String stackTrace) {
        String timestamp = formatter.format(ZonedDateTime.now());
        // 异常堆栈
        Throwable throwable = event.getThrown();

        StringBuilder markdown = new StringBuilder();
        markdown.append("- 时间: ").append(timestamp).append("\n");
        if (Objects.nonNull(MDC.get("traceId"))) {
            markdown.append("- traceId: ").append(MDC.get("traceId")).append("\n");
        }
        markdown.append("- 异常: ").append(throwable.getClass().getName()).append("\n");
        markdown.append("- 错误信息: ").append(event.getMessage().getFormattedMessage()).append(",").append(throwable.getMessage()).append("\n\n");

        if (stackTrace.length() > 2000) {
            stackTrace = stackTrace.substring(0, 2000) + "...";
        }
        markdown.append("- 堆栈信息: ").append(stackTrace).append("\n");
        return markdown.toString();
    }

    /**
     * 获取异常堆栈信息 - 优化版本
     */
    private String getStackTrace(Throwable throwable) {
        // 预估StringBuilder容量，减少扩容
        StringBuilder text = new StringBuilder(1024);

        // 添加堆栈信息（限制长度）
        StackTraceElement[] stackTrace = throwable.getStackTrace();
        int maxLines = Math.min(15, stackTrace.length); // 最多显示10行堆栈
        for (int i = 0; i < maxLines; i++) {
            text.append(stackTrace[i].toString()).append("\n");
        }
        return text.toString();
    }

    /**
     * 插件工厂方法
     */
    @PluginFactory
    public static DingTalkAppender createAppender(
            @PluginAttribute("name") String name,
            @PluginElement("Layout") Layout<? extends Serializable> layout,
            @PluginElement("Filter") Filter filter) {

        if (name == null) {
            LOGGER.error("No name provided for DingTalkAppender");
            return null;
        }

        if (layout == null) {
            layout = PatternLayout.createDefaultLayout();
        }

        return new DingTalkAppender(name, filter, layout, false, null);
    }

    private void sendDingTalkMsg(RedisUtil redisUtil, String content, String encode, ExceptionInfo exceptionInfo) {
        try {
            String access_token = "39a7de79443e098420632e2e6d127e02de54227e96faba09edc06fbcf20c843d";
            String secret = "SEC07c244261961ad04a6e62e4b92b7f2dc5ba14e307bc07c9bce91e0eb833f1aaa";
            if (SpringContextUtils.isOnline()) {
                access_token = "c13225fdd97e8b338b1c2f06ac4551cea909772d3ea5823a46ef9b9f1ffa4543";                  //线上的群
                secret = "SEC6bf964efca4099c6dc28d18877392c8096a514f3046daa39869c59fa2a474fe1";
            }
            redisUtil.set(encode, "1", 30, TimeUnit.MINUTES);
            String mobiles = getMobiles(exceptionInfo);
            String title = "【" + SpringContextUtils.getApplicationName() + "】【" + SpringContextUtils.getActiveProfile() + "】 ip=" + SpringContextUtils.getServerIp();
            DingTalkUtils.sendMsg(DingTalkRequestDto.ofMarkdown(access_token, secret, title + "\n" + content, mobiles));
        } catch (Exception e1) {
            log.info("发送异常钉钉消息异常, msg={} ", e1.getMessage());
        } finally {
            OrderUtil.removeLogNo();
        }
    }

    private static String getMobiles(ExceptionInfo exceptionInfo) {
        String mobiles = "";
        if (StringUtils.hasText(exceptionInfo.getClassName())) {
            exceptionInfo.setContent(null);
            String body = JsonUtil.writeString(exceptionInfo);
            log.info(" getGitPhone请求参数 ：" + body);
            String response = RestTemplateUtil.post("https://tkjdeploy.yijiesudai.com/api/jenkins/getGitPhone", body);
            log.info(" getGitPhone get git info " + response);
            if (StringUtils.hasText(response)) {
                GitResp gitResp = JsonUtil.readValue(response, GitResp.class);
                if (gitResp != null && Objects.equals(gitResp.getCode(), 200)) {
                    mobiles = gitResp.getGitPhone();
                }
            }
        }
        return mobiles;
    }

    public ThreadPoolTaskExecutor dingtalkExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("dingtalk-executor");
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2 + 1);
        executor.setKeepAliveSeconds(60);
        executor.setQueueCapacity(1000);
        executor.setTaskDecorator(new TransmittableDecorator());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy());//超时丢弃最老的请求
        executor.initialize();
        return executor;
    }


    public static ExceptionInfo getExceptionByLine(String e) {
        ExceptionInfo exceptionInfo = new ExceptionInfo();
        String[] lines = e.split("\n");
        exceptionInfo.setContent(e);
        for (String line : lines) {
            String lineStr = line;
            if (StringUtils.hasText(lineStr)) {
                lineStr = lineStr.trim();
                if (StringUtil.isEmpty(exceptionInfo.getClassName())
                        && StringUtils.hasText(lineStr)
                        && lineStr.startsWith("com.linzi.pitpat")
                        && !lineStr.contains(".util.")
                        && !lineStr.contains(".exception.")
                        && !lineStr.contains("$$")
                        && !lineStr.contains("Exception")
                ) {
                    //lineStr = lineStr.substring(3);
                    log.info(" 获取到的行信息 " + lineStr);
                    String[] classMethods = lineStr.split("\\(");

                    if (classMethods.length > 1) {
                        String method = classMethods[0];
                        String[] methods = method.split("\\.");
                        StringBuilder packageName = new StringBuilder();
                        for (int j = 0; j < methods.length - 2; j++) {
                            packageName.append(methods[j]);
                            if (j < methods.length - 3) {
                                packageName.append(".");
                            }
                        }

                        String className = methods[methods.length - 2];
                        String methodName = methods[methods.length - 1];
                        String lineInfo = classMethods[1];
                        lineInfo = lineInfo.replace(")", "");
                        String[] lineInfos = lineInfo.split(":");
                        Integer linex = MapUtil.getInteger(lineInfos[1], 0);
                        log.info("packageName=" + packageName + ",className =" + className + ",methodName=" + methodName + ",行号=" + linex);
                        exceptionInfo.setLine(linex);
                        exceptionInfo.setPackageName(packageName.toString());
                        exceptionInfo.setClassName(className);
                        exceptionInfo.setMethodName(methodName);
                        return exceptionInfo;
                    }
                }
            }
        }
        if (inheritableThreadLocalExceptionInfo.get() != null) {
            exceptionInfo = inheritableThreadLocalExceptionInfo.get();
            log.info(" 从AOP中获得异常信息 " + JsonUtil.writeString(exceptionInfo));
            exceptionInfo.setContent(e);
        }
        return exceptionInfo;
    }
}

