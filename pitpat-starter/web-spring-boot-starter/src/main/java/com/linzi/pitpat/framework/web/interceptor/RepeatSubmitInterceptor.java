package com.linzi.pitpat.framework.web.interceptor;

import com.google.common.io.CharStreams;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.framework.web.annotation.RepeatSubmit;
import com.linzi.pitpat.framework.web.constants.WebConstants;
import com.linzi.pitpat.framework.web.filter.RepeatedlyRequestWrapper;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.framework.web.util.ServletUtils;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/12/12 14:14
 */
@Slf4j
@Component
public class RepeatSubmitInterceptor implements HandlerInterceptor {
    // 用户令牌自定义标识
    @Value("${web.interceptor.token.header:Authorization}")
    private String header;
    public final String REPEAT_PARAMS = "repeatParams";

    public final String REPEAT_TIME = "repeatTime";
    @Autowired
    private RedissonClient redissonClient;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return true;// 不是方法的请求，直接放行
        }
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        RepeatSubmit annotation = method.getAnnotation(RepeatSubmit.class);
        if (annotation == null) {
            return true;// 没有注解标记，直接放行
        }
        if (this.isRepeatSubmit(request,annotation.timeout())) {
            Result ajaxResult = CommonResult.fail(I18nMsgUtils.getMessage("web.interceptor.repeat-submit"));
            ServletUtils.renderString(response, JsonUtil.writeString(ajaxResult));
            return false;
        }
        return true;
    }


    /**
     * 验证是否重复提交由子类实现具体的防重复提交的规则
     *
     * @param request
     * @param intervalTime
     * @return
     * @throws Exception
     */
    public boolean isRepeatSubmit(HttpServletRequest request, int intervalTime) {
        String nowParams = "";
        if (request instanceof RepeatedlyRequestWrapper) {
            RepeatedlyRequestWrapper repeatedlyRequest = (RepeatedlyRequestWrapper) request;
            try {
                nowParams = readUtf8(repeatedlyRequest.getInputStream());
            } catch (IOException e) {
                log.warn("读取流出现问题！");
            }
        }

        // body参数为空，获取Parameter的数据
        if (!StringUtils.hasText(nowParams)) {
            nowParams = JsonUtil.writeString(request.getParameterMap());
        }
        Map<String, Object> nowDataMap = new HashMap<String, Object>();
        nowDataMap.put(REPEAT_PARAMS, nowParams);
        nowDataMap.put(REPEAT_TIME, System.currentTimeMillis());

        // 请求地址（作为存放cache的key值）
        String url = request.getRequestURI();

        String cacheRepeatKey = getSubmitKey(request);

        return isRepeatSubmitCache(cacheRepeatKey, url, nowDataMap, intervalTime);
    }

    protected Boolean isRepeatSubmitCache(String cacheRepeatKey, String url, Map<String, Object> nowDataMap, int intervalTime) {
        RBucket<Object> bucket = redissonClient.getBucket(cacheRepeatKey);
        Object sessionObj = bucket.get();
        if (sessionObj != null) {
            Map<String, Object> sessionMap = (Map<String, Object>) sessionObj;
            if (sessionMap.containsKey(url)) {
                Map<String, Object> preDataMap = (Map<String, Object>) sessionMap.get(url);
                if (compareParams(nowDataMap, preDataMap) && compareTime(nowDataMap, preDataMap, intervalTime)) {
                    return true;
                }
            }
        }
        Map<String, Object> cacheMap = new HashMap<>();
        cacheMap.put(url, nowDataMap);
        bucket.set(cacheMap, intervalTime, TimeUnit.SECONDS);
        return false;
    }

    public String getSubmitKey(HttpServletRequest request) {
        // 请求地址（作为存放cache的key值）
        String url = request.getRequestURI();
        // 唯一值（没有消息头则使用请求地址）
        String submitKey = request.getHeader(header);
        if (!StringUtils.hasText(submitKey))
        {
            submitKey = url;
        }
        return WebConstants.REPEAT_SUBMIT_KEY + submitKey;
    }

    public static String readUtf8(InputStream inputStream) throws IOException {
        try (inputStream) {
            return CharStreams.toString(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
        }
    }
    /**
     * 判断参数是否相同
     */
    public boolean compareParams(Map<String, Object> nowMap, Map<String, Object> preMap) {
        String nowParams = (String) nowMap.get(REPEAT_PARAMS);
        String preParams = (String) preMap.get(REPEAT_PARAMS);
        return nowParams.equals(preParams);
    }

    /**
     * 判断两次间隔时间
     */
    public boolean compareTime(Map<String, Object> nowMap, Map<String, Object> preMap, int intervalTime) {
        long time1 = (Long) nowMap.get(REPEAT_TIME);
        long time2 = (Long) preMap.get(REPEAT_TIME);
        if ((time1 - time2) < (intervalTime*1000)) {
            return true;
        }
        return false;
    }
}
