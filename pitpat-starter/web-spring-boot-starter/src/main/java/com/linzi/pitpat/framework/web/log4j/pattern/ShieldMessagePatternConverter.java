package com.linzi.pitpat.framework.web.log4j.pattern;

import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.config.plugins.Plugin;
import org.apache.logging.log4j.core.pattern.ConverterKeys;
import org.apache.logging.log4j.core.pattern.LogEventPatternConverter;
import org.apache.logging.log4j.message.Message;
import org.apache.logging.log4j.util.PerformanceSensitive;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Objects;

/**
 * 日志脱敏转换器，支持Spring Boot 2.x
 * 用于自动脱敏日志中的敏感信息（手机号、身份证、邮箱等）
 *
 * <AUTHOR>
 * @version ShieldMessagePatternConverter.java, v 0.1 2024年04月09日 21:13 baichun
 */
@Plugin(name = "ShieldPatternConverter", category = "Converter")
@ConverterKeys({"shield", "sd", "shieldMessage", "sm"})
@PerformanceSensitive({"allocation"})
public final class ShieldMessagePatternConverter extends LogEventPatternConverter {
    private final String[] options;

    private ShieldMessagePatternConverter(String[] options) {
        super("Shield", "shield");
        this.options = options == null ? null : (String[]) Arrays.copyOf(options, options.length);
    }

    //必须要有newInstance方法，log4j2会调用该方法进行初始化
    public static ShieldMessagePatternConverter newInstance(String[] options) {
        return new ShieldMessagePatternConverter(options);
    }

    @Override
    public void format(LogEvent logEvent, StringBuilder output) {
        Message message = logEvent.getMessage();
        if (isFormatMessage(message)) {            //在这里格式化脱敏日志
            String msgInfo = ShieldUtils.format(message.getFormattedMessage());
            output.append(msgInfo);
        } else {
            output.append(message.getFormattedMessage());
        }
    }

    private boolean isFormatMessage(Message message) {
        return Objects.nonNull(message) && StringUtils.hasText(message.getFormattedMessage());
    }
}
