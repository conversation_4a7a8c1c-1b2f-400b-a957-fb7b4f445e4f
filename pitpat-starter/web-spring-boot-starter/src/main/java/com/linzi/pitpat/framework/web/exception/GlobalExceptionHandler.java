package com.linzi.pitpat.framework.web.exception;

import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.EnvUtils;
import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.exception.BaseException;
import com.linzi.pitpat.exception.BizI18nException;
import com.linzi.pitpat.framework.web.util.I18nMsgUtils;
import com.linzi.pitpat.lang.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolationException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @Value("${spring.profiles.active:dev}")
    private String profile;
    @Value("${spring.application.name}")
    private String project;


    @ExceptionHandler(BaseException.class)
    public Result<String> handleBaseException(HttpServletRequest request, BaseException e) {
        String method = request.getMethod();
        String requestUri = request.getRequestURI();

        log.info("handleCustomException requestUri:{},method:{}", requestUri, method);
        log.info(e.getMessage(), e);
        return CommonResult.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(BizI18nException.class)
    public Result<String> handleI18nCustomException(HttpServletRequest request, BizI18nException e) {
        String method = request.getMethod();
        String requestUri = request.getRequestURI();
        log.info("handleI18nCustomException requestUri:{},method:{}", requestUri, method);

        String message;
        if (Objects.isNull(e.getParams())) {
            message = I18nMsgUtils.getLangMessage(getLanguageCode(request), e.getI18nCode());
        } else {
            message = I18nMsgUtils.getLangMessage(getLanguageCode(request), e.getI18nCode(), e.getParams());
        }
        log.info(message, e);

        return CommonResult.fail(e.getCode(), message);
    }

    @ExceptionHandler(NullPointerException.class)
    public Result<String> handleNullPointerException(HttpServletRequest request, NullPointerException e) {
        String method = request.getMethod();
        String requestUri = request.getRequestURI();

        log.info("handleNullPointerException requestUri:{},method:{}", requestUri, method);
        log.error(e.getMessage(), e);

        String msg = e.getMessage();
        if (EnvUtils.isReallyOnline(profile) && !"pitpat-admin".equals(project)) {
            msg = CommonError.BUSINESS_ERROR.getMsg();
        }
        return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), msg);
    }

    @ExceptionHandler({MethodArgumentNotValidException.class})
    public Result<String> handleParamException(HttpServletRequest request, MethodArgumentNotValidException e) {
        String method = request.getMethod();
        String requestUri = request.getRequestURI();

        log.info("handleParamException requestUri:{},method:{}", requestUri, method);
        log.info(e.getMessage(), e);

        BindingResult exceptions = e.getBindingResult();
        // 判断异常中是否有错误信息，如果存在就使用异常中的消息，否则使用默认消息
        if (exceptions.hasErrors()) {
            List<ObjectError> errors = exceptions.getAllErrors();
            if (!errors.isEmpty()) {
                // 这里列出了全部错误参数，按正常逻辑，只需要第一条错误即可
                FieldError fieldError = (FieldError) errors.get(0);
                return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), fieldError.getDefaultMessage());
            }
        }
        return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), "param not exits");
    }

    @ExceptionHandler(Exception.class)
    public Result<String> handleException(HttpServletRequest request, Exception e) {
        String method = request.getMethod();
        String requestUri = request.getRequestURI();

        Map<String, String[]> parameterMap = request.getParameterMap();
        String body = parameterMap.entrySet().stream()
                .map(entry -> entry.getKey() + ":" + (entry.getValue().length > 1 ? Arrays.toString(entry.getValue()) : entry.getValue()[0]))
                .collect(Collectors.joining(","));
        log.info("handleException请求参数,requestUri:{},method:{}, body:{{}}", requestUri, method, body);
        log.error(e.getMessage(), e);
        String msg = e.getMessage();
        if (EnvUtils.isReallyOnline(profile) && !"pitpat-admin".equals(project)) {
            msg = CommonError.BUSINESS_ERROR.getMsg();
        }
        return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), msg);
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(BindException.class)
    public Result<String> validatedBindException(BindException e) {
        log.info("validatedBindException");
        log.info(e.getMessage(), e);
        String message = e.getAllErrors().get(0).getDefaultMessage();
        return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), e.getMessage());
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public Result<String> constraintViolationException(ConstraintViolationException e) {
        log.info("constraintViolationException");
        log.info(e.getMessage(), e);
        String message = e.getConstraintViolations().iterator().next().getMessage();
        return CommonResult.fail(CommonError.BUSINESS_ERROR.getCode(), e.getMessage());
    }

    /**
     * 获取用户语言，先从请求头获取不到查询用户表
     *
     * @return
     */
    public String getLanguageCode(HttpServletRequest request) {
        String languageCode = request.getHeader(I18nConstant.LANGUAGE_HEAD_PARAM);
        if (StringUtils.hasText(languageCode)) {
            return languageCode;
        }
        //使用默认语言-英语
        return I18nConstant.LanguageCodeEnum.en_US.getCode();
    }
}
