package com.linzi.pitpat.framework.web.log4j.pattern;

/**
 * 脱敏工具类
 * 支持手机号、身份证、邮箱等敏感信息的脱敏处理
 *
 * <AUTHOR>
 * @version ShieldUtils.java, v 0.1 2024年04月09日 21:13 baichun
 */
class ShieldUtils {

    /**
     * 不使用正则的脱敏方法
     */
    public static String format(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        StringBuilder result = new StringBuilder(text);
        boolean modified = shieldEmails(result);
        // 脱敏手机号
        //modified |= shieldPhoneNumbers(result);
        //
        //// 脱敏身份证
        //modified |= shieldIdCards(result);
        //
        //// 脱敏邮箱
        //modified |= shieldEmails(result);
        //
        //// 脱敏银行卡
        //modified |= shieldBankCards(result);

        return modified ? result.toString() : text;
    }

    /**
     * 脱敏手机号：基于字符串查找
     */
    private static boolean shieldPhoneNumbers(StringBuilder text) {
        boolean modified = false;
        int start = 0;

        while (start < text.length() - 10) {
            // 查找可能的手机号起始位置
            int phoneStart = findPhoneStart(text, start);
            if (phoneStart == -1) break;

            // 验证是否为有效手机号
            if (isValidPhone(text, phoneStart)) {
                // 脱敏：保留前3位和后4位
                for (int i = phoneStart + 3; i < phoneStart + 7; i++) {
                    text.setCharAt(i, '*');
                }
                modified = true;
                start = phoneStart + 11;
            } else {
                start = phoneStart + 1;
            }
        }

        return modified;
    }

    /**
     * 查找手机号起始位置
     */
    private static int findPhoneStart(StringBuilder text, int start) {
        for (int i = start; i <= text.length() - 11; i++) {
            char c = text.charAt(i);
            // 手机号以1开头
            if (c == '1') {
                return i;
            }
        }
        return -1;
    }

    /**
     * 验证是否为有效手机号
     */
    private static boolean isValidPhone(StringBuilder text, int start) {
        if (start + 11 > text.length()) return false;

        // 检查第一位是否为1
        if (text.charAt(start) != '1') return false;

        // 检查第二位是否为3-9
        char second = text.charAt(start + 1);
        if (second < '3' || second > '9') return false;

        // 检查后续9位是否都是数字
        for (int i = start + 2; i < start + 11; i++) {
            char c = text.charAt(i);
            if (c < '0' || c > '9') return false;
        }

        return true;
    }

    /**
     * 脱敏身份证号
     */
    private static boolean shieldIdCards(StringBuilder text) {
        boolean modified = false;
        int start = 0;

        while (start < text.length() - 14) {
            int idStart = findIdCardStart(text, start);
            if (idStart == -1) break;

            int idLength = getIdCardLength(text, idStart);
            if (idLength >= 15) {
                // 脱敏：保留前6位和后4位
                int maskStart = idStart + 6;
                int maskEnd = idStart + idLength - 4;
                for (int i = maskStart; i < maskEnd; i++) {
                    text.setCharAt(i, '*');
                }
                modified = true;
                start = idStart + idLength;
            } else {
                start = idStart + 1;
            }
        }

        return modified;
    }

    /**
     * 查找身份证号起始位置
     */
    private static int findIdCardStart(StringBuilder text, int start) {
        for (int i = start; i <= text.length() - 15; i++) {
            if (Character.isDigit(text.charAt(i))) {
                // 检查是否有连续的数字
                int digitCount = 0;
                int j = i;
                while (j < text.length() && (Character.isDigit(text.charAt(j)) ||
                        (j == text.length() - 1 && text.charAt(j) == 'X'))) {
                    digitCount++;
                    j++;
                }
                if (digitCount >= 15) {
                    return i;
                }
            }
        }
        return -1;
    }

    /**
     * 获取身份证号长度
     */
    private static int getIdCardLength(StringBuilder text, int start) {
        int length = 0;
        int i = start;
        while (i < text.length() &&
                (Character.isDigit(text.charAt(i)) ||
                        (length == 17 && text.charAt(i) == 'X'))) {
            length++;
            i++;
        }
        return length;
    }

    /**
     * 脱敏邮箱地址 - 前3位+4个星号+后1位
     */
    private static boolean shieldEmails(StringBuilder text) {
        boolean modified = false;
        int start = 0;

        while (start < text.length()) {
            int atIndex = text.indexOf("@", start);
            if (atIndex == -1) break;

            // 查找邮箱用户名的起始位置
            int usernameStart = findEmailUsernameStart(text, atIndex);
            if (usernameStart != -1) {
                int usernameLength = atIndex - usernameStart;
                if (usernameLength > 4) { // 至少需要5个字符才能脱敏（前3+后1+至少1个中间字符）
                    // 保留前3位和后1位，中间用4个星号替换
                    String prefix = text.substring(usernameStart, usernameStart + 3);
                    String suffix = text.substring(atIndex - 1, atIndex);

                    // 删除原用户名
                    text.delete(usernameStart, atIndex);

                    // 插入新的脱敏格式：前3位+4个星号+后1位
                    text.insert(usernameStart, prefix + "****" + suffix);

                    // 更新atIndex位置（因为字符串长度发生了变化）
                    atIndex = usernameStart + 8; // 3 + 4 + 1 = 8
                    modified = true;
                }
            }

            start = atIndex + 1;
        }

        return modified;
    }

    /**
     * 查找邮箱用户名起始位置
     */
    private static int findEmailUsernameStart(StringBuilder text, int atIndex) {
        for (int i = atIndex - 1; i >= 0; i--) {
            char c = text.charAt(i);
            if (!Character.isLetterOrDigit(c) && c != '.' && c != '-' && c != '_') {
                return i + 1;
            }
        }
        return 0;
    }

    /**
     * 脱敏银行卡号
     */
    private static boolean shieldBankCards(StringBuilder text) {
        boolean modified = false;
        int start = 0;

        while (start < text.length() - 12) {
            int cardStart = findBankCardStart(text, start);
            if (cardStart == -1) break;

            int cardLength = getBankCardLength(text, cardStart);
            if (cardLength >= 13 && cardLength <= 19) {
                // 脱敏：保留前4位和后4位
                int maskStart = cardStart + 4;
                int maskEnd = cardStart + cardLength - 4;
                for (int i = maskStart; i < maskEnd; i++) {
                    text.setCharAt(i, '*');
                }
                modified = true;
                start = cardStart + cardLength;
            } else {
                start = cardStart + 1;
            }
        }

        return modified;
    }

    /**
     * 查找银行卡号起始位置
     */
    private static int findBankCardStart(StringBuilder text, int start) {
        for (int i = start; i <= text.length() - 13; i++) {
            if (Character.isDigit(text.charAt(i))) {
                // 检查连续数字长度
                int digitCount = 0;
                int j = i;
                while (j < text.length() && Character.isDigit(text.charAt(j))) {
                    digitCount++;
                    j++;
                }
                if (digitCount >= 13 && digitCount <= 19) {
                    return i;
                }
            }
        }
        return -1;
    }

    /**
     * 获取银行卡号长度
     */
    private static int getBankCardLength(StringBuilder text, int start) {
        int length = 0;
        int i = start;
        while (i < text.length() && Character.isDigit(text.charAt(i))) {
            length++;
            i++;
        }
        return length;
    }
}
