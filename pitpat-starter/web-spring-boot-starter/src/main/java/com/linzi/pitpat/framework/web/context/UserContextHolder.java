package com.linzi.pitpat.framework.web.context;

import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.framework.web.util.HeaderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 从 Request 中解析 header 获得 用户邮箱，保存在上下文中，请求完毕后销毁
 */
@Slf4j
public class UserContextHolder {

    private static final ThreadLocal<Map<String, String>> contextHolder = new InheritableThreadLocal<>();

    //private static final ThreadLocal<Map<String,String>> localeContextHolder =
    //        new NamedThreadLocal<>("UserContextHolder");
    //
    //private static final ThreadLocal<Map<String,String>> inheritableLocaleContextHolder =
    //        new NamedInheritableThreadLocal<>("UserContextHolder");
    public static void clearContext() {
        if (log.isDebugEnabled()) {
            log.debug("clear context email={}", getContext());
        }
        contextHolder.remove();
    }

    /**
     * 返回用户Email
     *
     * @return
     */
    public static String getEmail() {
        return getContext().get("email");
    }

    /**
     * 返回用户语言
     *
     * @return 默认返回en_US
     */
    public static String getLocal() {
        return getContext().get("local");
    }

    /**
     * 返回用户时区
     *
     * @return 默认返回en_US
     */
    public static String getZonedId() {
        return getContext().get("zoneId");
    }

    /**
     * 返回用户版本
     *
     * @return 默认返回null
     */
    public static Integer getAppVersion() {
        String appVersion = getContext().get("appVersion");
        return StringUtils.hasText(appVersion)? Integer.parseInt(appVersion) : null;
    }

    /**
     * 返回用户版本
     *
     * @return 默认返回null
     */
    public static Integer getAppType() {
        String appType = getContext().get("appType");
        return StringUtils.hasText(appType)? Integer.parseInt(appType) : null;
    }

    private static Map<String, String> getContext() {
        Map<String, String> ctx = contextHolder.get();
        if (ctx == null) {
            ctx = new HashMap<>();
            contextHolder.set(ctx);
        }
        if (log.isDebugEnabled()) {
            log.debug("get context email={}", ctx);
        }
        return ctx;
    }

    public static void setContext(HttpServletRequest httpRequest) {
        try {
            setContextEmail(HeaderUtil.getEmail(httpRequest));
            setContextLocal(getLanguage(httpRequest.getHeader(I18nConstant.LANGUAGE_HEAD_PARAM)));
            setContextZoneId(httpRequest.getHeader(I18nConstant.ZONE_ID_HEAD_PARAM));
            setAppVersion(httpRequest.getHeader("appVersion"));
            setAppType(httpRequest.getHeader("appType"));
        } catch (Exception ignored) {
        }
    }

    private static String getLanguage(String language) {
        if (!StringUtils.hasText(language) || language.startsWith("en")) {
            language = I18nConstant.LanguageCodeEnum.en_US.getCode();
        } else if (language.startsWith("fr")) {
            language = I18nConstant.LanguageCodeEnum.fr_CA.getCode();
        } else {
            language = I18nConstant.LanguageCodeEnum.en_US.getCode();
        }
        return language;
    }

    public static void setContextEmail(String context) {
        getContext().put("email", context);
        if (log.isDebugEnabled()) {
            log.info("set context email={}", context);
        }
    }

    public static void setContextLocal(String context) {
        getContext().put("local", Optional.ofNullable(context).orElse(I18nConstant.LanguageCodeEnum.en_US.getCode()));
        if (log.isDebugEnabled()) {
            log.info("set context local={}", context);
        }
    }

    public static void setContextZoneId(String context) {
        getContext().put("zoneId", Optional.ofNullable(context).orElse("-8"));
        if (log.isDebugEnabled()) {
            log.info("set context zoneId={}", context);
        }
    }

    public static void setAppVersion(String context) {
        getContext().put("appVersion", context);
        if (log.isDebugEnabled()) {
            log.info("set context appVersion={}", context);
        }
    }

    public static void setAppType(String context) {
        getContext().put("appType", context);
        if (log.isDebugEnabled()) {
            log.info("set context appType={}", context);
        }
    }
}
