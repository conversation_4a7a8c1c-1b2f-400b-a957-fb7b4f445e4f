package com.linzi.pitpat.framework.web.context;

import brave.Span;
import brave.Tracer;
import brave.propagation.TraceContext;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.net.InetAddress;
import java.util.Arrays;
import java.util.List;

/**
 * Spring Context 工具类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年11月29日 下午11:45:51
 */
@Lazy(value = false)
@Component
public class SpringContextUtils implements ApplicationContextAware {
    public static ApplicationContext applicationContext;
    private static String serverIp;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext)
            throws BeansException {
        SpringContextUtils.applicationContext = applicationContext;
    }

    public static Object getBean(String name) {
        return applicationContext.getBean(name);
    }


    public static boolean hasBean(String name) {
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        List<String> list = Arrays.asList(beanNames);
        return list.contains(name);
    }

    public static <T> T getBean(String name, Class<T> requiredType) {
        return applicationContext.getBean(name, requiredType);
    }

    public static <T> T getBean(Class<T> requiredType) {
        return applicationContext.getBean(requiredType);
    }

    public static boolean containsBean(String name) {
        return applicationContext.containsBean(name);
    }

    public static boolean isSingleton(String name) {
        return applicationContext.isSingleton(name);
    }

    public static Class<? extends Object> getType(String name) {
        return applicationContext.getType(name);
    }

    //获取当前运行时环境
    public static String getActiveProfile() {
        if (applicationContext != null) {
            return applicationContext.getEnvironment().getActiveProfiles()[0];
        }
        return null;
    }

    // 是否线上环境
    public static boolean isOnline() {
        String env = getActiveProfile();
        if (StringUtils.isEmpty(env)) {
            return true;
        }

        if ("pre".equals(env) || "prod".equals(env)) {
            return true;
        }
        return false;
    }

    //是否测试环境
    public static boolean isTest() {
        String env = getActiveProfile();
        if (StringUtils.isEmpty(env) ) {
            return false;
        }
        return env.startsWith("test");
    }

    //是否开发环境
    public static boolean isDev() {
        String env = getActiveProfile();
        if (StringUtils.isEmpty(env)) {
            return false;
        }
        return "dev".equals(env);
    }


    public static String getApplicationName() {
        if (applicationContext != null) {
            return applicationContext.getId();
        }
        return null;
    }

    /**
     * 获取服务器地址
     */
    public static String getServerIp() {
        if (!StringUtils.hasText(serverIp)) {
            try {
                serverIp = InetAddress.getLocalHost().getHostAddress();
            } catch (Exception e) {
                serverIp = "localhost";
            }
        }
        return serverIp;
    }

}
