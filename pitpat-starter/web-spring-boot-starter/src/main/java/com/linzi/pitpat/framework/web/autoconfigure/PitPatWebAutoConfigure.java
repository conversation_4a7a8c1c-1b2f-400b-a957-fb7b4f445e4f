package com.linzi.pitpat.framework.web.autoconfigure;

import com.linzi.pitpat.framework.web.aspect.BaseLogAspect;
import com.linzi.pitpat.framework.web.context.SpringContextUtils;
import com.linzi.pitpat.framework.web.exception.GlobalExceptionHandler;
import com.linzi.pitpat.framework.web.filter.RepeatableFilter;
import com.linzi.pitpat.framework.web.filter.UserContextFilter;
import com.linzi.pitpat.framework.web.property.AwsProperties;
import com.linzi.pitpat.framework.web.util.AwsUtil;
import com.linzi.pitpat.framework.web.util.RestTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.OkHttpClient.Builder;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
@EnableConfigurationProperties(AwsProperties.class)
public class PitPatWebAutoConfigure {

    @ConditionalOnMissingBean({GlobalExceptionHandler.class})
    @Bean
    public GlobalExceptionHandler globalExceptionHandler() {
        return new GlobalExceptionHandler();
    }

    @ConditionalOnMissingBean({SpringContextUtils.class})
    @Bean
    public SpringContextUtils springContextUtils() {
        return new SpringContextUtils();
    }

    @Bean("i18nMessageSourceDirect")
    public ResourceBundleMessageSource i18nMessageSourceDirect() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        messageSource.addBasenames("i18n/messages");
        messageSource.setDefaultEncoding("UTF-8");
        messageSource.setCacheSeconds(-1);
        messageSource.setDefaultLocale(Locale.ENGLISH);
        return messageSource;
    }

    @Bean
    @Lazy(value = false)
    public AwsUtil initAwConfiguration(AwsProperties properties) {
        AwsUtil.setAccessKey(properties.getAccessKey());
        AwsUtil.setSecretKey(properties.getSecretKey());
        AwsUtil.setRegion(properties.getRegion());
        AwsUtil.setBucket(properties.getBucket());
        AwsUtil.setBaseUrl(properties.getBaseUrl());
        return null;
    }


    @Bean
    public FilterRegistrationBean<UserContextFilter> userContextFilter() {
        FilterRegistrationBean<UserContextFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new UserContextFilter());
        registrationBean.addUrlPatterns("/*"); // 设置需要过滤的URL模式
        registrationBean.setOrder(1); // 设置过滤器顺序（可选）
        return registrationBean;
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    @Bean
    public FilterRegistrationBean someFilterRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new RepeatableFilter());
        registration.addUrlPatterns("/*");
        registration.setName("repeatableFilter");
        registration.setOrder(FilterRegistrationBean.LOWEST_PRECEDENCE);
        return registration;
    }

    @Bean
    @ConditionalOnMissingBean(BaseLogAspect.class)
    public BaseLogAspect defaultLogAspect() {
        return new BaseLogAspect(){};  // 创建默认实现类
    }

    @Bean
    @Lazy(value = false)
    @ConditionalOnClass(RestTemplateBuilder.class)
    public RestTemplate restTemplateUtil(RestTemplateBuilder restTemplateBuilder) {
        RestTemplate restTemplate = restTemplateBuilder
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .requestFactory(() -> {
                    OkHttpClient okHttpClient = new Builder()
                            .connectionPool(new ConnectionPool(30, 5, TimeUnit.MINUTES))
                            .addInterceptor(new RetryInterceptor(3, 5))
                            .build();
                    return new OkHttp3ClientHttpRequestFactory(okHttpClient);
                })
                .setReadTimeout(Duration.ofSeconds(30))
                .setConnectTimeout(Duration.ofSeconds(30))
                .build();

        RestTemplateUtil.setRestTemplate(restTemplate);
        for (HttpMessageConverter<?> converter : restTemplate.getMessageConverters()) {
            if (converter instanceof StringHttpMessageConverter) {
                ((StringHttpMessageConverter) converter).setDefaultCharset(StandardCharsets.UTF_8);
            }
        }
        return restTemplate;
    }

    public static class RetryInterceptor implements Interceptor {
        private static final String HEADER_X_RETRY = "X-Retry";
        private static final String HEADER_X_RETRY_DELAY = "X-Retry-Delay";

        private int maxRetry; // 最大重试次数
        private long retryDelay; // 重试延迟时间（毫秒）
        private boolean enableRetry = false; //是否开启重试

        public RetryInterceptor(int maxRetry, int retryDelay) {
            this.maxRetry = maxRetry;
            this.retryDelay = retryDelay;
        }

        @Override
        public Response intercept(Chain chain) throws IOException {
            Request request = chain.request();
            Response response = null;

            String xRetry = request.header(HEADER_X_RETRY);
            String xRetryDelay = request.header(HEADER_X_RETRY_DELAY);

            if (StringUtils.hasText(xRetry) && StringUtils.hasText(xRetryDelay)) {
                try {
                    enableRetry = true;
                    this.maxRetry = Integer.parseInt(xRetry);
                    this.retryDelay = Long.parseLong(xRetryDelay);
                } catch (NumberFormatException e) {
                    log.warn("Invalid X-Retry or X-Retry-Delay header values: {}, {}", xRetry, xRetryDelay);
                }
            }

            int attempt = 0;
            while (attempt < maxRetry) {
                if (attempt > 0) {
                    log.info("retry={}, request={}", attempt, request.url());
                }

                try {
                    return chain.proceed(request);
                } catch (IOException e) {
                    attempt++;
                    if (!enableRetry) {
                        attempt = maxRetry;
                    }
                    if (enableRetry && (attempt >= maxRetry)) {
                        log.info("Request failed after {} attempts for url={}", maxRetry, request.url());
                        throw e;
                    }
                    try {
                        TimeUnit.SECONDS.sleep(retryDelay * attempt);// 等待一段时间后重试
                    } catch (InterruptedException ignored) {
                        Thread.currentThread().interrupt(); // 处理线程中断异常
                    }
                }
            }
            return response;
        }
    }
}
