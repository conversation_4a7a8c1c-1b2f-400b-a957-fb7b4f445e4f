package com.linzi.pitpat.framework.web.task;

import com.alibaba.ttl.TtlRunnable;
import org.slf4j.MDC;
import org.springframework.core.task.TaskDecorator;

import java.util.Map;

public class TransmittableDecorator implements TaskDecorator {
    @Override
    public Runnable decorate(Runnable runnable) {
        Runnable decoratedRunnable = TtlRunnable.get(runnable);  // 在任务执行前获取TTL（ThreadLocal）的值
        // 捕获主线程的 MDC 上下文
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        return () -> {
            try {
                // 在子线程中设置 MDC 上下文
                if (contextMap != null) {
                    MDC.setContextMap(contextMap);
                }
                // 执行原始任务
                decoratedRunnable.run();
            } finally {
                // 清除 MDC 上下文
                MDC.clear();
            }
        };
    }
}
