package com.linzi.pitpat.framework.web.aspect;

import com.linzi.pitpat.core.constants.I18nConstant;
import com.linzi.pitpat.core.util.IpUtil;
import com.linzi.pitpat.core.util.JsonUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 日志切面
 * <AUTHOR>
 * @date 2024/12/27 18:25
 */
@Aspect
@Component
@Order(1)
public abstract class BaseLogAspect {
    protected static final String BLANK_SPACE = "  ";
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Pointcut(value = "(@within(org.springframework.web.bind.annotation.RestController) " +
            "|| @within(org.springframework.stereotype.Controller))")
    public void pointcut() {}

    @Around("pointcut()")
    public Object doAround(ProceedingJoinPoint point) throws Throwable {
        long startTime = System.nanoTime();
        Object result = null;
        StringBuffer sb = new StringBuffer();
        String response = "";

        try {
            // 前置处理
            beforeProceed(point, sb);

            // 记录请求日志
            sb.append(recordRequestLog(point.getArgs()));

            // 执行目标方法
            result = point.proceed();

            // 计算执行时间
            Duration timeTaken = Duration.ofNanos(System.nanoTime() - startTime);
            long cost = timeTaken.toMillis();
            sb.append("请求耗时").append(cost > 2000 ? "高=" : "=").append(cost).append("ms").append(BLANK_SPACE);

            // 记录响应结果
            response = JsonUtil.writeString(result);

        } finally {
            try {
                // 记录完整日志
                sb.append("resp=").append(Optional.ofNullable(response).orElse("EMPTY"));
                logger.info(sb.toString());

                // 后置处理
                afterProceed();
            } catch (Exception e) {
                logger.error("日志记录异常", e);
            }
        }
        return result;
    }

    protected StringBuffer recordRequestLog(Object[] args) {
        StringBuffer sb = new StringBuffer();

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes != null ? attributes.getRequest() : null;

        try {
            List<Object> filteredArgs = filterArgs(args);
            String argsJson = JsonUtil.writeString(filteredArgs.size() == 1 ? filteredArgs.get(0) : filteredArgs);

            // 记录基础信息
            appendBaseInfo(sb, request);

            // 记录扩展信息
            appendExtendInfo(sb, request);

            // 记录参数
            sb.append("params=").append(argsJson).append(BLANK_SPACE);

        } catch (Exception e) {
            logger.error("请求日志记录异常", e);
        }

        return sb;
    }

    protected List<Object> filterArgs(Object[] args) {
        List<Object> filteredArgs = new ArrayList<>();
        if (args != null) {
            for (Object arg : args) {
                if (shouldSkipArg(arg)) {
                    continue;
                }
                filteredArgs.add(arg);
            }
        }
        return filteredArgs;
    }

    protected boolean shouldSkipArg(Object arg) {
        return arg instanceof HttpServletResponse
                || arg instanceof HttpServletRequest
                || arg instanceof MultipartFile
                || arg instanceof MultipartFile[];
    }

    protected void appendBaseInfo(StringBuffer sb, HttpServletRequest request) {
        if (request != null) {
            sb.append("uri=").append(request.getRequestURI()).append(BLANK_SPACE)
                    .append("rmtIP=").append(IpUtil.getRemoteIp(request)).append(BLANK_SPACE)
                    .append("traceId=").append(request.getHeader("x-b3-traceid")).append(BLANK_SPACE)
                    .append("spanId=").append(request.getHeader("x-b3-spanid")).append(BLANK_SPACE)
                    .append("zoneId=").append(request.getHeader(I18nConstant.ZONE_ID_HEAD_PARAM)).append(BLANK_SPACE)
                    .append("language=").append(request.getHeader(I18nConstant.LANGUAGE_HEAD_PARAM)).append(BLANK_SPACE)
                    .append("channelSource=").append(request.getHeader("channelSource")).append(BLANK_SPACE)
                    .append("zoneOffset=").append(request.getHeader(I18nConstant.ZONE_OFFSET_HEAD_PARAM)).append(BLANK_SPACE);
        }
    }


    protected void beforeProceed(ProceedingJoinPoint point, StringBuffer sb) {}

    protected void afterProceed() {}

    protected void appendExtendInfo(StringBuffer sb, HttpServletRequest request) {}
}
