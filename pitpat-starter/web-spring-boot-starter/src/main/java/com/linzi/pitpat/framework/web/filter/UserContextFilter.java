package com.linzi.pitpat.framework.web.filter;

import com.linzi.pitpat.framework.web.context.UserContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 参考 SecurityContextHolderFilter
 *
 */
public class UserContextFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        try {
            // 从请求中获取用户信息（例如从头信息或JWT中获取）
            HttpServletRequest httpRequest = request;
            // 设置上下文
            UserContextHolder.setContext(httpRequest);
            // 继续处理请求
            filterChain.doFilter(request, response);
        } finally {
            UserContextHolder.clearContext();
        }
    }
}
