<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
<!-- 如果需要自定义，可以通过[${applicationName},%X{traceId:-},%X{spanId:-}]三个参数自定义-->
<!--    <springProperty scope="context" name="applicationName" source="spring.application.name" defaultValue="localhost"/>-->
<!--    <property name="CONSOLE_LOG_PATTERN" value="[${applicationName},%X{traceId:-},%X{spanId:-}] ${CONSOLE_LOG_PATTERN:-%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(-&#45;&#45;){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>-->

    <springProfile name="dev">
        <!-- configuration to be enabled when the "dev" profiles are active -->
        <property name="LOG_HOME" value="${user.home}/logs/web-starter-pitpat"/>
    </springProfile>


    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <!--日志文件输出格式-->
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>${CONSOLE_LOG_CHARSET}</charset>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>

</configuration>


