package com.linzi.pitpat.framework.web.util;

import com.inzi.pitpat.framework.WebApplicationTests;
import com.linzi.pitpat.core.util.JsonUtil;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpServer;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.Asserts;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.ResourceAccessException;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static org.junit.jupiter.api.Assertions.assertThrows;

@Slf4j
@SpringBootTest(classes = WebApplicationTests.class)
class RestTemplateUtilTest extends WebApplicationTests {

    private static final String TEST_URL = "http://localhost:8000/test";

    @Test
    void testGet() {
        String s = RestTemplateUtil.get(getRunTimeRequest(1));
        Asserts.check(runTimeResponseCheck(s, 1), "Get请求验证失败");
    }

    @Test
    void testPost() {
        String s = RestTemplateUtil.post(TEST_URL, postRunTimeBody(1));
        Asserts.check(runTimeResponseCheck(s, 1), "Post请求验证失败");
    }

    @Test
    void testGet404() {
        assertThrows(HttpClientErrorException.class, () -> {
            RestTemplateUtil.get("http://localhost:8000/404");
        });
    }

    /**
     * 404 will not retry
     */
    @Test
    void testGetRetry() {
        assertThrows(ResourceAccessException.class, () -> {
            RestTemplateUtil.get(getExceptionRequest(1), Map.of("X-Retry", "3", "X-Retry-Delay", "5"));
        });
    }

    @Test
    void testGetRetryTime() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            RestTemplateUtil.get(getExceptionRequest(1), Map.of("X-Retry", "3", "X-Retry-Delay", "5"));
        } catch (Exception ignored) {
        }
        stopWatch.stop();
        log.info("{}", stopWatch.getTotalTimeSeconds());
        Assertions.assertTrue(stopWatch.getTotalTimeSeconds() >= 15 && stopWatch.getTotalTimeSeconds() <= 16, "GetRetry请求验证失败");
    }

    @Test
    void testPostRetry() {
        long l = System.currentTimeMillis();
        try {
            String s = RestTemplateUtil.postRetry(TEST_URL, postRunTimeBody(31));
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            Asserts.check((System.currentTimeMillis() - l) / 1000 / 30 >= 3, "PostRetry请求验证失败");
        }
    }


    @Test
    void testPut() {
        String s = RestTemplateUtil.put(TEST_URL, postRunTimeBody(1));
        Asserts.check(runTimeResponseCheck(s, 1), "Put请求验证失败");
    }


    private String getRunTimeRequest(int runTime) {
        return TEST_URL + "?runTime=" + runTime;
    }

    private String getExceptionRequest(int runTime) {
        return TEST_URL + "?runTime=" + runTime + "&throwException=true";
    }

    private Map<String, String> postRunTimeBody(int runTime) {
        Map<String, String> map = new HashMap<>();
        map.put("runTime", String.valueOf(runTime));
        return map;
    }

    private boolean runTimeResponseCheck(String resp, int runTime) {
        return resp.contains("runTime\": " + runTime);
    }

    @BeforeAll
    public static void initTemplate() throws InterruptedException {
        new Thread(() -> {
            try {
                new Server().startServer();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }).start();
        Thread.sleep(1000);
    }


    static class Server {

        private static final ConcurrentHashMap<String, Integer> connectionRequests = new ConcurrentHashMap<>();

        public void startServer() throws IOException {
            HttpServer server = HttpServer.create(new InetSocketAddress(8000), 0);
            server.createContext("/test", new StreamBlockingHandler());
            server.start();
            System.out.println("Server started at http://localhost:8000");
        }

        static class StreamBlockingHandler implements HttpHandler {

            @Override
            public void handle(HttpExchange exchange) throws IOException {
                try {
                    // 获取连接ID（实际HTTP/2场景中应该是stream ID）
                    String connectionId = exchange.getRemoteAddress().toString();
                    int requestCount = connectionRequests.merge(connectionId, 1, Integer::sum);
                    String query = exchange.getRequestURI().getQuery();
                    log.info("handle query:{}", query);
                    Map<String, String> requestParam = new HashMap<>();
                    if (StringUtils.hasText(query) && query.contains("&")) {
                        String[] params = query.split("&");
                        for (String param : params) {
                            String[] split = param.split("=");
                            requestParam.put(split[0], split[1]);
                        }
                    }

                    byte[] buf = exchange.getRequestBody().readAllBytes();
                    String request = new String(buf, StandardCharsets.UTF_8);
                    if (StringUtils.hasText(request)) {
                        log.info("request:{}", request);
                        Map<String, String> requestBody = JsonUtil.readValue(request);
                        requestParam.putAll(requestBody);
                    }

                    //是否抛出异常
                    String throwException = requestParam.getOrDefault("throwException", "false");
                    if (Boolean.parseBoolean(throwException)) {
                        int i = 1 / 0;
                    }

                    //根据参数，模拟等待时间
                    String runTimeStr = requestParam.getOrDefault("runTime", "1");
                    if (StringUtils.hasText(runTimeStr)) {
                        Thread.sleep(Integer.parseInt(runTimeStr) * 1000L);
                    } else {
                        Thread.sleep(1000L);
                    }

                    // 正常响应
                    String response = String.format(
                            "{\"connection\": \"%s\", \"request\": %d, \"status\": \"ok\",\"runTime\": %s}",
                            connectionId, requestCount, runTimeStr
                    );
                    exchange.sendResponseHeaders(200, response.length());
                    exchange.getResponseBody().write(response.getBytes());
                } catch (InterruptedException e) {
                    String error = "Request interrupted";
                    exchange.sendResponseHeaders(500, error.length());
                    exchange.getResponseBody().write(error.getBytes());
                } finally {
                    exchange.close();
                }
            }
        }
    }


}
