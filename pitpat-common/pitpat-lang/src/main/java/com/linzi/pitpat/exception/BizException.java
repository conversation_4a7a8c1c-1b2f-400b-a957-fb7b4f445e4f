package com.linzi.pitpat.exception;

import com.linzi.pitpat.lang.Err;
import lombok.Getter;

import java.io.Serial;

/**
 * 业务异常
 */
@Getter
public class BizException extends BaseException {

    @Serial
    private static final long serialVersionUID = 4569031928350301954L;

    public BizException(String message) {
        super(message);
    }

    public BizException(String message, Throwable cause) {
        super(message, cause);
    }

    public BizException(String message, Integer code) {
        super(message, code);
    }

    public BizException(String message, Integer code, Throwable cause) {
        super(message, code, cause);
    }


    public BizException(Err error) {
        super(error);
    }

    public BizException(Err error, Throwable cause) {
        super(error, cause);
    }


}
