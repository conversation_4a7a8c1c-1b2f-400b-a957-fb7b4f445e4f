package com.linzi.pitpat.lang;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 分页对象包装类, 为了区分度高一些，再考虑是否重命名为 PageResult 或 Paging 与 Mybatis Plus 命名规范保持一致
 * 查询参数使用 pageNum, pageSize
 * 返回值仅用到了  total， records
 */
@Getter
@Setter
@NoArgsConstructor
public class Paging<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = 6025854821798097090L;
    /**
     * 当前页数
     */
    private Integer current = 1;

    /**
     * 分页条数
     */
    private Integer size = 10;

    /**
     * 总条数
     */
    private Integer total = 0;

    /**
     * 分页数据
     */
    private List<T> records;

    private Paging(List<T> records, Integer total, Integer page, Integer pageSize) {
        this.records = records;
        this.total = total;
        this.current = page;
        this.size = pageSize;
    }

    public static <T> Paging<T> of(List<T> data, Integer total, PageQuery query) {
        return new Paging<>(data, total, query.getPageNum(), query.getPageSize());
    }


}
