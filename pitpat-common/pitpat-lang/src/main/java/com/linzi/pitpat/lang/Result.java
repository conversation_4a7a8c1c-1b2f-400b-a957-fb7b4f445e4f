package com.linzi.pitpat.lang;


import com.linzi.pitpat.util.NumberAware;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class Result<T> implements Serializable {
    /**
     * 返回码 200成功
     */
    private Integer code;

    /**
     * 返回信息提示
     */
    private String msg;

    /**
     * 响应数据
     */
    private T data;

    public Result(Err err) {
        this(err.getCode(), err.getMsg());
    }

    public Result(Err err, T result) {
        this(err.getCode(), err.getMsg(), result);
    }

    public Result(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
        this.data = null;
    }

    public Result(Integer code, String msg, T result) {
        NumberAware.process(result);
        this.code = code;
        this.msg = msg;
        this.data = result;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public T getData() {
        return data;
    }
}
