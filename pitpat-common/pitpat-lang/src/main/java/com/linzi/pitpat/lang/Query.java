package com.linzi.pitpat.lang;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class Query implements Serializable {

    @Serial
    private static final long serialVersionUID = 7655035628411524742L;
    /**
     * 排序字段 example:  [{column:"id","direction":"asc"}]
     */
    private List<OrderItem> orders;

    public void addOrderByDesc(String column){
        addOrderItem(OrderItem.desc(column));
    }
    public void addOrderByAsc(String column){
        addOrderItem(OrderItem.asc(column));
    }
    public void addOrderItem(OrderItem item){
        if (orders == null) {
            orders = new ArrayList<>();
        }
        orders.add(item);
    }
}
