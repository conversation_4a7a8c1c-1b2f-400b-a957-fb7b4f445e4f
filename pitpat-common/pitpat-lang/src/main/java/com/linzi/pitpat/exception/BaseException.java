package com.linzi.pitpat.exception;

import com.linzi.pitpat.lang.Err;
import lombok.Getter;

import java.io.Serial;

/**
 * 基础异常，抛出 改异常不会发送钉钉通知
 */
@Getter
public class BaseException extends RuntimeException {
    private Integer code = 999;

    private final String message;
    @Serial
    private static final long serialVersionUID = -2323242455352723793L;

    public BaseException(String message) {
        super(message);
        this.message = message;
    }

    public BaseException(String message, Throwable cause) {
        super(message, cause);
        this.message = message;
    }

    public BaseException(String message, Integer code) {
        super(message);
        this.message = message;
        this.code = code;
    }

    public BaseException(String message, Integer code, Throwable cause) {
        super(message, cause);
        this.message = message;
        this.code = code;
    }


    public BaseException(Err error) {
        super(error.getMsg());
        this.message = error.getMsg();
        this.code = error.getCode();
    }

    public BaseException(Err error, Throwable cause) {
        super(error.getMsg(), cause);
        this.message = error.getMsg();
        this.code = error.getCode();
    }

}
