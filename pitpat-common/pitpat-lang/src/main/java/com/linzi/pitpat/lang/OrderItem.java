package com.linzi.pitpat.lang;


import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Objects;

/**
 * 排序字段
 */
@Data
@Accessors(chain = true)
public class OrderItem {
    private static final long serialVersionUID = 1L;
    /**
     * 排序字段
     */
    private String column;
    /**
     * 排序规则
     */
    private String direction = "asc";

    public static OrderItem asc(String column) {
        return build(column, "asc");
    }

    public static OrderItem desc(String column) {
        return build(column, "desc");
    }

    public boolean isAsc() {
        return Objects.equals(direction, "asc");
    }

    private static OrderItem build(String column, String direction) {
        return new OrderItem().setColumn(column).setDirection(direction);
    }


}
