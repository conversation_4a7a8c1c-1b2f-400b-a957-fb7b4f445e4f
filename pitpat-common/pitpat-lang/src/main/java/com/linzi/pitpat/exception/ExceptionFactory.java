package com.linzi.pitpat.exception;

import com.linzi.pitpat.lang.Err;

/**
 * 异常工厂方法
 */
public class ExceptionFactory {

    /**
     * 业务异常
     * @param message 错误信息
     * @return  BizException
     */
    public static BizException bizException(String message) {
        return new BizException(message);
    }

    /**
     * 业务异常
     * @param message 错误信息
     * @param code 错误码
     * @return BizException
     */
    public static BizException bizException(String message, Integer code) {
        return new BizException(message, code);
    }

    /**
     * 业务异常
     * @param err 错误类
     * @return BizException
     */
    public static BizException bizException(Err err) {
        return new BizException(err);
    }

    /**
     * 校验提示异常
     * @param message 错误信息
     * @return  BaseException
     */
    public static BaseException baseException(String message) {
        return new BaseException(message);
    }

    /**
     * 校验提示异常
     * @param message 错误信息
     * @param code 错误码
     * @return BaseException
     */
    public static BaseException baseException(String message, Integer code) {
        return new BaseException(message, code);
    }

    /**
     * 校验提示异常
     * @param err 错误类
     * @return BizException
     */
    public static BaseException baseException(Err err) {
        return new BaseException(err);
    }


    /**
     * i18n 异常
     * @param i18nCode i18n key
     * @return BizI18nException
     */
    public static BizI18nException biz118nException(String i18nCode) {
        return new BizI18nException(i18nCode);
    }

    /**
     * i18n 异常
     * @param i18nCode i18n key
     * @param params i18n 参数
     * @return BizI18nException
     */
    public static BizI18nException biz118nException(String i18nCode, Object... params) {
        return new BizI18nException(i18nCode, params);
    }
}
