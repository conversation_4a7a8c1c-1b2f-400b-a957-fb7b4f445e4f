package com.linzi.pitpat.exception;


import lombok.Getter;

import java.io.Serial;

/**
 * 自定义国际化异常
 *
 * <AUTHOR>
 */
@Getter
public class BizI18nException extends BaseException {

    @Serial
    private static final long serialVersionUID = 4774059060199408238L;

    private final String i18nCode;
    private Object[] params;

    public BizI18nException(String i18NCode) {
        super(i18NCode, 999);
        this.i18nCode = i18NCode;
    }

    public BizI18nException(String i18NCode, Object... params) {
        super(i18NCode, 999);
        this.i18nCode = i18NCode;
        this.params = params;
    }

}
