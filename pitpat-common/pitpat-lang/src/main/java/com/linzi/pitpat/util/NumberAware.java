package com.linzi.pitpat.util;

import com.linzi.pitpat.annotation.NumberScale;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.Objects;

@Slf4j
public class NumberAware {
    /**
     * @param instance clazz
     * @see NumberScale
     * 处理浮点数的精度问题，默认保留2位小数，可以通过 NumberScale scale 控制
     */
    public static void process(Object instance) {
        try {
            processFields(instance);
        } catch (Exception e) {
            log.error("NumberAware process处理错误", e);
        }
    }

    public static void processFields(Object instance) {
        if (instance == null) {
            return;
        }
        if (isJavaBuiltinObject(instance)) {
            log.trace("NumberAware 不支持 {},直接返回", instance.getClass().getSimpleName());
            return;
        }
        //Collection
        if (instance instanceof Collection<?> set) {
            set.forEach(NumberAware::processFields);
            return;
        }

        // Process direct fields
        Field[] fields = instance.getClass().getDeclaredFields();
        for (Field field : fields) {
            //跳过静态字段
            if (Modifier.isStatic(field.getModifiers())) {
                log.trace("ignore static field, name={}", field.getName());
                continue;
            }
            field.setAccessible(true);
            processField(field, instance);
            processCollectionField(field, instance);
        }
    }

    private static void processField(Field field, Object instance) {
        Class<?> type = field.getType();
        if (isProcessableType(type)) {
            try {
                Object value = field.get(instance);
                Object processed = processValue(value, field);
                field.set(instance, processed);
            } catch (IllegalAccessException e) {
                throw new RuntimeException("Failed to process field: " + field.getName(), e);
            }
        }
    }

    private static void processCollectionField(Field field, Object obj) {
        if (Collection.class.isAssignableFrom(field.getType())) {
            try {
                Collection<?> collection = (Collection<?>) field.get(obj);
                if (collection != null) {
                    collection.forEach(NumberAware::processFields);
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException("Failed to process collection field: " + field.getName(), e);
            }
        }
    }

    private static boolean isProcessableType(Class<?> type) {
        return type == BigDecimal.class
                || type == Double.class
                || type == Float.class
                || type == double.class
                || type == float.class;
    }

    private static Object processValue(Object value, Field field) {
        if (value == null) return null;

        NumberScale decimal = field.getAnnotation(NumberScale.class);
        final int scale = Objects.isNull(decimal) ? 2 : decimal.scale();

        if (value instanceof BigDecimal bigDecimal) {
            //-1 不做处理
            return scale == -1 ? bigDecimal : bigDecimal.setScale(scale, RoundingMode.HALF_UP);
        } else if (value instanceof Float) {
            BigDecimal bigDecimal = BigDecimal.valueOf(((Number) value).floatValue());
            return bigDecimal.setScale(scale, RoundingMode.HALF_UP).floatValue();
        } else if (value instanceof Double) {
            BigDecimal bigDecimal = BigDecimal.valueOf(((Number) value).doubleValue());
            return bigDecimal.setScale(scale, RoundingMode.HALF_UP).doubleValue();
        }

        return value;
    }

    /**
     * 判断是否java 内置对象，如果是则不进行处理，理论上这里指处理业务对象
     *
     * @param instance
     * @return
     */
    public static boolean isJavaBuiltinObject(Object instance) {
        if (instance == null) return false;
        if (instance instanceof Collection<?>) return false; //排除Collection
        String packageName = instance.getClass().getPackage().getName();
        return packageName.startsWith("java.") || packageName.startsWith("javax.");
    }
}
