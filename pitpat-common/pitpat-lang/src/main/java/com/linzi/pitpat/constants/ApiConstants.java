/*

 * @description:

 *

 * projectName: pitpat-server

 * fileName: ApiConstants.java

 * date 2021-09-30

 * copyright(c) 2018-2020 杭州霖扬网络科技有限公司版权所有

 */

package com.linzi.pitpat.constants;


/**
 * description:
 *
 * <AUTHOR>
 * <p>
 * className ApiConstants
 * <p>
 * version V1.0
 * @date 2021-09-30
 **/
public class ApiConstants {

	/**
	 * app登录token缓存
	 */
	public static final String APP_LOGIN_TOKEN_KEY = "zns_login_";

	/**
	 * 注册redis锁
	 */
	public static final String REGISTER_LOCK = "zns_register_lock_";

	/**
	 * 重置密码redis锁
	 */
	public static final String RESET_PWD_LOCK = "reset_pwd_lock_";

	/**
	 * 发送邮箱redis锁
	 */
	public static final String SEND_MAIL_LOCK = "send_mail_lock_";

	/**
	 * 发送邮箱次数，每天限制5次
	 */
	public static final String SEND_MAIL_COUNT_KEY = "send_mail_count";

	/**
	 * 用于验证重置密码的token有效性,token24小时内有效
	 */
	public static final String SEND_MAIL_TOKEN_KEY = "send_mail_token";
	/**
	 * 重置密码输入无效key的次数,超过150次用户当天被禁
	 */
	public static final String RESET_PASSWORD_INVALID_COUNT_KEY = "reset_pwd_invalid_tokens_";
	/**
	 * 用户绑定设备锁
	 */
	public static final String USER_BIND_TREADMILL = "user_bind_treadmill_";
	/**
	 * 用户参与课程锁
	 */
	public static final String USER_PARTAKE_COURSE = "user_partake_course_";
	/**
	 * 用户参与课程锁
	 */
	public static final String PARTAKE_COURSE = "partake_course_";
	/**
	 * 用户跑步开始锁
	 */
	public static final String USER_COURSE_RUN = "user_course_run_";
	/**
	 * 用户发起组队活动锁
	 */
	public static final String RUN_ACTIVITY_LAUNCH = "run_activity_launch_";
	/**
	 * 活动结束锁
	 */
	public static final String RUN_ACTIVITY_END = "run_activity_end_new_";
	/**
	 * 活动结束锁
	 */
	public static final String RUN_ACTIVITY_USER_END = "run_activity_user_end_";
	/**
	 * 活动里程配置key-后台用
	 */
	public static final String MILEAGE = "mileage";
	/**
	 * 活动里程配置key
	 */
	public static final String MILEAGE_LIST = "mileageList";
	/**
	 * 活动跑步时长配置key
	 */
	public static final String RUN_TIME = "runTime";
	/**
	 * 活动里程配置key-后台用
	 */
	public static final String MILES = "miles";
	/**
	 * 活动里程配置key
	 */
	public static final String MILE_LIST = "mileList";
	/**
	 * 活动保证金配置key（美金）
	 */
	public static final String MARGIN = "margin";
	/**
	 * 活动保证金配置key(加币)
	 */
	public static final String MARGIN_CAD = "margin_CAD";
	/**
	 * 活动保证金配置key
	 */
	public static final String COST = "cost";

	/**
	 * 活动保证积分配置key
	 */
	public static final String SCORE_COST = "scoreCost";
	/**
	 * 活动参与补贴金额配置key
	 */
	public static final String PARTICIPATE_AWARD = "participateAward";
	/**
	 * 活动参与补贴金额配置key
	 */
	public static final String INITIATE_REWARDS_MULTIPLE = "initiateRewardsMultiple";
	/**
	 * 活动完成奖励金额配置key
	 */
	public static final String COMPLETE_AWARD = "completeAward";
	/**
	 * 活动完赛奖励-每公里
	 */
	public static final String COMPLETE_AWARD_PER_KM = "completeAwardPerKm";
	/**
	 * 活动完赛奖励-每英里
	 */
	public static final String COMPLETE_AWARD_PER_MILES = "completeAwardPerMiles";
	/**
	 * 最大奖励限制
	 */
	public static final String MAX_AWARD_LIMIT = "maxAwardLimit";
	/**
	 * 活动完成第一名额外奖励key
	 */
	public static final String FIRST_AWARD = "firstAward";
	/**
	 * 活动完成第二名额外奖励key
	 */
	public static final String SECOND_AWARD = "secondAward";
	/**
	 * 活动完成第三名额外奖励key
	 */
	public static final String THIRD_AWARD = "thirdAward";
	/**
	 * 活动完成第一名额外奖励key
	 */
	public static final String FIRST_AWARD_rate = "firstAwardRate";
	/**
	 * 活动完成第二名额外奖励比例
	 */
	public static final String SECOND_AWARD_rate = "secondAwardRate";
	/**
	 * 活动完成第三名额外奖励比例
	 */
	public static final String THIRD_AWARD_rate = "thirdAwardRate";

	/**
	 * 最大额外奖励限制（名次合计奖励）
	 */
	public static final String MAX_EXTRA_AWARD_LIMIT = "maxExtraAwardLimit";
	/**
	 * 活动胜者奖励金额配置key
	 */
	public static final String WINNER_AWARD = "winnerAward";
	/**
	 * 额外奖励计算基数
	 */
	public static final String BASE_EXTRA_AWARD = "baseExtraAward";
	/**
	 * 活动宣传图
	 */
	public static final String ADVERTISING_IMAGE = "advertisingImage";
	/**
	 * 宣传视频
	 */
	public static final String ADVERTISING_VIDEO = "advertisingVideo";
	/**
	 * 活动宣传图
	 */
	public static final String POP_IMAGE = "popImage";
	/**
	 * 活动详情图
	 */
	public static final String DETAILS_IMAGE = "detailsImage";
	/**
	 * 背景图
	 */
	public static final String BACKGROUND_IMAGE = "backgroundImage";
	/**
	 * 宣传配置图
	 */
	public static final String ADVERTISING_CONFIG_IMAGE = "advertisingConfigImage";
	/**
	 * 封面图
	 */
	public static final String COVER_IMAGE = "coverImage";
	/**
	 * 标签图
	 */
	public static final String TAG_IMAGE = "tagImage";
	/**
	 * 标签
	 */
	public static final String TAG_TEXT = "tagText";
	/**
	 * 活动介绍
	 */
	public static final String ACTIVITY_INTRODUCE = "activityIntroduce";
	/**
	 * 活动规则
	 */
	public static final String ACTIVITY_RULE = "activityRule";
	/**
	 * 活动要求
	 */
	public static final String ACTIVITY_REQUIRE = "activityRequire";
	/**
	 * 官方赛事活动完赛奖励
	 */
	public static final String ACTIVITY_COMPLETE_AWARD = "activityCompleteAward";
	/**
	 * 官方赛事活动挑战榜中人奖励
	 */
	public static final String ACTIVITY_RANK_AWARD = "activityRankAward";
	/**
	 * 官方赛事活动挑战成功奖励金额配置
	 */
	public static final String OFFICIAL_CHALLENGE_AWARD = "officialChallengeAward";
	public static final String OFFICIAL_CHALLENGE_COUPON_AWARD = "officialChallengeCouponAward";
	public static final String OFFICIAL_CHALLENGE_SCORE_AWARD = "officialChallengeScoreAward";
	/**
	 * 官方赛事活动挑战失败奖励金额配置
	 */
	public static final String CHALLENGE_FAILURE_AWARD = "challengeFailureAward";
	public static final String CHALLENGE_FAILURE_SCORE_AWARD = "challengeFailureScoreAward";
	public static final String CHALLENGE_FAILURE_COUPON_AWARD = "challengeFailureCouponAward";
	/**
	 * 官方赛事挑战要花费的积分
	 */
	public static final String CHALLENGE_SCORE_CONSUME = "challengeScoreConsumer";
	/**
	 * 官方赛事活动活动结束奖励金额配置
	 */
	public static final String OFFICIAL_EVENT_AWARD = "officialEventAward";

	/**
	 * 官方赛事活动活动结束奖励金额配置
	 */
	public static final String OFFICIAL_EVENT_SCORE_AWARD = "officialEventScoreAward";

	/**
	 * 官方赛事活动活动结束奖励金额配置
	 */
	public static final String OFFICIAL_EVENT_COUPON_AWARD = "officialEventCouponAward";

	/**
	 * 官方赛事活动被挑战奖励金额配置
	 */
	public static final String BE_CHALLENGED_AWARD = "beChallengedAward";
	public static final String BE_CHALLENGED_SCORE_AWARD = "beChallengedScoreAward";
	public static final String BE_CHALLENGED_COUPON_AWARD = "beChallengedCouponAward";
	/**
	 * 官方累计跑赛事活动每日里程限制
	 */
	public static final String DAILY_MILEAGE_LIMIT = "dailyMileageLimit";
	/**
	 * 官方累计跑赛事活动每日时长限制
	 */
	public static final String DAILY_RUN_TIME_LIMIT = "dailyRunTimeLimit";
	/**
	 * 温馨提示
	 */
	public static final String WARM_PROMPT = "warmPrompt";
	/**
	 * 组队跑开始跑步后多少分钟不能进入活动中(配置分钟)
	 */
	public static final String TEAM_ACTIVITY_LAST_ENTER = "teamRunLastEnter";

	/**
	 * 开始跑前多少分钟可入场(配置分钟)
	 */
	public static final String ACTIVITY_BEFORE_ENTER = "runBeforeEnter";
	/**
	 * 挑战跑开始跑前多少分钟可入场(配置分钟)
	 */
	public static final String CHALLENGE_ACTIVITY_BEFORE_ENTER = "challengeRunBeforeEnter";
	/**
	 * 限速配置
	 */
	public static final String RATE_LIMITING_CONFIG = "rateLimitingConfig";
	/**
	 * 限速配置（英里）
	 */
	public static final String RATE_LIMITING_MILE_CONFIG = "rateLimitingMileConfig";
	/**
	 * 跑步目标奖励规则
	 */
	public static final String RUNNING_GOALS = "runningGoals";
	/**
	 * 跑步目标奖励规则
	 */
	public static final String RUNNING_GOALS_AWARD = "runningGoalsAward";


	/**
	 * 跑步目标奖励规则
	 */
	public static final String RUNNING_GOAL_SCORE_AWARD = "runningGoalScoreAward";
	/**
	 * 机器人接受保证金邀请
	 */
	public static final String ACCEPT_FEE_INVITE = "robotAcceptFeeInvite";

	/**
	 * 跑步目标奖励规则
	 */
	public static final String RUNNING_GOAL_COUPON_AWARD = "runningGoalCouponAward";

	/**
	 *
	 * 路线缩略图
	 */
	public static final String ROUTE_THUMBNAIL = "routeThumbnail";

	/**
	 *
	 * 道具赛路线缩略图
	 */
	public static final String PROP_THUMBNAIL = "propThumbnail";
	/**
	 * 路线宣传图
	 */
	public static final String ROUTE_MAP = "routeMap";
	/**
	 * 全局路线图
	 */
	public static final String ROUTE_GLOBAL_MAP = "globalRouteMap";
	/**
	 * 路线一句话描述
	 */
	public static final String ROUTE_ONE_SENTENCE_DESC = "oneSentenceDesc";
	/**
	 * 路线一段话描述
	 */
	public static final String ROUTE_ONE_PARAGRAPH_DESC = "oneParagraphDesc";
	/**
	 * 路线一段话描述
	 */
	public static final String ROUTE_GIF = "routeGif";
	/**
	 * 发送邮箱redis锁
	 */
	public static final String PAY_SEND_MAIL_LOCK = "pay_send_mail_lock_";
	/**
	 * 发送邮箱次数，每天限制5次
	 */
	public static final String PAY_SEND_MAIL_COUNT_KEY = "pay_send_mail_count";

	/**
	 * 用于验证重置密码的token有效性,token24小时内有效
	 */
	public static final String PAY_SEND_MAIL_TOKEN_KEY = "pay_send_mail_token";
	/**
	 * 非官方同人同跑机器人限制
	 */
	public static final String TEAM_USER_LIMIT = "userLimit";

	public static final String API_SERVER_NAME_TEST_1 = "tkjapi.yijiesudai.com";
	public static final String API_SERVER_NAME_TEST_2 = "tkjapi2.yijiesudai.com";
	public static final String API_SERVER_NAME_ONLINE_1 = "api.pitpatfitness.com";
	public static final String API_SERVER_NAME_ONLINE_2 = "pitpat.pitpatfitness.com";


	/**
	 * app登录token缓存
	 */
	public static final String APP_LOGIN_TOKEN_USER_ID_KEY = "APP_LOGIN_TOKEN_USER_ID_KEY";
	/**
	 * 注册验证码缓存,token0.5小时内有效
	 */
	public static final String REGISTER_CODE_SEND_MAIL_TOKEN_KEY = "register_code_send_mail_token:";
	/**
	 * 绑定邮箱验证码缓存,token
	 */
	public static final String BIND_EMAIL_SEND_CODE_TOKEN = "bind_email_send_code_token:";
	/**
	 * 注册验证码缓存输入无效key的次数,超过5次用户当天被禁
	 */
	public static final String REGISTER_CODE_SEND_MAIL_INVALID_COUNT_KEY = "register_code_send_mail_invalid_count_key:";
	/**
	 * 发送邮箱redis锁
	 */
	public static final String REGISTER_CODE_SEND_MAIL_LOCK = "register_code_send_mail_lock:";
	/**
	 * 发送邮箱次数，每天限制5次
	 */
	public static final String REGISTER_CODE_SEND_MAIL_COUNT_KEY = "register_code_send_mail_count:";
	/**
	 * 累计跑优惠券奖励
	 */
	public static String milepostCouponAward = "milepostCouponAward";
	/**
	 * 累计跑服装奖励
	 */
	public static String milepostWearsAward = "milepostWearsAward";

	/**
	 * 俱乐部群聊id前缀
	 */
	public static final String clubGroupId = "@pitpat#club_";
	/**
	 * 活动结束锁
	 */
	public static final String HANDLE_CUMULATIVE_RUN_END = "handle_cumulative_run_end_";

	//活动参赛包是否装扮
	public static String ACTIVITY_APPLICATION_AWARD_DRESS = "activity_Application_Award_Dress";

	/**
	 * 忘记密码邮箱模板业务key
	 */
	public static final String FORGET_PASSWORD_EMAIL_TEMPLATE_KEY = "forget_password";

	public static final String NEW_USER_JOIN_CLUB = "new_user_join_club";

}
