package com.linzi.pitpat.lang;

/**
 * 枚举基础类，错误消息和所有枚举均继承此类，由于 java 语言限制，枚举基类只能设设计为接口，请自行保证字类一定是枚举类型。
 *
 * @param <T>
 */
public interface IEnum<T> {

    /**
     * 返回枚举的code。需要英文时建议使用 name。
     *
     * @return code
     */
    T getCode();

    /**
     * 返回枚举的名称。
     *
     * @return name
     */
    String getName();

    /**
     * 返回枚举的备注，可为空
     * @return remark
     */
    default String getRemark() {
        return getName();
    }

}
