package com.linzi.pitpat.util;

import java.util.Arrays;
import java.util.Objects;

/**
 * date source : https://ipsw.me/
 */
public enum IPhoneType {
    TYPE1("iPhone1,1","iPhone 2G"),
    TYPE2("iPhone1,2","iPhone 3G"),
    <PERSON><PERSON><PERSON><PERSON>("iPhone2,1","iPhone 3GS"),
    <PERSON><PERSON><PERSON><PERSON>("iPhone3,1","iPhone 4"),
    <PERSON><PERSON><PERSON><PERSON>("iPhone3,2","iPhone 4"),
    <PERSON><PERSON><PERSON><PERSON>("iPhone3,3","iPhone 4"),
    <PERSON><PERSON><PERSON><PERSON>("iPhone4,1","iPhone 4S"),
    T<PERSON>E8("iPhone5,1","iPhone 5"),
    <PERSON><PERSON><PERSON>9("iPhone5,2","iPhone 5"),
    T<PERSON><PERSON>10("iPhone5,3","iPhone 5c"),
    <PERSON><PERSON><PERSON>11("iPhone5,4","iPhone 5c"),
    <PERSON><PERSON><PERSON><PERSON>("iPhone6,1","iPhone 5s"),
    T<PERSON><PERSON>13("iPhone6,2","iPhone 5s"),
    <PERSON><PERSON><PERSON>14("iPhone7,1","iPhone 6 Plus"),
    TYP<PERSON>15("iPhone7,2","iPhone 6"),
    T<PERSON><PERSON><PERSON>("iPhone8,1","iPhone 6s"),
    <PERSON><PERSON><PERSON><PERSON>("iPhone8,2","iPhone 6s Plus"),
    T<PERSON>E18("iPhone8,4","iPhone SE"),
    TYPE19("iPhone9,1","iPhone 7"),
    TYPE20("iPhone9,2","iPhone 7 Plus"),
    TYPE21("iPhone10,1","iPhone 8"),
    TYPE22("iPhone10,4","iPhone 8"),
    TYPE23("iPhone10,2","iPhone 8 Plus"),
    TYPE24("iPhone10,5","iPhone 8 Plus"),
    TYPE25("iPhone10,3","iPhone X"),
    TYPE26("iPhone10,6","iPhone X"),
    TYPE27("iPhone11,8","iPhone XR"),
    TYPE28("iPhone11,2","iPhone XS"),
    TYPE29("iPhone11,6","iPhone XS Max"),
    TYPE30("iPhone11,4","iPhone XS Max"),
    TYPE31("iPhone12,1","iPhone 11"),
    TYPE32("iPhone12,3","iPhone 11 Pro"),
    TYPE33("iPhone12,5","iPhone 11 Pro Max"),
    TYPE34("iPhone12,8","iPhone SE(2nd generation)"),
    TYPE35("iPhone13,1","iPhone 12 mini"),
    TYPE36("iPhone13,2","iPhone 12"),
    TYPE37("iPhone13,3","iPhone 12 Pro"),
    TYPE38("iPhone13,4","iPhone 12 Pro Max"),
    TYPE39("iPhone14,4","iPhone 13 mini"),
    TYPE40("iPhone14,5","iPhone 13"),
    TYPE41("iPhone14,2","iPhone 13 Pro"),
    TYPE42("iPhone14,3","iPhone 13 Pro Max"),
    TYPE43("iPhone14,6","iPhone SE (3rd generation)"),
    TYPE44("iPhone14,7","iPhone 14"),
    TYPE45("iPhone14,8","iPhone 14 Plus"),
    TYPE46("iPhone15,2","iPhone 14 Pro"),
    TYPE47("iPhone15,3","iPhone 14 Pro Max"),
    TYPE470("iPhone15,4","iPhone 15"),
    TYPE471("iPhone15,5","iPhone 15 Plus"),
    TYPE472("iPhone16,1","iPhone 15 Pro"),
    TYPE473("iPhone16,2","iPhone 15 Pro Max"),

    TYPE48("iPod1,1","iPod Touch 1G"),
    TYPE49("iPod2,1","iPod Touch 2G"),
    TYPE50("iPod3,1","iPod Touch 3G"),
    TYPE51("iPod4,1","iPod Touch 4G"),
    TYPE52("iPod5,1","iPod Touch 5G"),
    TYPE53("iPad1,1","iPad 1G"),
    TYPE54("iPad2,1","iPad 2"),
    TYPE55("iPad2,2","iPad 2"),
    TYPE56("iPad2,3","iPad 2"),
    TYPE57("iPad2,4","iPad 2"),
    TYPE58("iPad2,5","iPad Mini 1G"),
    TYPE59("iPad2,6","iPad Mini 1G"),
    TYPE60("iPad2,7","iPad Mini 1G"),
    TYPE61("iPad3,1","iPad 3"),
    TYPE62("iPad3,2","iPad 3"),
    TYPE63("iPad3,3","iPad 3"),
    TYPE64("iPad3,4","iPad 4"),
    TYPE65("iPad3,5","iPad 4"),
    TYPE66("iPad3,6","iPad 4"),
    TYPE67("iPad4,1","iPad Air"),
    TYPE68("iPad4,2","iPad Air"),
    TYPE69("iPad4,3","iPad Air"),
    TYPE70("iPad4,4","iPad Mini 2G"),
    TYPE71("iPad4,5","iPad Mini 2G"),
    TYPE72("iPad4,6","iPad Mini 2G"),
    TYPE73("i386","iPhone Simulator"),
    TYPE74("x86_64","iPhone Simulator"),
    ;

    private final String iPhoneType;

    private final String iPhoneName;

    IPhoneType(String iPhoneType, String iPhoneName) {
        this.iPhoneType = iPhoneType;
        this.iPhoneName = iPhoneName;
    }

    public String getiPhoneType() {
        return iPhoneType;
    }

    public String getiPhoneName() {
        return iPhoneName;
    }

    public static String getNameByType (String type) {
        IPhoneType phoneType = Arrays.asList(IPhoneType.values()).stream().filter(e -> e.getiPhoneType().equals(type)).findFirst().orElse(null);
        if (Objects.nonNull(phoneType)) {
            return phoneType.getiPhoneName();
        }
        return "";
    }
}
