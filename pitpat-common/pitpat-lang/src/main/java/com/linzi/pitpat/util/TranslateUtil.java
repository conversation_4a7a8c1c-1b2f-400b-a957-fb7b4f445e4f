package com.linzi.pitpat.util;
import com.linzi.pitpat.annotation.HasDecimals;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 对浮点数据进行精度截取
 * @see NumberAware
 */
@Deprecated
@Slf4j
public class TranslateUtil {

    private final static Map<String, String> data = new HashMap<>();
    private final static Map<List<Object>, List<Object>> match = new HashMap<>();

    private static final List<Class<?>> primitiveTypes = new ArrayList<>(32);

    static {
        try {
            primitiveTypes.add(Boolean.class);
            primitiveTypes.add(Byte.class);
            primitiveTypes.add(Character.class);
            //primitiveTypes.add(Double.class);
            //primitiveTypes.add(Float.class);
            primitiveTypes.add(Integer.class);
            primitiveTypes.add(Long.class);
            primitiveTypes.add(Short.class);
            //primitiveTypes.add(BigDecimal.class);

            primitiveTypes.add(boolean.class);
            primitiveTypes.add(byte.class);
            primitiveTypes.add(char.class);
            //primitiveTypes.add(double.class);
            //primitiveTypes.add(float.class);
            primitiveTypes.add(int.class);
            primitiveTypes.add(long.class);
            primitiveTypes.add(short.class);
            primitiveTypes.add(Date.class);
            primitiveTypes.add(java.sql.Date.class);

            primitiveTypes.addAll(Arrays.asList(new Class<?>[]{
                    boolean[].class, byte[].class, char[].class, double[].class,
                    float[].class, int[].class, long[].class, short[].class}));

            primitiveTypes.addAll(Arrays.asList(new Class<?>[]{
                    Boolean[].class, Byte[].class, Character[].class, Double[].class,
                    Float[].class, Integer[].class, Long[].class, Short[].class}));
        } catch (Exception e) {
            log.error("TranslateUtil异常", e);
        }
    }


    /**
     *
     * @param obj
     * @param hasDecimals BigDecimal 是否保留小数，true ：保留，false：默认两位小数
     * @param <E>
     * @return
     */
    public static <E> E translate(Object obj,Boolean ... hasDecimals) {
        try {
            if (obj == null) {
                return null;
            }
            // 如果是基本数据类型
            Class clazz = obj.getClass();
            if (isNotEnType(clazz)) {
                return (E) obj;
            }
            // 如果是 可转换类型
            if (clazz == String.class
                    || clazz == Double.class || clazz == double.class
                    || clazz == Float.class || clazz == float.class
                    || clazz == BigDecimal.class) {
                Object v = getProperties(clazz, obj,hasDecimals);
                return v != null ? (E) v : (E) obj;
            } else if (clazz == String[].class) {
                String[] s = (String[]) obj;
                String list[] = new String[s.length];
                for (int i = 0; i < s.length; i++) {
                    list[i] = translate(s[i]);
                }
                return (E) list;
            } else if (clazz == Map.class || clazz == HashMap.class) {
                Map<Object, Object> data = (Map) obj;
                for (Map.Entry<Object, Object> map : data.entrySet()) {
                    Object value = map.getValue();
                    data.put(map.getKey(), translate(value));
                }
                return (E) data;
            } else if (clazz == List.class || clazz == ArrayList.class) {
                List<Object> n = new ArrayList<>();
                for (Object o : (List) obj) {
                    n.add(translate(o));
                }
                return (E) n;
            } else if (clazz == Set.class || clazz == HashSet.class) {
                List<Object> n = new ArrayList<>();
                for (Object o : (Set) obj) {
                    n.add(translate(o));
                }
            }
            // 是对象类型
            List<Field> fields = new ArrayList<>();
            getAllField(fields, clazz);
            if (fields.size() > 0) {
                for (Field field : fields) {
                    field.setAccessible(true);
                    Object v = field.get(obj);
                    if (field.isAnnotationPresent(HasDecimals.class)){
                        //保留小数
                        field.set(obj, translate(v,true));
                    }else {
                        field.set(obj, translate(v));
                    }

                }
            }
        } catch (IllegalAccessException e) {
            log.error("TranslateUtil异常", e);
        }
        return (E) obj;
    }

    private static boolean isNotEnType(Class clazz) {
        return primitiveTypes.contains(clazz) ? true : false;
    }


    // getFields方法 , 返回一个Field类型数组，其中包含当前类的public字段，如果此类继承于某个父类，同事包括父类的public字段。
    // 其它的proteced和private字段，无论是属于当前类还是父类都不被此方法获取。
    // getDeclareFields方法
    // 返回一个Field类型数组，结果包含当前类的所有字段，private、protected、public或者无修饰符都在内。另外，此方法返回的结果不包括父类的任何字段。 此方法只是针对当前类的。
    public static void getAllField(List<Field> list, Class returnClass) {
        if (returnClass == Object.class || returnClass == null) {
            return;
        }
        if (!returnClass.getName().startsWith("com.linzi.pitpat")) {
            return;
        }
        Field[] fields = returnClass.getDeclaredFields();

        for (Field field : fields) {
            if (Modifier.isFinal(field.getModifiers())) {    //如果是静态类型
                continue;
            }

            // 如果是字符串类型 ， 或非基本数据类型，则进行处理
            if (!isNotEnType(field.getType())) {
                list.add(field);
            }
        }
        getAllField(list, returnClass.getSuperclass());
    }


    public static Object getProperties(Class clazz, Object key,Boolean ... hasDecimals) {
        if (clazz == String.class) {
            return  data.get(key);
            //  Object obj =  data.get(key);
            //if(obj == null){
//                return getProperties(key.toString());
            //          }
            //        return obj;
        } else if (clazz == Double.class || clazz == double.class) {
            Double value = objToDoubleDefault(key, null);
            if (value != null) {
                return removeEndOfZero(getScale(new BigDecimal(value), 2)).doubleValue();
            }
        } else if (clazz == Float.class || clazz == float.class) {
            Float value = objToFloatDefault(key, null);
            if (value != null) {
                return removeEndOfZero(getScale(new BigDecimal(value), 2)).floatValue();
            }
        } else if (clazz == BigDecimal.class) {
            BigDecimal value = objToBigDecimalDefault(key, null);
            if (hasDecimals != null && hasDecimals.length >0  && hasDecimals[0]) {
                //保留小数
                return value;
            }else {
                //默认两位小数
                return removeEndOfZero(getScale(value, 2));
            }
        }
        return null;
    }




    public static String getProperties(String key) {
        if (key == null || key == "") {
            return key;
        }
        String result = data.get(key);
        if (result == null) {
            char origin[] = key.toCharArray();
            for (Map.Entry<List<Object>, List<Object>> entry : match.entrySet()) {
                List<Object> listKey = entry.getKey();
                List<Object> listValue = entry.getValue();
                StringBuilder sb = new StringBuilder();
                int offset = 0;
                start:
                for (int j = 0; j < listKey.size(); j++) {
                    Object obj = listKey.get(j);
                    if (obj instanceof String) {
                        if (listKey.size() - 1 == j) {
                            for (int z = offset; z < origin.length; z++) {
                                sb.append(origin[offset]);
                            }
                            return sb.toString();
                        }
                    } else {
                        char target[] = (char[]) obj;
                        // 如果是第一个值
                        if (j == 0) {
                            for (int i = 0; i < target.length; i++) {
                                if (target[i] != origin[i]) {
                                    break start;
                                } else {
                                    offset++;
                                }
                            }
                        } else {
                            boolean blag = true;
                            for (int i = 0; i < target.length; i++) {
                                for (int k = offset; k < origin.length; k++) {
                                    offset++;
                                    if (target[i] != origin[k]) {
                                        if (blag == false) {
                                            break start;
                                        }
                                        sb.append(origin[k]);
                                    } else {
                                        blag = false;
                                        i++;
                                        if (i > target.length - 1) {
                                            break;
                                        }
                                    }

                                }
                                if (i == target.length - 1 && blag) { //如果匹配到最后一个，还没有匹配到，则说明不匹配
                                    break start;
                                }
                            }
                        }
                        sb.append(listValue.get(j));
                        if (j == listKey.size() - 1 && offset == origin.length) {
                            return sb.toString();
                        }
                    }


                }

            }
        }
        return result;
    }

    public static Double objToDoubleDefault(Object obj, Double defaultValue) {
        if (null == obj) {
            return defaultValue;
        }
        try {
            return Double.parseDouble(obj.toString());
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static float objToFloatDefault(Object obj, Float defaultValue) {
        if (null == obj) {
            return defaultValue;
        }
        try {
            return Float.parseFloat(obj.toString());
        } catch (Exception e) {
            return defaultValue;
        }
    }
    public static BigDecimal objToBigDecimalDefault(Object obj, BigDecimal defaultValue) {
        if (null == obj) {
            return defaultValue;
        }
        try {
            return new BigDecimal(obj.toString());
        } catch (Exception e) {
            return defaultValue;
        }
    }
    public static BigDecimal removeEndOfZero(BigDecimal value) {
        if (value == null) {
            return null;
        }
        String a = value + "";
        int index = a.indexOf(".");
        if (index == -1) {
            return value;
        }
        if (a.endsWith("0")) {
            char[] b = a.toCharArray();
            // 如果以0结尾，从最后一个小数点向前数，看0的个数
            int j = 0;
            for (int i = b.length - 1; i >= 0; i--) {
                if (b[i] != '0') {
                    break;
                }
                j++;
            }
            int dot = a.length() - index - 1;
            int scale = dot - j;
            return getScale(value, scale);
        }
        return value;
    }
    /**
     * @param value
     * @param scale 保留几位有效果数字
     * @return
     */
    public static BigDecimal getScale(BigDecimal value, int scale){
        if(value == null){
            return BigDecimal.ZERO;
        }
        return  value.setScale(scale, BigDecimal.ROUND_HALF_UP);
    }

    public static void main(String[] args) {
        Map<String, Object> map = new HashMap<>();

        map.put("country", "中国");
        map.put("province", "杭州");
        map.put("c1",new BigDecimal(500));

        map.put("BigDecimal",new BigDecimal(0.70623));
        map.put("d",0.70123d);
        map.put("f",0.70123f);
        map.put("new Date()",new Date());

        HashMap<String,Object> objectHash = new HashMap<>();
        objectHash.put("h",new BigDecimal(0.70623));
        map.put("g", objectHash);

        List<Object> list = new ArrayList<>();
        list.add(map);

        list  = translate(list);
        System.out.println(list);

        RunningLevelVo runningLevelVo = new RunningLevelVo(new BigDecimal("0.5532"), new BigDecimal("0.5536"),new BigDecimal("0.5536"),new BigDecimal("0.5536"));
        List<RunningLevelVo> list1 = new ArrayList<>();
        list1.add(runningLevelVo);
        RunningLevelVo translate = translate(runningLevelVo);
        List<RunningLevelVo> translate1 = translate(list1);

        System.out.println(translate);
        System.out.println(translate1);

        String url = "222.2222";
        translate(url);
        log.info("url={}", url);

    }





    @Data
    public static class RunningLevelVo {
        public RunningLevelVo(BigDecimal exceedRate, BigDecimal beforeCapabilityValue, BigDecimal capabilityValue, BigDecimal changeRate) {
            this.exceedRate = exceedRate;
            this.beforeCapabilityValue = beforeCapabilityValue;
            this.capabilityValue = capabilityValue;
            this.changeRate = changeRate;
        }

        @HasDecimals
        private BigDecimal exceedRate;

        private BigDecimal beforeCapabilityValue;

        private BigDecimal capabilityValue;

        @HasDecimals
        private BigDecimal changeRate;
    }


}
