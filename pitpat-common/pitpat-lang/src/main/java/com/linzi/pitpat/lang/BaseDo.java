package com.linzi.pitpat.lang;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * 作为Do接口,方便通过切面进行操作
 */
@Getter
@Setter
public abstract class BaseDo implements Serializable {
    //是否删除状态，1：删除，0：有效
    private Integer isDelete;
    //创建时间
    private ZonedDateTime gmtCreate;
    //最后修改时间
    private ZonedDateTime gmtModified;

    public abstract Long getId();
}
