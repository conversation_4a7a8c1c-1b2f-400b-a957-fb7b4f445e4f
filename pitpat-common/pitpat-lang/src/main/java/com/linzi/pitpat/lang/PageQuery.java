package com.linzi.pitpat.lang;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

/**
 * 此处继承 IPage 是为了 避免在查询列表分页时再构建一次 Paging 对象
 */
@Getter
@Setter
public class PageQuery extends Query {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 页数
     */
    private Integer pageNum= 1;
    /**
     * 分页条数
     */
    private Integer pageSize= 10;
    /**
     * 是否进行 count 查询
     */
    @JsonIgnore
    private Boolean searchCount = true;
}
