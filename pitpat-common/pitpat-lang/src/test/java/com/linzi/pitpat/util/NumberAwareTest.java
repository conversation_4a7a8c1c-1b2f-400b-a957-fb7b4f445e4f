package com.linzi.pitpat.util;

import com.linzi.pitpat.annotation.NumberScale;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
public class NumberAwareTest {

    public static void main(String[] args) {
        OrderItem orderItem = new OrderItem();
        orderItem.setPrice(new BigDecimal("52.02045"));
        orderItem.setQuantity(88.289f);

        Order order = new Order();
        order.setItems(List.of(orderItem));
        order.setDiscountRate(0.555555D);
        order.setTotalAmount(new BigDecimal("52.********"));

        User user = new User();
        user.setRating(0.2456f);
        user.setSalary(35.222D);
        user.setAccountBalance(new BigDecimal("1103.09024"));
        user.setOrders(List.of(order));
        NumberAware.processFields(user);
        log.info("user={}", user);

        RunningLevelVo runningLevelVo = new RunningLevelVo(new BigDecimal("0.5532"), new BigDecimal("0.5536"), new BigDecimal("0.5536"), new BigDecimal("0.5536"));
        NumberAware.processFields(runningLevelVo);

        Map<String, RunningLevelVo> runningLevelVo2 = Map.of("runningLevelVo", runningLevelVo);
        NumberAware.processFields(runningLevelVo2);
        log.info("processed={}", runningLevelVo2);

        Set<RunningLevelVo> runningLevelVo1 = new HashSet<>();
        runningLevelVo1.add(runningLevelVo);

        NumberAware.processFields(runningLevelVo1);
        log.info("processed={}", runningLevelVo);
        log.info("processed={}", runningLevelVo1);


        NumberAware.processFields(2);
        NumberAware.processFields(2.0D);
        NumberAware.processFields(2.0F);
        NumberAware.processFields("url");

        List<String> url = List.of("url");
        NumberAware.processFields(url);

        List<Integer> intList = List.of(2);
        NumberAware.processFields(intList);
        log.info("processed={}", intList);

    }


    @Data
    public static class RunningLevelVo {
        public RunningLevelVo(BigDecimal exceedRate, BigDecimal beforeCapabilityValue, BigDecimal capabilityValue, BigDecimal changeRate) {
            this.exceedRate = exceedRate;
            this.beforeCapabilityValue = beforeCapabilityValue;
            this.capabilityValue = capabilityValue;
            this.changeRate = changeRate;
        }

        @NumberScale
        private BigDecimal exceedRate;

        private BigDecimal beforeCapabilityValue;

        private BigDecimal capabilityValue;

        @NumberScale
        private BigDecimal changeRate;
    }

    @Data
    public static class Order implements Serializable {
        @Serial
        private static final long serialVersionUID = 9208957800892546996L;
        @NumberScale(scale = 2)
        private BigDecimal totalAmount;

        @NumberScale(scale = 4)
        private Double discountRate;

        private List<OrderItem> items;
    }

    @Data
    public static class OrderItem {
        @NumberScale(scale = 2)
        private BigDecimal price;

        @NumberScale(scale = 5)
        private Float quantity;

    }

    @Data
    public static class User {
        @NumberScale(scale = 4)
        private BigDecimal accountBalance;

        //@NumberScale(scale = 2)
        private Double salary;

        @NumberScale(scale = 1)
        private Float rating;

        private List<Order> orders;

    }

}
