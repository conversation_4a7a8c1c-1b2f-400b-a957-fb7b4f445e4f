package com.linzi.pitpat.excel.config;

import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * Excel导出配置类
 */
@Data
@Builder
public class ExcelExportConfig<T> {

    /**
     * 数据类型
     */
    private Class<T> clazz;

    /**
     * Sheet名称
     */
    private String sheetName;

    /**
     * 是否使用模板
     */
    private boolean useTemplate;

    /**
     * 模板路径
     */
    private String templatePath;

    /**
     * 是否自动调整列宽
     */
    private boolean autoColumnWidth;

    /**
     * 是否冻结首行
     */
    private boolean freezeFirstRow;

    /**
     * 自定义样式配置
     */
    @Builder.Default
    private Map<String, Object> styleConfig = new HashMap<>();

    /**
     * 自定义Excel处理器
     */
    private String handlerClass;

    /**
     * 忽略的字段
     */
    private String[] excludeFields;

    /**
     * 包含的字段
     */
    private String[] includeFields;

    /**
     * 创建导出配置的构建器并指定数据类型
     *
     * @param clazz 数据类型
     * @param <T>   数据类型
     * @return 构建器
     */
    public static <T> ExcelExportConfigBuilder<T> builder(Class<T> clazz) {
        return new ExcelExportConfigBuilder<T>().clazz(clazz);
    }

    /**
     * 创建Excel导出配置
     *
     * @param clazz 数据类型
     * @param <T>   数据类型
     * @return Excel导出配置构建器
     */
    public static <T> ExcelExportConfig<T> create(Class<T> clazz) {
        return ExcelExportConfig.builder(clazz)
                .sheetName("Sheet1")
                .build();
    }

    /**
     * 创建Excel导出配置
     *
     * @param clazz 数据类型
     * @param <T>   数据类型
     * @return Excel导出配置构建器
     */
    public static <T> ExcelExportConfig<T> create(Class<T> clazz, String sheetName) {
        return ExcelExportConfig.builder(clazz)
                .sheetName(sheetName)
                .build();
    }
}
