package com.linzi.pitpat.excel.handler;

import cn.idev.excel.write.handler.WriteHandler;
import com.linzi.pitpat.excel.config.ExcelExportConfig;
import com.linzi.pitpat.excel.constants.ExcelConstant;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Excel写入处理器工厂
 * 根据配置创建相应的处理器
 */
public class WriteHandlerFactory {


    /**
     * 根据导出配置创建写入处理器列表
     *
     * @param config 导出配置
     * @param <T>    数据类型
     * @return 写入处理器列表
     */
    public static <T> List<WriteHandler> createHandlers(ExcelExportConfig<T> config) {
        List<WriteHandler> handlers = new ArrayList<>();

        // 如果配置了冻结首行，添加冻结首行处理器
        if (config.isFreezeFirstRow()) {
            handlers.add(new FreezeFirstRowSheetWriteHandler());
        }

        // 如果配置了自动调整列宽，添加自动调整列宽处理器
        if (config.isAutoColumnWidth()) {
            // 检查是否使用行处理器版本
            handlers.add(createAutoColumnWidthRowHandler(config.getStyleConfig()));
        }

        // 自定义处理器支持
        if (config.getHandlerClass() != null && !config.getHandlerClass().isEmpty()) {
            try {
                // 通过反射创建自定义处理器
                Class<?> handlerClass = Class.forName(config.getHandlerClass());
                WriteHandler handler = (WriteHandler) handlerClass.getDeclaredConstructor().newInstance();
                handlers.add(handler);
            } catch (Exception e) {
                throw new RuntimeException("Failed to create custom handler: " + config.getHandlerClass(), e);
            }
        }

        return handlers;
    }

    /**
     * 创建自动调整列宽处理器 (RowWriteHandler版本)
     *
     * @param styleConfig 样式配置
     * @return 自动调整列宽处理器
     */
    private static AutoColumnWidthRowWriteHandler createAutoColumnWidthRowHandler(Map<String, Object> styleConfig) {
        // 默认配置
        int maxAnalyzeRows = ExcelConstant.AUTO_WIDTH_MAX_ANALYZE_ROW;
        int realAnalyzeRow = ExcelConstant.AUTO_WIDTH_MAX_ANALYZE_ROW;

        // 从样式配置中读取自定义参数
        if (styleConfig != null) {
            // 最大处理行数
            if (styleConfig.containsKey(ExcelConstant.AUTO_WIDTH_ANALYZE_ROW_NAME)) {
                Object value = styleConfig.get(ExcelConstant.AUTO_WIDTH_ANALYZE_ROW_NAME);
                if (value instanceof Number) {
                    maxAnalyzeRows = ((Number) value).intValue();
                } else if (value instanceof String) {
                    try {
                        maxAnalyzeRows = Integer.parseInt((String) value);
                    } catch (NumberFormatException ignored) {
                        // 忽略解析错误，使用默认值
                    }
                }
            }
            if (styleConfig.containsKey(ExcelConstant.AUTO_WIDTH_REAL_ROW_NUMBER_NAME)) {
                Object value = styleConfig.get(ExcelConstant.AUTO_WIDTH_REAL_ROW_NUMBER_NAME);
                if (value instanceof Number) {
                    realAnalyzeRow = ((Number) value).intValue();
                } else if (value instanceof String) {
                    try {
                        realAnalyzeRow = Integer.parseInt((String) value);
                    } catch (NumberFormatException ignored) {
                        // 忽略解析错误，使用默认值
                    }
                }
                //当配置行数大于最大行数时，调整为最大行数，确保自动调整列宽会生效
                if (maxAnalyzeRows > realAnalyzeRow) {
                    maxAnalyzeRows = realAnalyzeRow;
                }
            }
        }

        // 创建处理器实例 - 移除autoApply参数
        return new AutoColumnWidthRowWriteHandler(maxAnalyzeRows);
    }
}
