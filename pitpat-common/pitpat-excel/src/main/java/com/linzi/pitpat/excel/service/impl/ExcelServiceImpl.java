package com.linzi.pitpat.excel.service.impl;

import com.linzi.pitpat.excel.config.ExcelExportConfig;
import com.linzi.pitpat.excel.config.ExcelImportConfig;
import com.linzi.pitpat.excel.model.ExcelImportResult;
import com.linzi.pitpat.excel.service.ExcelService;
import com.linzi.pitpat.excel.util.ExcelUtils;
import com.linzi.pitpat.excel.validator.ExcelValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * Implementation of ExcelService interface
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ExcelServiceImpl implements ExcelService {

    @Override
    public <T> void exportExcel(HttpServletResponse response, List<T> data, Class<T> clazz, String fileName) {
        try {
            // Set response headers
            ExcelUtils.setExcelResponseHeaders(response, fileName);

            // Export data to Excel
            try (OutputStream outputStream = response.getOutputStream()) {
                ExcelUtils.export(data, clazz, outputStream);
            }
        } catch (IOException e) {
            throw new RuntimeException("Export Excel failed", e);
        }
    }

    @Override
    public <T> void exportExcelTemplate(HttpServletResponse response, Class<T> clazz, String fileName) {
        try{
            // Set response headers
            ExcelUtils.setExcelResponseHeaders(response, fileName + "_Template");

            // Create empty template (or with sample data if needed)
            List<T> emptyList = new ArrayList<>();

            // Export template
            try (OutputStream outputStream = response.getOutputStream()) {
                //ExcelUtils.export(emptyList, clazz, outputStream);

                ExcelExportConfig<T> config = ExcelExportConfig.create(clazz, "Template");
                ExcelUtils.exportWithConfig(emptyList, config, outputStream);
            }
        }catch (Exception e) {
            throw new RuntimeException("Export Excel failed", e);
        }

    }

    @Override
    public <T> ExcelImportResult<T> importExcel(MultipartFile file, Class<T> clazz) {
        try {
            try (InputStream inputStream = file.getInputStream()) {
                // 创建导入配置
                ExcelImportConfig<T> config = ExcelImportConfig.<T>builder()
                        .clazz(clazz)
                        .validator(ExcelValidator.createDefault(clazz))
                        .skipInvalid(true)
                        .build();

                // Import data with default validation
                ExcelImportResult<T> result = ExcelUtils.readWithValidation(inputStream, config);

                // Return only valid data
                log.trace("import result={}, errorMessages={}, ErrorMap={}", result.getInvalidData(), result.getErrorMessages(), result.getErrorMap());
                return result;
            }
        } catch (Exception e) {
            throw new RuntimeException("Import Excel failed", e);
        }

    }

    @Override
    public <T> List<T> importExcelWithoutValidation(MultipartFile file, Class<T> clazz) {
        try {
            try (InputStream inputStream = file.getInputStream()) {
                // 创建导入配置
                ExcelImportConfig<T> config = ExcelImportConfig.<T>builder()
                        .clazz(clazz)
                        .build();

                return ExcelUtils.readWithConfig(inputStream, config);
            }
        } catch (Exception e) {
            throw new RuntimeException("Import Excel failed", e);
        }
    }

    @Override
    public <T> ExcelImportResult<T> importExcel(MultipartFile file, Class<T> clazz, Class<?>... groups) {
        try {
            try (InputStream inputStream = file.getInputStream()) {
                // Create validator with specified groups
                // Note: This would require implementing a group-aware validator
                // For now, we'll use the default validator
                // In a real implementation, you might extend the ExcelValidator.createDefault to support groups

                // 创建导入配置
                ExcelImportConfig<T> config = ExcelImportConfig.<T>builder()
                        .clazz(clazz)
                        .validator(ExcelValidator.createDefault(clazz))
                        .skipInvalid(true)
                        .build();

                // Import data with default validation
                ExcelImportResult<T> result = ExcelUtils.readWithValidation(inputStream, config);

                // Return only valid data
                //return result.getValidData();
                return result;
            }
        } catch (IOException e) {
            throw new RuntimeException("Import Excel failed", e);
        }
    }

    @Override
    public <T> ExcelImportResult<T> importExcelWithValidation(MultipartFile file, Class<T> clazz, ExcelValidator<T> validator) {
        try {
            try (InputStream inputStream = file.getInputStream()) {
                // Import data with custom validation
                // 创建导入配置
                ExcelImportConfig<T> config = ExcelImportConfig.<T>builder()
                        .clazz(clazz)
                        .validator(validator)
                        .skipInvalid(true)
                        .build();

                // Import data with default validation
                ExcelImportResult<T> result = ExcelUtils.readWithValidation(inputStream, config);
                // Return only valid data
                return result;
            }
        } catch (IOException e) {
            throw new RuntimeException("Import Excel failed", e);
        }
    }
}
