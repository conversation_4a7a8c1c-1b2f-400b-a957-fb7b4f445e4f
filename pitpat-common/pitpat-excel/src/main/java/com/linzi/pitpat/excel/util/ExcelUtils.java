package com.linzi.pitpat.excel.util;

import cn.idev.excel.FastExcel;
import com.linzi.pitpat.excel.config.ExcelExportConfig;
import com.linzi.pitpat.excel.config.ExcelImportConfig;
import com.linzi.pitpat.excel.converter.ZonedDateNumberConverter;
import com.linzi.pitpat.excel.converter.ZonedDateStringConverter;
import com.linzi.pitpat.excel.listener.BatchProcessListener;
import com.linzi.pitpat.excel.listener.CollectDataListener;
import com.linzi.pitpat.excel.listener.SimpleValidationReadListener;
import com.linzi.pitpat.excel.model.ExcelImportResult;
import com.linzi.pitpat.excel.validator.ExcelValidator;
import com.linzi.pitpat.exception.BizException;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * Excel工具类
 * 提供Excel相关的工具方法
 */
public class ExcelUtils {

    private ExcelUtils() {
        // 工具类不允许实例化
    }

    /**
     * 设置Excel响应头
     *
     * @param response HttpServletResponse
     * @param fileName 文件名
     */
    public static void setExcelResponseHeaders(HttpServletResponse response, String fileName) {
        ExcelExportUtils.setExcelResponseHeaders(response, fileName);
    }

    /**
     * 生成带时间戳的文件名
     *
     * @param baseName 基础文件名
     * @return 带时间戳的文件名
     */
    public static String generateTimestampFileName(String baseName) {
        return ExcelExportUtils.generateTimestampFileName(baseName);
    }

    /**
     * 获取临时目录路径
     *
     * @return 临时目录路径
     */
    public static String getTempDirectoryPath() {
        return ExcelExportUtils.getTempDirectoryPath();
    }

    /**
     * 导出Excel到文件
     *
     * @param data     数据列表
     * @param clazz    数据类型
     * @param filePath 文件路径
     * @param <T>      数据泛型
     */
    public static <T> void exportToFile(List<T> data, Class<T> clazz, String filePath) {
        ExcelExportUtils.exportToFile(data, clazz, filePath);
    }

    /**
     * 使用模板导出Excel到文件
     *
     * @param data         数据列表
     * @param clazz        数据类型
     * @param templatePath 模板路径
     * @param filePath     文件路径
     * @param <T>          数据泛型
     */
    public static <T> void exportToFileWithTemplate(List<T> data, Class<T> clazz, String templatePath, String filePath) {
        ExcelExportUtils.exportToFileWithTemplate(data, clazz, templatePath, filePath);
    }

    /**
     * 导出多个Sheet的Excel到输出流
     *
     * @param dataMap      Sheet名称和数据的映射
     * @param outputStream 输出流
     */
    public static void exportMultiSheetToFile(Map<String, List<?>> dataMap, OutputStream outputStream) {
        ExcelExportUtils.exportMultiSheetToFile(dataMap, outputStream);
    }

    /**
     * 导出Excel到字节数组
     *
     * @param data  数据列表
     * @param clazz 数据类型
     * @param <T>   数据泛型
     * @return Excel字节数组
     */
    public static <T> byte[] exportToBytes(List<T> data, Class<T> clazz) {
        return ExcelExportUtils.exportToBytes(data, clazz);
    }


    /**
     * 创建Excel导出构建器
     * 为保持向后兼容性，返回ExcelExportConfigBuilder
     *
     * @param clazz 数据类型
     * @param <T>   数据类型
     * @return 导出构建器
     */
    public static <T> void export(List<T> data, Class<T> clazz, OutputStream outputStream) {
        ExcelExportUtils.export(data, clazz, outputStream);
    }

    /**
     * 使用配置导出Excel到输出流
     *
     * @param dataList     数据列表
     * @param config       导出配置
     * @param outputStream 输出流
     * @param <T>          数据类型
     */
    public static <T> void exportWithConfig(List<T> dataList, ExcelExportConfig<T> config, OutputStream outputStream) {
        ExcelExportUtils.exportWithConfig(dataList, config, outputStream);
    }

    /**
     * 使用配置导出Excel到文件
     *
     * @param dataList 数据列表
     * @param config   导出配置
     * @param file     输出文件
     * @param <T>      数据类型
     * @throws IOException IO异常
     */
    public static <T> void exportWithConfig(List<T> dataList, ExcelExportConfig<T> config, File file){
        try {
            try (FileOutputStream outputStream = new FileOutputStream(file)) {
                exportWithConfig(dataList, config, outputStream);
            }
        } catch (IOException e) {
            throw new RuntimeException("Export Excel failed");
        }
    }

    /**
     * 使用配置导出Excel到文件路径
     *
     * @param dataList 数据列表
     * @param config   导出配置
     * @param filePath 文件路径
     * @param <T>      数据类型
     * @throws IOException IO异常
     */
    public static <T> void exportWithConfig(List<T> dataList, ExcelExportConfig<T> config, String filePath){
        exportWithConfig(dataList, config, new File(filePath));
    }


    /**
     * 从字节数组导入Excel
     *
     * @param bytes Excel字节数组
     * @param clazz 数据类型
     * @param <T>   数据泛型
     * @return 导入的数据列表
     */
    public static <T> List<T> importFromBytes(byte[] bytes, Class<T> clazz) {
        try (InputStream inputStream = new ByteArrayInputStream(bytes)) {
            return readWithConfig(inputStream, ExcelImportConfig.create(clazz));
        } catch (IOException e) {
            throw new BizException("导入Excel失败", e);
        }
    }


    /**
     * 从文件导入Excel
     *
     * @param filePath 文件路径
     * @param clazz    数据类型
     * @param <T>      数据泛型
     * @return 导入的数据列表
     */
    public static <T> List<T> importFromFile(String filePath, Class<T> clazz) {
        try (InputStream inputStream = new FileInputStream(filePath)) {
            return readWithConfig(inputStream, ExcelImportConfig.create(clazz));
        } catch (IOException e) {
            throw new BizException("导入Excel失败", e);
        }
    }

    /**
     * 从文件导入Excel指定Sheet
     *
     * @param filePath  文件路径
     * @param clazz     数据类型
     * @param sheetName Sheet名称
     * @param <T>       数据泛型
     * @return 导入的数据列表
     */
    public static <T> List<T> importFromFile(String filePath, Class<T> clazz, String sheetName) {
        try (InputStream inputStream = new FileInputStream(filePath)) {
            return readWithConfig(inputStream, ExcelImportConfig.<T>builder().clazz(clazz).sheetName(sheetName).build());
        } catch (IOException e) {
            throw new BizException("导入Excel失败", e);
        }
    }

    /**
     * 带数据校验的Excel文件导入
     *
     * @param filePath  文件路径
     * @param clazz     数据类型
     * @param validator 数据校验器
     * @param <T>       数据泛型
     * @return 校验通过的数据列表
     */
    public static <T> List<T> importFromFileWithValidation(String filePath, Class<T> clazz, ExcelValidator<T> validator) {
        try (InputStream inputStream = new FileInputStream(filePath)) {
            return readWithConfig(inputStream, ExcelImportConfig.<T>builder().clazz(clazz).validator(validator).build());
        } catch (IOException e) {
            throw new BizException("导入Excel失败", e);
        }
    }

    /**
     * 带数据校验的Excel文件导入指定Sheet
     *
     * @param filePath  文件路径
     * @param clazz     数据类型
     * @param validator 数据校验器
     * @param sheetName Sheet名称
     * @param <T>       数据泛型
     * @return 校验通过的数据列表
     */
    public static <T> List<T> importFromFileWithValidation(String filePath, Class<T> clazz, ExcelValidator<T> validator, String sheetName) {
        try (InputStream inputStream = new FileInputStream(filePath)) {
            return readWithConfig(inputStream, ExcelImportConfig.<T>builder().clazz(clazz).validator(validator).build());
        } catch (IOException e) {
            throw new BizException("导入Excel失败", e);
        }
    }

    /**
     * 分批处理导入Excel
     *
     * @param filePath      文件路径
     * @param clazz         数据类型
     * @param batchSize     批处理大小
     * @param batchConsumer 批处理函数
     * @param <T>           数据泛型
     */
    public static <T> void importWithBatch(String filePath, Class<T> clazz, int batchSize, Consumer<List<T>> batchConsumer) {
        try (InputStream inputStream = new FileInputStream(filePath)) {
            List<T> batchList = new ArrayList<>(batchSize);
            // 创建监听器
            BatchProcessListener<T> listener = new BatchProcessListener<>(batchSize, batchConsumer, batchList);
            // 调用内部方法导入
            readWithListener(inputStream, ExcelImportConfig.<T>builder().clazz(clazz).build(), listener);
        } catch (IOException e) {
            throw new BizException("导入Excel失败", e);
        }
    }


    // 实现内部导入方法
    public static <T> List<T> readWithConfig(InputStream inputStream, ExcelImportConfig<T> config){
        // 创建结果列表
        List<T> result = new ArrayList<>();

        // 创建监听器
        cn.idev.excel.read.listener.ReadListener<T> listener = new CollectDataListener<>(result);

        // 调用内部方法导入
        readWithListener(inputStream, config, listener);

        return result;
    }

    /**
     * 从输入流中读取数据（带验证）
     *
     * @param inputStream 输入流
     * @return 导入结果（包含有效数据和验证错误）
     * @throws IOException IO异常
     */
    public static <T> ExcelImportResult<T> readWithValidation(InputStream inputStream, ExcelImportConfig<T> config){
        if (config.getValidator() == null) {
            // 如果没有设置验证器，使用默认验证器
            config.setValidator(ExcelValidator.createDefault(config.getClazz()));
        }

        // 创建验证结果
        ExcelImportResult<T> result = new ExcelImportResult<>();

        // 创建验证监听器
        SimpleValidationReadListener<T> listener = new SimpleValidationReadListener<>(
                result,
                config.getValidator(),
                config.isSkipInvalid()
        );

        // 调用内部方法导入
        readWithListener(inputStream, config, listener);

        return result;
    }

    private static <T> void readWithListener(InputStream inputStream, ExcelImportConfig<T> config,
                                             cn.idev.excel.read.listener.ReadListener<T> listener) {
        // 使用FastExcel的API进行导入
        if (config.getSheetName() != null) {
            FastExcel.read(inputStream, config.getClazz(), listener)
                    .sheet(config.getSheetName())
                    .headRowNumber(config.getHeadRowNumber())
                    .registerConverter(new ZonedDateNumberConverter())
                    .registerConverter(new ZonedDateStringConverter())
                    .doRead();
        } else if (config.getSheetNo() != null) {
            FastExcel.read(inputStream, config.getClazz(), listener)
                    .sheet(config.getSheetNo())
                    .headRowNumber(config.getHeadRowNumber())
                    .registerConverter(new ZonedDateNumberConverter())
                    .registerConverter(new ZonedDateStringConverter())
                    .doRead();
        } else {
            FastExcel.read(inputStream, config.getClazz(), listener)
                    .sheet()
                    .headRowNumber(config.getHeadRowNumber())
                    .registerConverter(new ZonedDateNumberConverter())
                    .registerConverter(new ZonedDateStringConverter())
                    .doRead();
        }
    }
}
