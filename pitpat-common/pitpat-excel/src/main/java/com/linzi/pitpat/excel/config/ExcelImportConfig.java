package com.linzi.pitpat.excel.config;

import cn.idev.excel.read.listener.ReadListener;
import com.linzi.pitpat.excel.validator.ExcelValidator;
import lombok.Builder;
import lombok.Data;

/**
 * Excel导入配置类
 */
@Data
@Builder
public class ExcelImportConfig<T> {

    /**
     * 数据类型
     */
    private Class<T> clazz;

    /**
     * 指定读取的Sheet名称，默认不要填写，如果有多个 sheet,想读取其中某个可以填写
     */
    private String sheetName;

    /**
     * 指定读取Sheet索引，默认不要填写，如果有多个 sheet,想读取其中某个可以填写
     */
    private Integer sheetNo;

    /**
     * 表头行数
     */
    private Integer headRowNumber;

    /**
     * 是否跳过无效数据
     */
    private boolean skipInvalid;

    /**
     * 是否忽略空行
     */
    private boolean ignoreEmptyRow;

    /**
     * 导入行数限制
     */
    private Integer rowLimit;

    /**
     * 自定义字段映射
     */
    private String[] fieldMapping;

    /**
     * 数据验证器
     */
    private ExcelValidator<T> validator;

    /**
     * 事件监听器
     */
    private ReadListener<T> listener;

    /**
     * 创建Excel导出配置
     *
     * @param clazz 数据类型
     * @param <T>   数据类型
     * @return Excel导出配置构建器
     */
    public static <T> ExcelImportConfig<T> create(Class<T> clazz) {
        return ExcelImportConfig.<T>builder().clazz(clazz)
                .build();
    }
}
