package com.linzi.pitpat.excel.validator.impl;

import com.linzi.pitpat.excel.model.ValidationResult;
import com.linzi.pitpat.excel.validator.ExcelValidator;

import java.util.Set;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;

/**
 * ExcelValidator that uses JSR-380 (Bean Validation) annotations
 */
public class Jsr380Validator<T> implements ExcelValidator<T> {

    private final Validator validator;
    private final Class<?>[] groups;

    public Jsr380Validator(Class<?>... groups) {
        try (ValidatorFactory factory = Validation.buildDefaultValidatorFactory()) {
            this.validator = factory.getValidator();
            this.groups = groups;
        }
    }

    @Override
    public ValidationResult validate(T data) {
        ValidationResult result = ValidationResult.success();

        return getValidationResult(data, result);
    }

    private ValidationResult getValidationResult(T data, ValidationResult result) {
        if (data == null) {
            return result;
        }

        Set<ConstraintViolation<T>> violations = validator.validate(data, groups);

        for (ConstraintViolation<T> violation : violations) {
            String message = violation.getMessage();
            String fieldName = violation.getPropertyPath().toString();
            result.addError(fieldName, message);
        }

        return result;
    }

    @Override
    public ValidationResult validate(T data, int rowNumber) {
        ValidationResult result = ValidationResult.valid(rowNumber, data);

        return getValidationResult(data, result);
    }
}
