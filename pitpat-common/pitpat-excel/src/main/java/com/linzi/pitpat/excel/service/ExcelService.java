package com.linzi.pitpat.excel.service;

import com.linzi.pitpat.excel.model.ExcelImportResult;
import com.linzi.pitpat.excel.validator.ExcelValidator;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * Service interface for Excel operations including import, export, and validation
 *
 * <AUTHOR>
 */
public interface ExcelService {

    /**
     * Export data to Excel file
     *
     * @param response HTTP response for file download
     * @param data     Data to export
     * @param clazz    Data class type
     * @param fileName File name without extension
     * @param <T>      Data type
     * @throws IOException if an error occurs during file writing
     */
    <T> void exportExcel(HttpServletResponse response, List<T> data, Class<T> clazz, String fileName);

    /**
     * Export Excel template based on class definition
     *
     * @param response HTTP response for file download
     * @param clazz    Data class type to use for template
     * @param fileName File name without extension
     * @param <T>      Data type
     * @throws IOException if an error occurs during file writing
     */
    <T> void exportExcelTemplate(HttpServletResponse response, Class<T> clazz, String fileName);

    /**
     * Import data from Excel file with default validation
     *
     * @param file  Excel file to import
     * @param clazz Data class type
     * @param <T>   Data type
     * @return List of imported data objects
     * @throws IOException if an error occurs during file reading
     */
    <T> ExcelImportResult<T> importExcel(MultipartFile file, Class<T> clazz);

    /**
     * Import data from Excel file without validation
     *
     * @param file  Excel file to import
     * @param clazz Data class type
     * @param <T>   Data type
     * @return List of imported data objects
     * @throws IOException if an error occurs during file reading
     */
    <T> List<T> importExcelWithoutValidation(MultipartFile file, Class<T> clazz);

    /**
     * Import data from Excel file with validation using specified validation groups
     *
     * @param file   Excel file to import
     * @param clazz  Data class type
     * @param groups Validation groups to use
     * @param <T>    Data type
     * @return List of validated data objects
     * @throws IOException if an error occurs during file reading
     */
    <T> ExcelImportResult<T> importExcel(MultipartFile file, Class<T> clazz, Class<?>... groups);

    /**
     * Import Excel data with custom validator
     *
     * @param file      Excel file
     * @param clazz     Data class type
     * @param validator Custom validator
     * @param <T>       Data type
     * @return List of validated data
     * @throws IOException if an error occurs during file reading
     */
    <T> ExcelImportResult<T> importExcelWithValidation(MultipartFile file, Class<T> clazz, ExcelValidator<T> validator);

}
