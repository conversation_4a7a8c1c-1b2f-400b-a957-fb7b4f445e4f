package com.linzi.pitpat.excel.validator;

import com.linzi.pitpat.excel.model.ValidationResult;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.ValidatorFactory;
import java.util.Set;

/**
 * Generic validator interface for validating objects
 *
 * @param <T> Type of object to validate
 * <AUTHOR>
 */
public interface ExcelValidator<T> {

    /**
     * Validates the provided object
     *
     * @param target Object to validate
     * @return Validation result indicating success or failure with error messages
     */
    ValidationResult validate(T target);

    /**
     * Validates the provided object with row index context (for Excel data)
     *
     * @param target Object to validate
     * @param rowIndex Row index in Excel (0-based)
     * @return Validation result
     */
    default ValidationResult validate(T target, int rowIndex) {
        ValidationResult result = validate(target);
        result.setRowNumber(rowIndex);
        return result;
    }

    /**
     * Creates default validator implementation using Java Bean Validation (JSR-380)
     *
     * @param <T> Type of object to validate
     * @param clazz Class of object to validate
     * @return Default validator implementation
     */
    static <T> ExcelValidator<T> createDefault(Class<T> clazz) {
        return new ExcelValidator<T>() {
            private final javax.validation.Validator validator;

            {
                try(ValidatorFactory factory = Validation.buildDefaultValidatorFactory()){
                    validator = factory.getValidator();
                }
            }

            @Override
            public ValidationResult validate(T target) {
                if (target == null) {
                    return ValidationResult.invalid(0, null, "data", "Data cannot be null");
                }

                ValidationResult result = ValidationResult.success().setData(target);

                Set<ConstraintViolation<T>> violations = validator.validate(target);
                if (!violations.isEmpty()) {
                    for (ConstraintViolation<T> violation : violations) {
                        String field = violation.getPropertyPath().toString();
                        result.addError(field, violation.getMessage());
                    }
                }

                return result;
            }
        };
    }
}
