package com.linzi.pitpat.excel.listener;


import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

/**
 * 批处理监听器
 */
public class BatchProcessListener<T> implements cn.idev.excel.read.listener.ReadListener<T> {
    private final int batchSize;
    private final Consumer<List<T>> batchConsumer;
    private final List<T> batchList;

    public BatchProcessListener(int batchSize, Consumer<List<T>> batchConsumer, List<T> batchList) {
        this.batchSize = batchSize;
        this.batchConsumer = batchConsumer;
        this.batchList = batchList;
    }

    @Override
    public void invoke(T data, cn.idev.excel.context.AnalysisContext context) {
        batchList.add(data);

        // 达到批处理大小，处理批次数据
        if (batchList.size() >= batchSize) {
            processBatch();
        }
    }

    @Override
    public void doAfterAllAnalysed(cn.idev.excel.context.AnalysisContext context) {
        // 处理剩余数据
        if (!batchList.isEmpty()) {
            processBatch();
        }
    }

    private void processBatch() {
        batchConsumer.accept(new ArrayList<>(batchList));
        batchList.clear();
    }
}
