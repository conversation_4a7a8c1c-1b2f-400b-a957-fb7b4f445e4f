package com.linzi.pitpat.excel.service;

import com.linzi.pitpat.excel.config.ExcelExportConfig;
import com.linzi.pitpat.excel.config.ExcelImportConfig;
import com.linzi.pitpat.excel.validator.ExcelValidator;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 高级Excel处理服务接口，支持更多自定义功能
 */
public interface AdvancedExcelService {

    /**
     * 使用自定义配置导出Excel
     *
     * @param data         数据列表
     * @param config       导出配置
     * @param outputStream 输出流
     * @param <T>          数据泛型
     */
    <T> void exportWithConfig(List<T> data, ExcelExportConfig<T> config, OutputStream outputStream);

    /**
     * 使用模板导出Excel
     *
     * @param data         数据列表
     * @param templatePath 模板路径
     * @param outputStream 输出流
     * @param <T>          数据泛型
     */
    <T> void exportWithTemplate(List<T> data, Class<T> clazz, String templatePath, OutputStream outputStream);

    /**
     * 导出多个Sheet的Excel
     *
     * @param dataMap      Sheet名称和数据的映射
     * @param outputStream 输出流
     */
    void exportMultiSheet(Map<String, List<?>> dataMap, OutputStream outputStream);

    /**
     * 使用自定义配置导入Excel
     *
     * @param inputStream 输入流
     * @param config      导入配置
     * @param <T>         数据泛型
     * @return 导入的数据列表
     */
    <T> List<T> importWithConfig(InputStream inputStream, ExcelImportConfig<T> config);

    /**
     * 不带数据校验的Excel导入
     *
     * @param inputStream 输入流
     * @param clazz       数据类型
     * @param <T>         数据泛型
     * @return 导入的数据列表
     */
    <T> List<T> importWithoutValidation(InputStream inputStream, Class<T> clazz);

    /**
     * 带数据校验的Excel导入
     *
     * @param inputStream 输入流
     * @param clazz       数据类型
     * @param validator   数据校验器
     * @param <T>         数据泛型
     * @return 校验通过的数据列表
     */
    <T> List<T> importWithValidation(InputStream inputStream, Class<T> clazz, ExcelValidator<T> validator);

    /**
     * 使用默认Bean校验导入Excel
     *
     * @param inputStream 输入流
     * @param clazz       数据类型
     * @param <T>         数据泛型
     * @return 校验通过的数据列表
     */
    <T> List<T> importWithDefaultValidation(InputStream inputStream, Class<T> clazz);

    /**
     * 分批处理导入数据
     *
     * @param inputStream   输入流
     * @param clazz         数据类型
     * @param batchSize     批处理大小
     * @param batchConsumer 批处理函数
     * @param <T>           数据泛型
     */
    <T> void importWithBatch(InputStream inputStream, Class<T> clazz, int batchSize, Consumer<List<T>> batchConsumer);
}
