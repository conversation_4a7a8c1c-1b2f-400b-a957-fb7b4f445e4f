package com.linzi.pitpat.excel.listener;

import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.read.listener.ReadListener;
import com.linzi.pitpat.excel.model.ValidationResult;
import com.linzi.pitpat.excel.validator.ExcelValidator;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel导入验证监听器
 * 在导入过程中对数据进行验证
 *
 * @param <T> 数据类型
 */
public class ValidationReadListener<T> implements ReadListener<T> {
    private final List<T> resultList;
    private final ExcelValidator<T> validator;
    private final Map<Integer, Map<String, String>> errorMap = new HashMap<>();
    private int rowIndex = 0;


    /**
     * 构造方法
     *
     * @param resultList 结果列表
     * @param validator  验证器实现
     */
    public ValidationReadListener(List<T> resultList, ExcelValidator<T> validator) {
        this.resultList = resultList;
        this.validator = validator;
    }

    @Override
    public void invoke(T data, AnalysisContext context) {
        // 验证数据
        ValidationResult validationResult = validator.validate(data, rowIndex);

        // 如果数据有效或配置为不跳过无效数据，则添加到结果列表
        if (validationResult.isValid()) {
            resultList.add(data);
        }

        // 如果有错误，则添加到错误映射
        if (!validationResult.isValid()) {
            errorMap.put(rowIndex, validationResult.getErrors());
        }

        // 增加行索引
        rowIndex++;
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 分析完成后的处理
    }

    /**
     * 获取验证错误
     *
     * @return 行索引到错误映射的映射
     */
    public Map<Integer, Map<String, String>> getErrorMap() {
        return errorMap;
    }

    /**
     * 检查是否有错误
     *
     * @return 是否有任何验证错误
     */
    public boolean hasErrors() {
        return !errorMap.isEmpty();
    }

    /**
     * 获取错误消息
     *
     * @return 格式化的错误消息列表
     */
    public List<String> getErrorMessages() {
        List<String> messages = new ArrayList<>();

        // Format and add error messages from the error map
        errorMap.forEach((rowIdx, rowErrors) -> {
            rowErrors.forEach((field, message) -> {
                messages.add(String.format("Row %d, %s: %s", rowIdx, field, message));
            });
        });

        return messages;
    }
}

