package com.linzi.pitpat.excel.listener;

import java.util.List;

/**
 * 收集数据的监听器
 */
public class CollectDataListener<T> implements cn.idev.excel.read.listener.ReadListener<T> {
    private final List<T> resultList;

    public CollectDataListener(List<T> resultList) {
        this.resultList = resultList;
    }

    @Override
    public void invoke(T data, cn.idev.excel.context.AnalysisContext context) {
        resultList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(cn.idev.excel.context.AnalysisContext context) {
        // 解析结束
    }
}
