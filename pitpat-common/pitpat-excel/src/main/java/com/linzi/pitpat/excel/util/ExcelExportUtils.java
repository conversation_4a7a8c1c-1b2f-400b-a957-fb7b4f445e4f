package com.linzi.pitpat.excel.util;

import cn.idev.excel.ExcelWriter;
import cn.idev.excel.FastExcel;
import com.linzi.pitpat.excel.config.ExcelExportConfig;
import com.linzi.pitpat.excel.constants.ExcelConstant;
import com.linzi.pitpat.excel.converter.ZonedDateDateConverter;
import com.linzi.pitpat.excel.converter.ZonedDateNumberConverter;
import com.linzi.pitpat.excel.converter.ZonedDateStringConverter;
import com.linzi.pitpat.excel.handler.WriteHandlerFactory;
import com.linzi.pitpat.exception.BizException;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

class ExcelExportUtils {

    private static final String EXCEL_CONTENT_TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    private ExcelExportUtils() {
        // 工具类不允许实例化
    }

    /**
     * 设置Excel响应头
     *
     * @param response HttpServletResponse
     * @param fileName 文件名
     */
    public static void setExcelResponseHeaders(HttpServletResponse response, String fileName) {
        response.setContentType(EXCEL_CONTENT_TYPE);
        response.setCharacterEncoding("utf-8");
        String encodedFileName = URLEncoder.encode(fileName + ".xlsx", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        // Encode the filename for content disposition header
        response.setHeader("Content-Disposition", "attachment;filename=" + encodedFileName);
    }

    /**
     * 生成带时间戳的文件名
     *
     * @param baseName 基础文件名
     * @return 带时间戳的文件名
     */
    public static String generateTimestampFileName(String baseName) {
        return baseName + "_" + LocalDateTime.now().format(DATE_FORMATTER);
    }

    /**
     * 获取临时目录路径
     *
     * @return 临时目录路径
     */
    public static String getTempDirectoryPath() {
        return System.getProperty("java.io.tmpdir");
    }

    /**
     * 导出Excel到文件
     *
     * @param data     数据列表
     * @param clazz    数据类型
     * @param filePath 文件路径
     * @param <T>      数据泛型
     */
    public static <T> void exportToFile(List<T> data, Class<T> clazz, String filePath) {
        try (OutputStream outputStream = new FileOutputStream(filePath)) {
            FastExcel.write(outputStream, clazz)
                    .sheet("Sheet1")
                    .doWrite(data);
        } catch (IOException e) {
            throw new BizException("导出Excel失败", e);
        }
    }

    /**
     * 使用模板导出Excel到文件
     *
     * @param data         数据列表
     * @param clazz        数据类型
     * @param templatePath 模板路径
     * @param filePath     文件路径
     * @param <T>          数据泛型
     */
    public static <T> void exportToFileWithTemplate(List<T> data, Class<T> clazz, String templatePath, String filePath) {
        try (OutputStream outputStream = new FileOutputStream(filePath)) {
            FastExcel.write(outputStream, clazz)
                    .withTemplate(templatePath)
                    .sheet("Sheet1")
                    .doWrite(data);
        } catch (IOException e) {
            throw new BizException("导出Excel失败", e);
        }
    }

    /**
     * 导出多个Sheet的Excel到输出流
     *
     * @param dataMap      Sheet名称和数据的映射
     * @param outputStream 输出流
     */
    public static void exportMultiSheetToFile(Map<String, List<?>> dataMap, OutputStream outputStream) {
        // 获取第一个数据集的Class类型
        Class<?> firstClazz = dataMap.values().iterator().next().get(0).getClass();

        // 创建写入器
        try (ExcelWriter excelWriter = FastExcel.write(outputStream, firstClazz).build()) {
            try {
                // 为每个sheet创建写入器并写入数据
                int sheetNo = 0;
                for (Map.Entry<String, List<?>> entry : dataMap.entrySet()) {
                    String sheetName = entry.getKey();
                    List<?> data = entry.getValue();

                    // 如果数据为空，跳过
                    if (data == null || data.isEmpty()) {
                        continue;
                    }

                    // 获取数据的Class类型
                    Class<?> dataClazz = data.get(0).getClass();

                    // 创建Sheet并写入数据
                    cn.idev.excel.write.builder.ExcelWriterSheetBuilder sheetBuilder = FastExcel.writerSheet(sheetNo++, sheetName);
                    // 注册转换器
                    sheetBuilder.registerConverter(new ZonedDateDateConverter())
                            .registerConverter(new ZonedDateNumberConverter())
                            .registerConverter(new ZonedDateStringConverter());
                    // 设置表头
                    if (dataClazz != null) {
                        sheetBuilder.head(dataClazz);
                    }

                    excelWriter.write(data, sheetBuilder.build());
                }
            } finally {
                // 完成写入并关闭资源
                if (excelWriter != null) {
                    excelWriter.finish();
                }
            }
        } catch (Exception e) {
            throw new BizException("导出Excel失败", e);
        }
    }

    /**
     * 导出Excel到字节数组
     *
     * @param data  数据列表
     * @param clazz 数据类型
     * @param <T>   数据泛型
     * @return Excel字节数组
     */
    public static <T> byte[] exportToBytes(List<T> data, Class<T> clazz) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            FastExcel.write(outputStream, clazz)
                    .sheet("Sheet1")
                    .doWrite(data);
            return outputStream.toByteArray();
        } catch (IOException e) {
            throw new BizException("导出Excel失败", e);
        }
    }

    /**
     * 创建Excel导出构建器
     * 为保持向后兼容性，返回ExcelExportConfigBuilder
     *
     * @param clazz 数据类型
     * @param <T>   数据类型
     * @return 导出构建器
     */
    public static <T> void export(List<T> data, Class<T> clazz, OutputStream outputStream) {
        ExcelExportConfig<T> config = ExcelExportConfig.builder(clazz).sheetName("Sheet1").build();

        exportWithConfig(data, config, outputStream);
    }

    /**
     * 使用配置导出Excel到输出流
     *
     * @param dataList     数据列表
     * @param config       导出配置
     * @param outputStream 输出流
     * @param <T>          数据类型
     */
    public static <T> void exportWithConfig(List<T> dataList, ExcelExportConfig<T> config, OutputStream outputStream) {
        // 构建写入器
        cn.idev.excel.write.builder.ExcelWriterBuilder writerBuilder = cn.idev.excel.FastExcel.write(outputStream, config.getClazz());

        // 应用模板
        if (config.isUseTemplate() && config.getTemplatePath() != null) {
            writerBuilder.withTemplate(config.getTemplatePath());
        }
        //写入实际导入的行数，用于解决自动调整时，无法获取真实的导入行数的问题
        if (config.isAutoColumnWidth() && !CollectionUtils.isEmpty(dataList)) {
            config.getStyleConfig().put(ExcelConstant.AUTO_WIDTH_REAL_ROW_NUMBER_NAME, dataList.size());
        }

        // 创建和注册处理器
        WriteHandlerFactory.createHandlers(config)
                .forEach(writerBuilder::registerWriteHandler);

        // 创建写入Sheet
        cn.idev.excel.write.builder.ExcelWriterSheetBuilder sheetBuilder = writerBuilder.sheet(
                config.getSheetName() != null ? config.getSheetName() : "Sheet1");

        // 应用排除字段
        if (config.getExcludeFields() != null && config.getExcludeFields().length > 0) {
            sheetBuilder.excludeColumnFieldNames(List.of(config.getExcludeFields()));
        }

        // 应用包含字段
        if (config.getIncludeFields() != null && config.getIncludeFields().length > 0) {
            sheetBuilder.includeColumnFieldNames(List.of(config.getIncludeFields()));
        }

        // 注册转换器
        sheetBuilder.registerConverter(new ZonedDateDateConverter())
                .registerConverter(new ZonedDateNumberConverter())
                .registerConverter(new ZonedDateStringConverter());

        // 写入数据
        sheetBuilder.doWrite(dataList);
    }
}
