package com.linzi.pitpat.excel.converter;

import cn.idev.excel.converters.Converter;
import cn.idev.excel.metadata.GlobalConfiguration;
import cn.idev.excel.metadata.data.WriteCellData;
import cn.idev.excel.metadata.property.ExcelContentProperty;
import cn.idev.excel.util.DateUtils;
import cn.idev.excel.util.WorkBookUtil;

import java.time.ZonedDateTime;

public class ZonedDateDateConverter  implements Converter<ZonedDateTime> {
    @Override
    public Class<?> supportJavaTypeKey() {
        return ZonedDateTime.class;
    }

    @Override
    public WriteCellData<?> convertToExcelData(ZonedDateTime value, ExcelContentProperty contentProperty,
            GlobalConfiguration globalConfiguration) {
        WriteCellData<?> cellData = new WriteCellData<>(value.toLocalDateTime());
        String format = null;
        if (contentProperty != null && contentProperty.getDateTimeFormatProperty() != null) {
            format = contentProperty.getDateTimeFormatProperty().getFormat();
        }
        WorkBookUtil.fillDataFormat(cellData, format, DateUtils.defaultDateFormat);
        return cellData;
    }
}

