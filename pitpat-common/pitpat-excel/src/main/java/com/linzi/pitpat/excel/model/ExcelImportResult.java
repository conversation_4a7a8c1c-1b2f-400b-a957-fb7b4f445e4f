package com.linzi.pitpat.excel.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Excel导入结果类
 * 包含数据和验证错误信息
 * @param <T> 数据类型
 */
@Data
public class ExcelImportResult<T> {
    /**
     * 所有导入的数据（包括无效数据）
     */
    private List<T> allData = new ArrayList<>();

    /**
     * 有效数据（验证通过的数据）
     */
    private List<T> validData = new ArrayList<>();

    /**
     * 无效数据（验证失败的数据）
     */
    private List<T> invalidData = new ArrayList<>();

    /**
     * 错误信息映射（行索引 -> 字段错误）
     */
    private Map<Integer, Map<String, String>> errorMap;

    /**
     * 格式化的错误消息
     */
    private List<String> errorMessages = new ArrayList<>();

    /**
     * 是否存在验证错误
     */
    private boolean hasErrors;
}
