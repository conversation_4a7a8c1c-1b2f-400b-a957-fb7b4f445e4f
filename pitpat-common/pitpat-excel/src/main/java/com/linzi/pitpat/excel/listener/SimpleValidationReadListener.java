package com.linzi.pitpat.excel.listener;

import com.linzi.pitpat.excel.model.ExcelImportResult;
import com.linzi.pitpat.excel.model.ValidationResult;
import com.linzi.pitpat.excel.validator.ExcelValidator;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * 简化的验证读取监听器
 * @param <T> 数据类型
 */
public class SimpleValidationReadListener<T> implements cn.idev.excel.read.listener.ReadListener<T> {
    private final ExcelImportResult<T> result;
    private final ExcelValidator<T> validator;
    private final boolean skipInvalid;

    /**
     * 创建监听器
     * @param result 导入结果对象
     * @param validator 验证器
     * @param skipInvalid 是否跳过无效数据
     */
    public SimpleValidationReadListener(ExcelImportResult<T> result, ExcelValidator<T> validator, boolean skipInvalid) {
        this.result = result;
        this.validator = validator;
        this.skipInvalid = skipInvalid;

        // 初始化结果对象
        result.setValidData(new ArrayList<>());
        result.setInvalidData(new ArrayList<>());
        result.setErrorMap(new HashMap<>());
        result.setHasErrors(false);
    }

    @Override
    public void invoke(T data, cn.idev.excel.context.AnalysisContext context) {
        // 获取当前行号（从1开始）
        int rowIndex = context.readRowHolder().getRowIndex() + 1;

        // 验证数据
        ValidationResult validationResult = validator.validate(data, rowIndex);

        // 添加到结果列表
        result.getAllData().add(data);

        // 处理验证结果
        if (validationResult.isValid()) {
            result.getValidData().add(data);
        } else {
            result.getInvalidData().add(data);
            result.getErrorMap().put(rowIndex, validationResult.getErrors());
            result.setHasErrors(true);

            // 生成错误消息
            validationResult.getErrors().forEach((field, message) -> {
                result.getErrorMessages().add(String.format("第%d行 %s: %s", rowIndex, field, message));
            });
        }
    }

    @Override
    public void doAfterAllAnalysed(cn.idev.excel.context.AnalysisContext context) {
        // 无需额外处理
    }
}
