package com.linzi.pitpat.excel.service.impl;

import com.linzi.pitpat.excel.config.ExcelExportConfig;
import com.linzi.pitpat.excel.config.ExcelImportConfig;
import com.linzi.pitpat.excel.exception.ExcelWriteException;
import com.linzi.pitpat.excel.listener.BatchProcessListener;
import com.linzi.pitpat.excel.service.AdvancedExcelService;
import com.linzi.pitpat.excel.util.ExcelUtils;
import com.linzi.pitpat.excel.validator.ExcelValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 高级Excel服务实现
 */
@Service
public class AdvancedExcelServiceImpl implements AdvancedExcelService {

    @Override
    public <T> void exportWithConfig(List<T> data, ExcelExportConfig<T> config, OutputStream outputStream) {
        if (config == null) {
            throw new ExcelWriteException("config can't be null");
        }
        try {
            if (StringUtils.isEmpty(config.getSheetName())) {
                config.setSheetName("Sheet1");
            }
            ExcelUtils.exportWithConfig(data, config, outputStream);
        } catch (Exception e) {
            throw new RuntimeException("Export Excel failed", e);
        }
    }

    @Override
    public <T> void exportWithTemplate(List<T> data, Class<T> clazz, String templatePath, OutputStream outputStream) {
        try {
            ExcelExportConfig<T> config = ExcelExportConfig.builder(clazz).sheetName("Sheet1")
                    .templatePath(templatePath)
                    .useTemplate(true)
                    .build();

            // 导出
            ExcelUtils.exportWithConfig(data, config, outputStream);
        } catch (Exception e) {
            throw new RuntimeException("Export Excel with template failed", e);
        }
    }

    @Override
    public void exportMultiSheet(Map<String, List<?>> dataMap, OutputStream outputStream) {
        try {
            ExcelUtils.exportMultiSheetToFile(dataMap, outputStream);
        } catch (Exception e) {
            throw new RuntimeException("Export multi-sheet Excel failed", e);
        }
    }

    @Override
    public <T> List<T> importWithConfig(InputStream inputStream, ExcelImportConfig<T> config) {
        try {
            return ExcelUtils.readWithConfig(inputStream, config);
        } catch (Exception e) {
            throw new RuntimeException("Import Excel failed", e);
        }
    }

    @Override
    public <T> List<T> importWithoutValidation(InputStream inputStream, Class<T> clazz) {
        try {
            // 创建导入配置
            ExcelImportConfig<T> config = ExcelImportConfig.<T>builder()
                    .clazz(clazz)
                    .build();

            return ExcelUtils.readWithConfig(inputStream, config);
        } catch (Exception e) {
            throw new RuntimeException("Import Excel failed", e);
        }
    }

    @Override
    public <T> List<T> importWithValidation(InputStream inputStream, Class<T> clazz, ExcelValidator<T> validator) {
        try {
            // 创建导入配置
            ExcelImportConfig<T> config = ExcelImportConfig.<T>builder()
                    .clazz(clazz)
                    .validator(validator)
                    .skipInvalid(true)
                    .build();
            return ExcelUtils.readWithConfig(inputStream, config);
        } catch (Exception e) {
            throw new RuntimeException("Import Excel with validation failed", e);
        }
    }

    @Override
    public <T> List<T> importWithDefaultValidation(InputStream inputStream, Class<T> clazz) {
        // 使用默认验证器
        return importWithValidation(inputStream, clazz, ExcelValidator.createDefault(clazz));
    }

    @Override
    public <T> void importWithBatch(InputStream inputStream, Class<T> clazz, int batchSize, Consumer<List<T>> batchConsumer) {
        try {
            List<T> batchList = new ArrayList<>(batchSize);
            // 创建导入配置
            ExcelImportConfig<T> config = ExcelImportConfig.<T>builder()
                    .clazz(clazz)
                    .listener(new BatchProcessListener<T>(batchSize, batchConsumer, batchList))
                    .skipInvalid(true)
                    .build();
            ExcelUtils.readWithConfig(inputStream, config);

        } catch (Exception e) {
            throw new RuntimeException("Import Excel with batch failed", e);
        }
    }
}
