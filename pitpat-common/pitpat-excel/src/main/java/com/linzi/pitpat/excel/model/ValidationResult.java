package com.linzi.pitpat.excel.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Enhanced validation result model
 */
@Data
@Accessors(chain = true)
public class ValidationResult {
    /**
     * Row number in Excel (1-based)
     */
    private int rowNumber;

    /**
     * Whether the validation passed
     */
    private boolean valid;

    /**
     * The data being validated
     */
    private Object data;

    /**
     * List of validation errors
     */
    private List<ValidationError> errors = new ArrayList<>();

    /**
     * Add a validation error
     *
     * @param field   Field name
     * @param message Error message
     * @return this
     */
    public ValidationResult addError(String field, String message) {
        errors.add(new ValidationError(field, message));
        valid = false;
        return this;
    }

    /**
     * Add a validation error
     *
     * @param error Validation error
     * @return this
     */
    public ValidationResult addError(ValidationError error) {
        errors.add(error);
        valid = false;
        return this;
    }
    
    /**
     * Get field errors as a map
     * 
     * @return Map of field name to error message
     */
    public Map<String, String> getErrors() {
        Map<String, String> errorMap = new HashMap<>();
        for (ValidationError error : errors) {
            errorMap.put(error.getField(), error.getMessage());
        }
        return Collections.unmodifiableMap(errorMap);
    }

    /**
     * Create a valid result
     *
     * @param rowNumber Row number
     * @param data      Data
     * @return ValidationResult
     */
    public static ValidationResult valid(int rowNumber, Object data) {
        return new ValidationResult()
                .setRowNumber(rowNumber)
                .setValid(true)
                .setData(data);
    }

    /**
     * Create an invalid result
     *
     * @param rowNumber Row number
     * @param data      Data
     * @param field     Field name
     * @param message   Error message
     * @return ValidationResult
     */
    public static ValidationResult invalid(int rowNumber, Object data, String field, String message) {
        return new ValidationResult()
                .setRowNumber(rowNumber)
                .setValid(false)
                .setData(data)
                .addError(field, message);
    }
    
    /**
     * Creates a successful validation result
     *
     * @return Successful validation result
     */
    public static ValidationResult success() {
        return new ValidationResult()
                .setValid(true);
    }
    
    /**
     * Creates a failed validation result with map of errors
     *
     * @param errors Map of field errors
     * @return Failed validation result
     */
    public static ValidationResult failure(Map<String, String> errors) {
        ValidationResult result = new ValidationResult().setValid(false);
        errors.forEach(result::addError);
        return result;
    }
} 