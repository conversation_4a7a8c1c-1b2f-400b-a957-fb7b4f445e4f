package com.linzi.pitpat.excel.util;

import cn.idev.excel.FastExcel;
import com.linzi.pitpat.excel.config.ExcelImportConfig;
import com.linzi.pitpat.excel.converter.ZonedDateNumberConverter;
import com.linzi.pitpat.excel.converter.ZonedDateStringConverter;
import com.linzi.pitpat.excel.listener.BatchProcessListener;
import com.linzi.pitpat.excel.listener.CollectDataListener;
import com.linzi.pitpat.excel.listener.SimpleValidationReadListener;
import com.linzi.pitpat.excel.listener.ValidationReadListener;
import com.linzi.pitpat.excel.model.ExcelImportResult;
import com.linzi.pitpat.excel.validator.ExcelValidator;
import com.linzi.pitpat.exception.BizException;

import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

public class ExcelImportUtils {

    private ExcelImportUtils() {
        // 工具类不允许实例化
    }

    /**
     * 从字节数组导入Excel
     *
     * @param bytes Excel字节数组
     * @param clazz 数据类型
     * @param <T>   数据泛型
     * @return 导入的数据列表
     */
    public static <T> List<T> importFromBytes(byte[] bytes, Class<T> clazz) {
        try (InputStream inputStream = new ByteArrayInputStream(bytes)) {
            List<T> result = new ArrayList<>();
            FastExcel.read(inputStream, clazz, new CollectDataListener<>(result))
                    .sheet()
                    .doRead();
            return result;
        } catch (IOException e) {
            throw new BizException("导入Excel失败", e);
        }
    }


    /**
     * 从文件导入Excel
     *
     * @param filePath 文件路径
     * @param clazz    数据类型
     * @param <T>      数据泛型
     * @return 导入的数据列表
     */
    public static <T> List<T> importFromFile(String filePath, Class<T> clazz) {
        try (InputStream inputStream = new FileInputStream(filePath)) {
            List<T> result = new ArrayList<>();
            FastExcel.read(inputStream, clazz, new CollectDataListener<>(result))
                    .sheet()
                    .doRead();
            return result;
        } catch (IOException e) {
            throw new BizException("导入Excel失败", e);
        }
    }

    /**
     * 从文件导入Excel指定Sheet
     *
     * @param filePath  文件路径
     * @param clazz     数据类型
     * @param sheetName Sheet名称
     * @param <T>       数据泛型
     * @return 导入的数据列表
     */
    public static <T> List<T> importFromFile(String filePath, Class<T> clazz, String sheetName) {
        try (InputStream inputStream = new FileInputStream(filePath)) {
            List<T> result = new ArrayList<>();
            FastExcel.read(inputStream, clazz, new CollectDataListener<>(result))
                    .sheet(sheetName)
                    .doRead();
            return result;
        } catch (IOException e) {
            throw new BizException("导入Excel失败", e);
        }
    }

    /**
     * 带数据校验的Excel文件导入
     *
     * @param filePath  文件路径
     * @param clazz     数据类型
     * @param validator 数据校验器
     * @param <T>       数据泛型
     * @return 校验通过的数据列表
     */
    public static <T> List<T> importFromFileWithValidation(String filePath, Class<T> clazz, ExcelValidator<T> validator) {
        try (InputStream inputStream = new FileInputStream(filePath)) {
            List<T> result = new ArrayList<>();
            FastExcel.read(inputStream, clazz, new ValidationReadListener<>(result, validator))
                    .sheet()
                    .doRead();
            return result;
        } catch (IOException e) {
            throw new BizException("导入Excel失败", e);
        }
    }

    /**
     * 带数据校验的Excel文件导入指定Sheet
     *
     * @param filePath  文件路径
     * @param clazz     数据类型
     * @param validator 数据校验器
     * @param sheetName Sheet名称
     * @param <T>       数据泛型
     * @return 校验通过的数据列表
     */
    public static <T> List<T> importFromFileWithValidation(String filePath, Class<T> clazz, ExcelValidator<T> validator, String sheetName) {
        try (InputStream inputStream = new FileInputStream(filePath)) {
            List<T> result = new ArrayList<>();
            FastExcel.read(inputStream, clazz, new ValidationReadListener<>(result, validator))
                    .sheet(sheetName)
                    .doRead();
            return result;
        } catch (IOException e) {
            throw new BizException("导入Excel失败", e);
        }
    }

    /**
     * 分批处理导入Excel
     *
     * @param filePath      文件路径
     * @param clazz         数据类型
     * @param batchSize     批处理大小
     * @param batchConsumer 批处理函数
     * @param <T>           数据泛型
     */
    public static <T> void importWithBatch(String filePath, Class<T> clazz, int batchSize, Consumer<List<T>> batchConsumer) {
        try (InputStream inputStream = new FileInputStream(filePath)) {
            List<T> batchList = new ArrayList<>(batchSize);
            FastExcel.read(inputStream, clazz, new BatchProcessListener<>(batchSize, batchConsumer, batchList))
                    .sheet()
                    .doRead();
        } catch (IOException e) {
            throw new BizException("导入Excel失败", e);
        }
    }


    // 实现内部导入方法
    public static <T> List<T> readWithConfig(InputStream inputStream, ExcelImportConfig<T> config) throws IOException {
        // 创建结果列表
        List<T> result = new ArrayList<>();

        // 创建监听器
        cn.idev.excel.read.listener.ReadListener<T> listener = new CollectDataListener<>(result);

        // 调用内部方法导入
        readWithListener(inputStream, config, listener);

        return result;
    }

    /**
     * 从输入流中读取数据（带验证）
     *
     * @param inputStream 输入流
     * @return 导入结果（包含有效数据和验证错误）
     * @throws IOException IO异常
     */
    public static <T> ExcelImportResult<T> readWithValidation(InputStream inputStream, ExcelImportConfig<T> config) throws IOException {
        if (config.getValidator() == null) {
            // 如果没有设置验证器，使用默认验证器
            config.setValidator(ExcelValidator.createDefault(config.getClazz()));
        }

        // 创建验证结果
        ExcelImportResult<T> result = new ExcelImportResult<>();

        // 创建验证监听器
        SimpleValidationReadListener<T> listener = new SimpleValidationReadListener<>(
                result,
                config.getValidator(),
                config.isSkipInvalid()
        );

        // 调用内部方法导入
        readWithListener(inputStream, config, listener);

        return result;
    }

    private static <T> void readWithListener(InputStream inputStream, ExcelImportConfig<T> config,
                                             cn.idev.excel.read.listener.ReadListener<T> listener) {
        // 使用FastExcel的API进行导入
        if (config.getSheetName() != null) {
            FastExcel.read(inputStream, config.getClazz(), listener)
                    .sheet(config.getSheetName())
                    .registerConverter(new ZonedDateNumberConverter())
                    .registerConverter(new ZonedDateStringConverter())
                    .doRead();
        } else if (config.getSheetNo() != null) {
            FastExcel.read(inputStream, config.getClazz(), listener)
                    .sheet(config.getSheetNo())
                    .registerConverter(new ZonedDateNumberConverter())
                    .registerConverter(new ZonedDateStringConverter())
                    .doRead();
        } else {
            FastExcel.read(inputStream, config.getClazz(), listener)
                    .sheet()
                    .registerConverter(new ZonedDateNumberConverter())
                    .registerConverter(new ZonedDateStringConverter())
                    .doRead();
        }
    }
}
