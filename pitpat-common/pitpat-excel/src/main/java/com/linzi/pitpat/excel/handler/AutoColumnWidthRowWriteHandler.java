package com.linzi.pitpat.excel.handler;

import cn.idev.excel.write.handler.RowWriteHandler;
import cn.idev.excel.write.handler.context.RowWriteHandlerContext;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * Row写入处理器 - 自动调整列宽
 * 实现在导出Excel时自动调整列宽功能，基于行处理
 */
public class AutoColumnWidthRowWriteHandler implements RowWriteHandler {
    private static final Logger log = LoggerFactory.getLogger(AutoColumnWidthRowWriteHandler.class);

    // 最小列宽
    private static final int MIN_COLUMN_WIDTH = 2048;
    // 最大列宽
    private static final int MAX_COLUMN_WIDTH = 25600;
    // 字符宽度因子
    private static final int WIDTH_FACTOR = 256;

    // 字符宽度系数
    private static final double CHINESE_CHAR_WIDTH = 2.2;
    private static final double LETTER_CHAR_WIDTH = 1.1;
    private static final double DIGIT_CHAR_WIDTH = 0.9;
    private static final double PUNCT_CHAR_WIDTH = 1.0;

    // 数据类型宽度倍率
    private static final double NUMBER_WIDTH_RATIO = 1.1;

    // 最大列宽记录
    private final Map<String, Map<Integer, Integer>> maxColumnWidths = new HashMap<>();
    // 处理过的行数记录
    private final Map<String, Integer> processedRowCount = new HashMap<>();
    // 已处理的工作表
    private final Map<String, Boolean> processedSheets = new HashMap<>();

    // 最大处理行数
    private final int maxProcessRows;

    /**
     * 创建默认的自动列宽处理器
     */
    public AutoColumnWidthRowWriteHandler() {
        this(500);
    }

    /**
     * 创建自定义的自动列宽处理器
     *
     * @param maxProcessRows 最大处理行数
     */
    public AutoColumnWidthRowWriteHandler(int maxProcessRows) {
        this.maxProcessRows = maxProcessRows;
        log.debug("创建自动列宽行处理器: maxProcessRows={}", maxProcessRows);
    }

    @Override
    public void beforeRowCreate(RowWriteHandlerContext context) {
        // 行创建前不需要处理
    }

    @Override
    public void afterRowCreate(RowWriteHandlerContext context) {
        // 行创建后不需要处理
    }

    @Override
    public void afterRowDispose(RowWriteHandlerContext context) {
        try {
            // 获取当前行和工作表
            Row row = context.getRow();
            Sheet sheet = context.getWriteSheetHolder().getSheet();

            if (row == null || sheet == null) {
                return;
            }

            // 获取工作表名称
            String sheetName = sheet.getSheetName();

            // 获取当前工作表的最大列宽记录
            Map<Integer, Integer> sheetMaxWidths = maxColumnWidths.computeIfAbsent(sheetName, k -> new HashMap<>());

            // 获取当前工作表已处理行数
            int rowsProcessed = processedRowCount.getOrDefault(sheetName, 0);

            // 如果已达到最大处理行数，检查是否需要应用列宽
            if (rowsProcessed >= maxProcessRows && !processedSheets.containsKey(sheetName)) {
                applyColumnWidths(sheet, sheetMaxWidths);
                processedSheets.put(sheetName, Boolean.TRUE);
                log.debug("已处理 {} 行，应用列宽调整到工作表 {}", rowsProcessed, sheetName);
                return;
            }

            // 增加处理行数计数
            processedRowCount.put(sheetName, rowsProcessed + 1);

            // 处理每个单元格，计算并记录所需宽度
            int lastCellNum = row.getLastCellNum();
            for (int i = 0; i < lastCellNum; i++) {
                Cell cell = row.getCell(i);
                if (cell != null) {
                    try {
                        int requiredWidth = estimateCellWidth(cell);

                        // 更新最大宽度记录
                        int currentMaxWidth = sheetMaxWidths.getOrDefault(i, MIN_COLUMN_WIDTH);
                        if (requiredWidth > currentMaxWidth) {
                            sheetMaxWidths.put(i, requiredWidth);
                            log.trace("列 {} 宽度更新为 {}", i, requiredWidth);
                        }
                    } catch (Exception e) {
                        log.trace("处理单元格 [{},{}] 宽度时出错", row.getRowNum(), i, e);
                    }
                }
            }

            // 如果是第一行（通常是表头），特殊处理
            if (row.getRowNum() == 0 && lastCellNum > 0) {
                // 暂时应用列宽，确保表头可见
                // 后续会根据内容再次调整
                applyColumnWidths(sheet, sheetMaxWidths);
                log.debug("应用表头行的列宽");
            }
            
            // 周期性调整列宽
            // 每处理maxProcessRows行调整一次，避免过于频繁
            if (row.getRowNum() % maxProcessRows == 0 && row.getRowNum() > 0) {
                applyColumnWidths(sheet, sheetMaxWidths);
                log.debug("已处理到第{}行，应用阶段性列宽调整", row.getRowNum());
            }
            
            // 注意：在这种Row处理回调模式下，无法可靠地检测到最后一行
            // POI会在所有行处理完后进行收尾工作
        } catch (Exception e) {
            log.warn("行处理后自动调整列宽出错", e);
        }
    }

    /**
     * 估算单元格所需宽度
     */
    private int estimateCellWidth(Cell cell) {
        try {
            CellType cellType = cell.getCellType();

            // 根据单元格类型处理
            switch (cellType) {
                case STRING:
                    String text = cell.getStringCellValue();
                    return estimateTextWidth(text);

                case NUMERIC:
                    // 数值型单元格
                    double numValue = cell.getNumericCellValue();
                    return estimateNumericWidth(numValue);

                case BOOLEAN:
                    // 布尔型单元格 (TRUE/FALSE)
                    return 1500;

                case FORMULA:
                    // 公式单元格，尝试获取计算值
                    try {
                        CellType cachedType = cell.getCachedFormulaResultType();
                        if (cachedType == CellType.STRING) {
                            String formulaText = cell.getStringCellValue();
                            return estimateTextWidth(formulaText);
                        } else if (cachedType == CellType.NUMERIC) {
                            double formulaNum = cell.getNumericCellValue();
                            return estimateNumericWidth(formulaNum);
                        }
                    } catch (Exception e) {
                        // 公式无法计算，使用默认宽度
                        return 3000;
                    }
                    break;

                default:
                    // 其他类型使用默认宽度
                    return 2500;
            }

            return MIN_COLUMN_WIDTH;
        } catch (Exception e) {
            log.trace("估算单元格宽度出错", e);
            return MIN_COLUMN_WIDTH;
        }
    }

    /**
     * 估算文本宽度
     */
    private int estimateTextWidth(String text) {
        if (text == null || text.isEmpty()) {
            return MIN_COLUMN_WIDTH;
        }

        // 根据字符类型估算宽度
        double estimatedWidth = 0;
        for (char c : text.toCharArray()) {
            if (isChinese(c)) {
                estimatedWidth += CHINESE_CHAR_WIDTH;
            } else if (Character.isDigit(c)) {
                estimatedWidth += DIGIT_CHAR_WIDTH;
            } else if (isPunctuation(c)) {
                estimatedWidth += PUNCT_CHAR_WIDTH;
            } else {
                estimatedWidth += LETTER_CHAR_WIDTH;
            }
        }

        // 对于较短的文本，添加更多的额外空间
        double paddingFactor = text.length() < 10 ? 1.5 : 1.2;

        // 转换为Excel列宽单位
        return (int) (estimatedWidth * WIDTH_FACTOR * paddingFactor) + WIDTH_FACTOR / 2;
    }

    /**
     * 估算数值宽度
     */
    private int estimateNumericWidth(double value) {
        // 转为字符串计算长度
        String numText = String.valueOf(value);

        // 对于数字，使用较小的宽度系数
        double estimatedWidth = numText.length() * DIGIT_CHAR_WIDTH;

        // 应用数值类型的宽度调整
        return (int) (estimatedWidth * WIDTH_FACTOR * NUMBER_WIDTH_RATIO) + WIDTH_FACTOR / 2;
    }

    /**
     * 判断字符是否为中文
     */
    private boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        return ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS_SUPPLEMENT;
    }

    /**
     * 判断字符是否为标点符号
     */
    private boolean isPunctuation(char c) {
        return c == '.' || c == ',' || c == ':' || c == ';' || c == '!' || c == '?'
                || c == '(' || c == ')' || c == '[' || c == ']' || c == '{' || c == '}'
                || c == '+' || c == '-' || c == '*' || c == '/' || c == '%' || c == '='
                || c == '<' || c == '>' || c == '&' || c == '|' || c == '^';
    }

    /**
     * 应用列宽到工作表
     */
    private void applyColumnWidths(Sheet sheet, Map<Integer, Integer> columnWidths) {
        if (sheet == null || columnWidths.isEmpty()) {
            return;
        }

        // 应用每列的最大宽度
        for (Map.Entry<Integer, Integer> entry : columnWidths.entrySet()) {
            int colIndex = entry.getKey();
            int width = entry.getValue();

            try {
                // 确保宽度在合理范围内
                width = Math.max(width, MIN_COLUMN_WIDTH);
                width = Math.min(width, MAX_COLUMN_WIDTH);

                // 设置列宽
                sheet.setColumnWidth(colIndex, width);
                log.trace("设置列 {} 宽度为 {}", colIndex, width);
            } catch (Exception e) {
                log.warn("设置列 {} 宽度时出错", colIndex, e);
            }
        }
    }
}
