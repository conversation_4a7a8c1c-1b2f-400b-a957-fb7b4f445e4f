package com.linzi.pitpat.excel.handler;

import org.apache.poi.ss.usermodel.Sheet;
import cn.idev.excel.write.handler.SheetWriteHandler;
import cn.idev.excel.write.handler.context.SheetWriteHandlerContext;

/**
 * Sheet写入处理器 - 冻结首行
 * 实现在导出Excel时冻结首行的功能
 */
public class FreezeFirstRowSheetWriteHandler implements SheetWriteHandler {

    @Override
    public void beforeSheetCreate(SheetWriteHandlerContext context) {
        // 在创建Sheet之前不需要操作
    }

    @Override
    public void afterSheetCreate(SheetWriteHandlerContext context) {
        // 获取当前Sheet
        Sheet sheet = context.getWriteSheetHolder().getSheet();
        
        // 冻结首行（从第二行开始滚动）
        // 参数含义: 冻结的行数, 冻结的列数, 第一个可见行, 第一个可见列
        sheet.createFreezePane(0, 1, 0, 1);
    }
} 