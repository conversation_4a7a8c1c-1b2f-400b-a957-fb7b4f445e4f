package com.linzi.pitpat.excel.constants;

public class ExcelConstant {

    // 样式配置键
    //自动调整最大行数，超过达到该行数则不再分析和调整，进行最后一次调整列宽后返回
    public static final String AUTO_WIDTH_ANALYZE_ROW_NAME = "autoWidthMaxAnalyzeRow";
    //自动调整列宽的实际行数，从导出的数据 list中获取
    public static final String AUTO_WIDTH_REAL_ROW_NUMBER_NAME = "autoWidthRowNumber";

    public static final Integer AUTO_WIDTH_MAX_ANALYZE_ROW = 100;
}
