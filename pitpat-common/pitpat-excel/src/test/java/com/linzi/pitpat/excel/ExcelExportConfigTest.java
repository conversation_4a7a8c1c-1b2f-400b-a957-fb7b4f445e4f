package com.linzi.pitpat.excel;

import com.linzi.pitpat.excel.config.ExcelExportConfig;
import com.linzi.pitpat.excel.util.ExcelUtils;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Test class for ExcelExportConfig functionality
 */
public class ExcelExportConfigTest {

    @TempDir
    Path tempDir;

    @Test
    public void testExportConfigBuilder() throws IOException {
        // Create test data
        List<TestUser> users = createTestUsers();

        // Create export config using builder
        ExcelExportConfig<TestUser> config = ExcelExportConfig.builder(TestUser.class)
                .sheetName("Users")
                .autoColumnWidth(true)
                .freezeFirstRow(true)
                .excludeFields(new String[]{"password"})
                .build();

        // Create temp file for export
        File exportFile = tempDir.resolve("users_export.xlsx").toFile();

        // Export using config
        ExcelUtils.exportWithConfig(users, config, exportFile);

        // Verify file was created
        assertTrue(exportFile.exists());
        assertTrue(exportFile.length() > 0);
    }


    /**
     * Create test user data
     */
    private List<TestUser> createTestUsers() {
        List<TestUser> users = new ArrayList<>();

        TestUser user1 = new TestUser();
        user1.setId(1L);
        user1.setUsername("user1");
        user1.setEmail("<EMAIL>");
        user1.setPassword("password1");
        users.add(user1);

        TestUser user2 = new TestUser();
        user2.setId(2L);
        user2.setUsername("user2");
        user2.setEmail("<EMAIL>");
        user2.setPassword("password2");
        users.add(user2);

        return users;
    }

    /**
     * Test user class for Excel export
     */
    @Data
    public static class TestUser {
        private Long id;
        private String username;
        private String email;
        private String password;
    }
}
