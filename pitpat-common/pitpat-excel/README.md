# PitPat Excel

PitPat Excel 是一个强大的 Excel 导入导出模块，提供了简单易用的 API，支持数据校验、自定义转换器、批处理等高级功能。

## 功能特点

- **简单易用**：提供简洁的 API，快速实现 Excel 导入导出
- **数据校验**：支持 JSR-380 Bean Validation 注解校验
- **自定义校验**：支持自定义校验器
- **批量处理**：支持大数据量的批量处理
- **多 Sheet 支持**：支持导出多个 Sheet
- **模板支持**：支持使用模板导出 Excel
- **自动列宽**：支持自动调整列宽
- **冻结首行**：支持冻结首行
- **日期时间支持**：内置对 Java 8 日期时间类型的支持
- **自定义样式**：支持自定义样式配置

## 快速开始

### Maven 依赖

```xml
<dependency>
    <groupId>com.linzi.pitpat</groupId>
    <artifactId>pitpat-excel</artifactId>
    <version>${pitpat.version}</version>
</dependency>
```

### 定义数据模型

使用 `@ExcelProperty` 注解标记需要导出的字段，使用 `@ExcelIgnore` 注解标记不需要导出的字段。

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserExcelDTO {

    @ExcelIgnore
    private Long id;

    @ExcelProperty(value = "用户名")
    @NotBlank(message = "用户名不能为空")
    @Size(max = 50, message = "用户名长度不能超过50个字符")
    private String username;

    @ExcelProperty(value = "性别")
    @NotBlank(message = "性别不能为空")
    @Pattern(regexp = "^[男女]$", message = "性别只能是男或女")
    private String gender;

    @ExcelProperty(value = "年龄")
    @Min(value = 0, message = "年龄不能小于0")
    @Max(value = 120, message = "年龄不能大于120")
    private Integer age;

    @ExcelProperty(value = "手机号")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @ExcelProperty(value = "电子邮箱")
    @Email(message = "邮箱格式不正确")
    private String email;

    @ExcelProperty(value = "地址")
    private String address;

    @ExcelProperty(value = "生日")
    private LocalDateTime birthday;

    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "创建时间")
    private ZonedDateTime createTime;

    @ExcelIgnore
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
```

## 核心 API 使用指南

### ExcelUtils

`ExcelUtils` 是一个静态工具类，提供了基本的 Excel 导入导出功能。

#### 导出 Excel

```java
// 导出到输出流
ExcelUtils.export(dataList, UserExcelDTO.class, outputStream);

// 导出到文件
ExcelUtils.exportToFile(dataList, UserExcelDTO.class, "users.xlsx");

// 使用配置导出
ExcelExportConfig<UserExcelDTO> config = ExcelExportConfig.builder(UserExcelDTO.class)
        .sheetName("用户数据")
        .autoColumnWidth(true)
        .freezeFirstRow(true)
        .build();
ExcelUtils.exportWithConfig(dataList, config, outputStream);

// 导出多个 Sheet
Map<String, List<?>> dataMap = new HashMap<>();
dataMap.put("所有用户", allUsers);
dataMap.put("男性用户", maleUsers);
dataMap.put("女性用户", femaleUsers);
ExcelUtils.exportMultiSheetToFile(dataMap, outputStream);
```

#### 导入 Excel

```java
// 从文件导入
List<UserExcelDTO> users = ExcelUtils.importFromFile("users.xlsx", UserExcelDTO.class);

// 从输入流导入（带配置）
ExcelImportConfig<UserExcelDTO> config = ExcelImportConfig.<UserExcelDTO>builder()
        .clazz(UserExcelDTO.class)
        .headRowNumber(1)
        .skipInvalid(true)
        .build();
List<UserExcelDTO> users = ExcelUtils.readWithConfig(inputStream, config);

// 带验证导入
ExcelImportResult<UserExcelDTO> result = ExcelUtils.readWithValidation(inputStream, config);
List<UserExcelDTO> validUsers = result.getValidData();
List<UserExcelDTO> invalidUsers = result.getInvalidData();
Map<Integer, Map<String, String>> errorMap = result.getErrorMap();

// 批量处理导入
ExcelUtils.importWithBatch("users.xlsx", UserExcelDTO.class, 100, batchData -> {
    // 处理每批数据
    batchService.processBatch(batchData);
});
```

### ExcelService

`ExcelService` 是一个服务接口，提供了基于 Web 应用的 Excel 导入导出功能。

#### 导出 Excel

```java
// 导出数据
excelService.exportExcel(response, dataList, UserExcelDTO.class, "用户数据");

// 导出模板
excelService.exportExcelTemplate(response, UserExcelDTO.class, "用户模板");
```

#### 导入 Excel

```java
// 导入数据（带默认验证）
List<UserExcelDTO> users = excelService.importExcel(file, UserExcelDTO.class);

// 导入数据（不带验证）
List<UserExcelDTO> users = excelService.importExcelWithoutValidation(file, UserExcelDTO.class);

// 导入数据（带自定义验证）
ExcelValidator<UserExcelDTO> validator = new CustomValidator<>();
List<UserExcelDTO> users = excelService.importExcelWithValidation(file, UserExcelDTO.class, validator);
```

### AdvancedExcelService

`AdvancedExcelService` 是一个高级服务接口，提供了更多自定义功能。

#### 高级导出

```java
// 使用配置导出
ExcelExportConfig<UserExcelDTO> config = ExcelExportConfig.builder(UserExcelDTO.class)
        .sheetName("用户数据")
        .autoColumnWidth(true)
        .freezeFirstRow(true)
        .excludeFields(new String[]{"password"})
        .build();
advancedExcelService.exportWithConfig(dataList, config, outputStream);

// 使用模板导出
advancedExcelService.exportWithTemplate(dataList, UserExcelDTO.class, "template.xlsx", outputStream);

// 导出多个 Sheet
Map<String, List<?>> dataMap = new HashMap<>();
dataMap.put("所有用户", allUsers);
dataMap.put("男性用户", maleUsers);
dataMap.put("女性用户", femaleUsers);
advancedExcelService.exportMultiSheet(dataMap, outputStream);
```

#### 高级导入

```java
// 使用配置导入
ExcelImportConfig<UserExcelDTO> config = ExcelImportConfig.<UserExcelDTO>builder()
        .clazz(UserExcelDTO.class)
        .headRowNumber(1)
        .skipInvalid(true)
        .validator(ExcelValidator.createDefault(UserExcelDTO.class))
        .build();
List<UserExcelDTO> users = advancedExcelService.importWithConfig(inputStream, config);

// 不带验证导入
List<UserExcelDTO> users = advancedExcelService.importWithoutValidation(inputStream, UserExcelDTO.class);

// 带验证导入
ExcelValidator<UserExcelDTO> validator = new CustomValidator<>();
List<UserExcelDTO> users = advancedExcelService.importWithValidation(inputStream, UserExcelDTO.class, validator);

// 使用默认验证导入
List<UserExcelDTO> users = advancedExcelService.importWithDefaultValidation(inputStream, UserExcelDTO.class);

// 批量处理导入
advancedExcelService.importWithBatch(inputStream, UserExcelDTO.class, 100, batchData -> {
    // 处理每批数据
    batchService.processBatch(batchData);
});
```

## 高级功能

### 自定义验证器

```java
public class CustomValidator<T> implements ExcelValidator<T> {
    @Override
    public ValidationResult validate(T data) {
        ValidationResult result = ValidationResult.success();
        
        // 自定义验证逻辑
        if (data instanceof UserExcelDTO) {
            UserExcelDTO user = (UserExcelDTO) data;
            if (user.getUsername() != null && user.getUsername().contains("admin")) {
                result.addError("username", "用户名不能包含 admin");
            }
        }
        
        return result;
    }
}
```

### 自定义转换器

```java
public class CustomConverter implements Converter<LocalDate> {
    @Override
    public Class<?> supportJavaTypeKey() {
        return LocalDate.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public LocalDate convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty,
                                      GlobalConfiguration globalConfiguration) {
        // 自定义转换逻辑
        return LocalDate.parse(cellData.getStringValue());
    }

    @Override
    public WriteCellData<?> convertToExcelData(LocalDate value, ExcelContentProperty contentProperty,
                                              GlobalConfiguration globalConfiguration) {
        // 自定义转换逻辑
        return new WriteCellData<>(value.format(DateTimeFormatter.ISO_DATE));
    }
}
```

### 自定义处理器

```java
public class CustomRowWriteHandler implements RowWriteHandler {
    @Override
    public void beforeRowCreate(RowWriteHandlerContext context) {
        // 行创建前的处理
    }

    @Override
    public void afterRowCreate(RowWriteHandlerContext context) {
        // 行创建后的处理
    }

    @Override
    public void afterRowDispose(RowWriteHandlerContext context) {
        // 行处理后的处理
    }
}
```

## 最佳实践

1. **使用注解标记字段**：使用 `@ExcelProperty` 和 `@ExcelIgnore` 注解标记需要导出和不需要导出的字段。
2. **使用验证注解**：使用 JSR-380 Bean Validation 注解进行数据验证。
3. **批量处理大数据量**：对于大数据量的导入，使用批量处理功能。
4. **自定义验证器**：对于复杂的验证逻辑，实现自定义验证器。
5. **使用配置类**：使用 `ExcelExportConfig` 和 `ExcelImportConfig` 配置类进行更精细的控制。

## 示例代码

更多示例代码请参考 `ExcelController` 和 `AdvancedExcelController` 类。

## 依赖

- FastExcel 1.2.0
- Apache POI 5.3.0
- Spring Boot Web
- Spring Boot Validation
- Lombok

## 许可证

[Apache License 2.0](LICENSE)

## 开源协议

Apache License 2.0


## 流程图

1. Excel 导出流程
```mermaid
flowchart TD
    A[开始导出] --> B[准备数据对象List]
    B --> C{选择导出方式}
    C -->|简单导出| D[PitpatExcel.export]
    C -->|模板导出| E[PitpatExcel.exportWithTemplate]
    C -->|多Sheet导出| F[PitpatExcel.exportMultiSheet]
    C -->|自定义配置导出| G[创建ExcelExportConfig]
    G --> H[PitpatExcel.exportWithConfig]
    
    D --> I[FastExcel.write处理]
    E --> I
    F --> I
    H --> I
    
    I --> J[写入OutputStream]
    J --> K[导出完成]
```

2. Excel 导入流程(含Java Validation)

```mermaid
flowchart TD
    A[开始导入] --> B[获取Excel文件InputStream]
    B --> C{选择导入方式}
    C -->|简单导入| D[PitpatExcel.read]
    C -->|带验证的导入| E[创建验证器]
    C -->|分批处理导入| F[PitpatExcel.readWithBatch]
    C -->|自定义配置导入| G[创建ExcelImportConfig]
    
    E -->|自定义验证器| E1[创建UserExcelValidator]
    E -->|Java Validation| E2[创建BeanValidationAdapter]
    E1 --> E3[PitpatExcel.readWithValidation]
    E2 --> E3
    
    G --> H[PitpatExcel.readWithConfig]
    
    D --> I[FastExcel.read处理]
    E3 --> I
    F --> I
    H --> I
    
    I --> J[读取并解析Excel]
    J --> K[实例化业务对象]
    K --> L{需要验证?}
    
    L -->|不需要| N[返回数据对象列表]
    L -->|需要| M[执行数据验证]
    M --> O{验证通过?}
    O -->|通过| N
    O -->|不通过| P[收集错误信息]
    P --> Q[过滤无效数据]
    Q --> N
    
    N --> R[导入完成]
```

3. Java Validation与ExcelValidator集成

```mermaid 
flowchart TD
    A["Java Bean对象"] --> B{"添加验证注解"}
    B --> B1["@NotBlank 非空验证"]
    B --> B2["@Size 长度验证"]
    B --> B3["@Pattern 正则验证"]
    B --> B4["@Min/@Max 范围验证"]
    B --> B5["@Email 邮箱验证"]
    B --> B6["自定义验证注解"]
    
    B1 --> C["创建BeanValidationAdapter"]
    B2 --> C
    B3 --> C
    B4 --> C
    B5 --> C
    B6 --> C
    
    C --> D["实现ExcelValidator接口"]
    
    D --> E["validate方法"]
    D --> F["validateBatch方法"]
    
    E --> G["调用javax.validation.Validator"]
    G --> H["获取验证结果"]
    H --> I["转换为错误信息Map"]
    
    J["Excel导入流程"] --> K["使用BeanValidationAdapter"]
    K --> L["PitpatExcel.readWithValidation"]
    L --> M["验证数据并收集错误"]
    M --> N["返回有效数据列表"]
```

```mermaid
classDiagram
    class PitpatExcel {
        +export()
        +exportToFile()
        +exportWithTemplate()
        +exportMultiSheet()
        +exportWithConfig()
        +read()
        +readFromFile()
        +readWithValidation()
        +readWithBatch()
        +readWithConfig()
    }
    
    class ExcelValidator~T~ {
        <<interface>>
        +validate(data, rowIndex)
        +validateBatch(dataList)
        +isValid(data, rowIndex)
        +getValidData(dataList)
    }
    
    class UserExcelValidator {
        +validate(data, rowIndex)
        +validateBatch(dataList)
    }
    
    class BeanValidationAdapter~T~ {
        -Validator validator
        -Class<T> clazz
        +BeanValidationAdapter(clazz)
        +validate(data, rowIndex)
        +validateBatch(dataList)
        +getValidData(dataList)
    }
    
    class UserExcelDTO {
        +id: Long
        +username: String
        +gender: String
        +age: Integer
        +phone: String
        +email: String
        +address: String
        +birthday: Date
        +createTime: Date
        +updateTime: Date
    }
    
    PitpatExcel ..> ExcelValidator: 使用
    ExcelValidator <|.. UserExcelValidator: 实现
    ExcelValidator <|.. BeanValidationAdapter: 实现
    UserExcelValidator ..> UserExcelDTO: 验证
    BeanValidationAdapter ..> UserExcelDTO: 验证
```
