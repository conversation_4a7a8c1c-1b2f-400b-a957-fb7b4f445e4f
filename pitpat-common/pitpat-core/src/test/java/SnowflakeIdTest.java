import com.linzi.pitpat.core.util.SnowflakeId;
import org.springframework.util.Assert;

import java.net.NetworkInterface;
import java.security.SecureRandom;
import java.util.Enumeration;



public class SnowflakeIdTest {
    public static void main(String[] args) throws InterruptedException {
        Long nodeId = createNodeId();
        System.out.printf("nodeId=%d",nodeId);
        Assert.state(0 != nodeId, "机器码为0");
        for (int i=0;i<15;i++){
            Thread.sleep(100);
            long l = SnowflakeId.nextId();
            System.out.println(l);
            if ("1651990864068640".length()!=((l+"").length())) {
                System.out.println("长度不相等");
            }
        }
    }

    public static Long createNodeId() {
        final long MAX_WORKER_ID = ~(-1L << 5);
        long nodeId;
        try {
            StringBuilder sb = new StringBuilder();
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                byte[] mac = networkInterface.getHardwareAddress();
                if (mac != null) {
                    for (int i = 0; i < mac.length; i++) {
                        sb.append(String.format("%02X", mac[i]));
                    }
                }
            }
            nodeId = sb.toString().hashCode();
        } catch (Exception ex) {
            nodeId = (new SecureRandom().nextInt());
        }
        nodeId = nodeId & MAX_WORKER_ID;
        return nodeId;

    }
}
