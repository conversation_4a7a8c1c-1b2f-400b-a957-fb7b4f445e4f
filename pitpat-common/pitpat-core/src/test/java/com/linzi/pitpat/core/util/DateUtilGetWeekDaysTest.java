package com.linzi.pitpat.core.util;

import com.linzi.pitpat.core.util.dto.WeekDayDateDto;
import org.junit.jupiter.api.Test;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试 DateUtil 中基于 ZonedDateTime 的 getWeekDays 方法
 */
public class DateUtilGetWeekDaysTest {

    @Test
    public void testGetWeekDaysWithZonedDateTime() {
        // 测试2024年1月的周信息
        ZonedDateTime january2024 = ZonedDateTime.of(2024, 1, 15, 10, 0, 0, 0, ZoneId.systemDefault());
        List<WeekDayDateDto> result = DateUtil.getWeekDays(january2024);
        
        assertNotNull(result);
        assertFalse(result.isEmpty());
        
        System.out.println("2024年1月的周信息:");
        for (int i = 0; i < result.size(); i++) {
            WeekDayDateDto weekInfo = result.get(i);
            System.out.println("第" + (i + 1) + "个周四: " + weekInfo.getWeekStart() + 
                             " - " + weekInfo.getWeekEnd() + 
                             " (第" + weekInfo.getWeekN() + "周)");
            
            // 验证基本属性
            assertNotNull(weekInfo.getWeekStart());
            assertNotNull(weekInfo.getWeekEnd());
            assertNotNull(weekInfo.getBeijingWeekStart());
            assertNotNull(weekInfo.getBeijingWeekEnd());
            assertTrue(weekInfo.getWeekN() > 0);
            // 注意：最后一个周四可能属于下个月的第1周，这是正确的业务逻辑
            assertNotNull(weekInfo.getCurrentMonth());
            assertTrue(weekInfo.getMonth() >= 1 && weekInfo.getMonth() <= 12);
        }
    }

    @Test
    public void testGetWeekDaysCurrentMonth() {
        // 测试当前月份
        ZonedDateTime now = ZonedDateTime.now();
        List<WeekDayDateDto> result = DateUtil.getWeekDays(now);
        
        assertNotNull(result);
        
        System.out.println("\n当前月份的周信息:");
        for (int i = 0; i < result.size(); i++) {
            WeekDayDateDto weekInfo = result.get(i);
            System.out.println("第" + (i + 1) + "个周四: " + weekInfo.getWeekStart() + 
                             " - " + weekInfo.getWeekEnd() + 
                             " (第" + weekInfo.getWeekN() + "周)");
            
            // 验证月份信息正确
            assertEquals(now.getMonthValue(), weekInfo.getMonth());
            assertEquals(now.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM")), 
                        weekInfo.getCurrentMonth());
        }
    }

    @Test
    public void testGetWeekDaysFebruary2024() {
        // 测试2024年2月（闰年）
        ZonedDateTime february2024 = ZonedDateTime.of(2024, 2, 15, 10, 0, 0, 0, ZoneId.systemDefault());
        List<WeekDayDateDto> result = DateUtil.getWeekDays(february2024);
        
        assertNotNull(result);
        assertFalse(result.isEmpty());
        
        System.out.println("\n2024年2月的周信息:");
        for (int i = 0; i < result.size(); i++) {
            WeekDayDateDto weekInfo = result.get(i);
            System.out.println("第" + (i + 1) + "个周四: " + weekInfo.getWeekStart() + 
                             " - " + weekInfo.getWeekEnd() + 
                             " (第" + weekInfo.getWeekN() + "周)");
            
            assertEquals("2024-02", weekInfo.getCurrentMonth());
            assertEquals(2, weekInfo.getMonth());
        }
    }

    @Test
    public void testGetWeekDaysDecember() {
        // 测试12月（年末）
        ZonedDateTime december2024 = ZonedDateTime.of(2024, 12, 15, 10, 0, 0, 0, ZoneId.systemDefault());
        List<WeekDayDateDto> result = DateUtil.getWeekDays(december2024);
        
        assertNotNull(result);
        assertFalse(result.isEmpty());
        
        System.out.println("\n2024年12月的周信息:");
        for (int i = 0; i < result.size(); i++) {
            WeekDayDateDto weekInfo = result.get(i);
            System.out.println("第" + (i + 1) + "个周四: " + weekInfo.getWeekStart() + 
                             " - " + weekInfo.getWeekEnd() + 
                             " (第" + weekInfo.getWeekN() + "周)");
            
            assertEquals("2024-12", weekInfo.getCurrentMonth());
            assertEquals(12, weekInfo.getMonth());
        }
    }

    @Test
    public void testWeekDayDateDtoProperties() {
        ZonedDateTime testDate = ZonedDateTime.of(2024, 6, 15, 10, 0, 0, 0, ZoneId.systemDefault());
        List<WeekDayDateDto> result = DateUtil.getWeekDays(testDate);
        
        assertNotNull(result);
        assertFalse(result.isEmpty());
        
        // 验证第一个周的详细属性
        WeekDayDateDto firstWeek = result.get(0);
        
        // 验证所有必要字段都不为空
        assertNotNull(firstWeek.getWeekStart());
        assertNotNull(firstWeek.getWeekEnd());
        assertNotNull(firstWeek.getBeijingWeekStart());
        assertNotNull(firstWeek.getBeijingWeekEnd());
        assertNotNull(firstWeek.getWeekStartStr());
        assertNotNull(firstWeek.getWeekEndStr());
        assertNotNull(firstWeek.getCurrentMonth());
        
        // 验证数值范围
        assertTrue(firstWeek.getWeekN() > 0);
        assertTrue(firstWeek.getWeek() >= 1 && firstWeek.getWeek() <= 7);
        assertTrue(firstWeek.getMonth() >= 1 && firstWeek.getMonth() <= 12);
        
        // 验证时间逻辑
        assertTrue(firstWeek.getWeekStart().isBefore(firstWeek.getWeekEnd()) || 
                  firstWeek.getWeekStart().equals(firstWeek.getWeekEnd()));
        
        System.out.println("\n详细属性验证:");
        System.out.println("周开始: " + firstWeek.getWeekStart());
        System.out.println("周结束: " + firstWeek.getWeekEnd());
        System.out.println("北京时间周开始: " + firstWeek.getBeijingWeekStart());
        System.out.println("北京时间周结束: " + firstWeek.getBeijingWeekEnd());
        System.out.println("第几周: " + firstWeek.getWeekN());
        System.out.println("星期几: " + firstWeek.getWeek());
        System.out.println("当前月份: " + firstWeek.getCurrentMonth());
        System.out.println("月份数字: " + firstWeek.getMonth());
    }
}
