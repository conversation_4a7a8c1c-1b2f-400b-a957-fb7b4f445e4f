package com.linzi.pitpat.core.util;

import org.junit.jupiter.api.Test;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.TimeZone;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class DateUtilConvertTimeZoneTest {

    @Test
    public void testConvertTimeZone_AsiaShanghaiToUTC() {
        // 创建一个固定的时间点进行测试
        Date date = new Date(1234567890000L); // 2009-02-14 07:31:30 UTC
        
        // 使用DateUtil.convertTimeZone方法
        Date convertedDate = DateUtil.convertTimeZone(date, "Asia/Shanghai", "UTC");
        
        // 使用ZonedDateTime API实现相同功能
        ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.of("Asia/Shanghai"));
        ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));
        Date expectedDate = Date.from(utcDateTime.toInstant());
        
        // 验证结果一致
        assertEquals(expectedDate, convertedDate);
        assertEquals(expectedDate.getTime(), convertedDate.getTime());
    }
    
    @Test
    public void testConvertTimeZone_UTCtoAmericaNewYork() {
        // 创建一个固定的时间点进行测试
        Date date = new Date(1234567890000L); // 2009-02-14 07:31:30 UTC
        
        // 使用DateUtil.convertTimeZone方法
        Date convertedDate = DateUtil.convertTimeZone(date, "UTC", "America/New_York");
        
        // 使用ZonedDateTime API实现相同功能
        ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.of("UTC"));
        ZonedDateTime nyDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("America/New_York"));
        Date expectedDate = Date.from(nyDateTime.toInstant());
        
        // 验证结果一致
        assertEquals(expectedDate, convertedDate);
        assertEquals(expectedDate.getTime(), convertedDate.getTime());
    }
    
    @Test
    public void testConvertTimeZone_EuropeLondonToAsiaTokyo() {
        // 创建一个固定的时间点进行测试
        Date date = new Date(1234567890000L); // 2009-02-14 07:31:30 UTC
        
        // 使用DateUtil.convertTimeZone方法
        Date convertedDate = DateUtil.convertTimeZone(date, "Europe/London", "Asia/Tokyo");
        
        // 使用ZonedDateTime API实现相同功能
        ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.of("Europe/London"));
        ZonedDateTime tokyoDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("Asia/Tokyo"));
        Date expectedDate = Date.from(tokyoDateTime.toInstant());
        
        // 验证结果一致
        assertEquals(expectedDate, convertedDate);
        assertEquals(expectedDate.getTime(), convertedDate.getTime());
    }
    
    @Test
    public void testConvertTimeZone_AmericaLosAngelesToAustraliaSydney() {
        // 创建一个固定的时间点进行测试
        Date date = new Date(1234567890000L); // 2009-02-14 07:31:30 UTC
        
        // 使用DateUtil.convertTimeZone方法
        Date convertedDate = DateUtil.convertTimeZone(date, "America/Los_Angeles", "Australia/Sydney");
        
        // 使用ZonedDateTime API实现相同功能
        ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.of("America/Los_Angeles"));
        ZonedDateTime sydneyDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("Australia/Sydney"));
        Date expectedDate = Date.from(sydneyDateTime.toInstant());
        
        // 验证结果一致
        assertEquals(expectedDate, convertedDate);
        assertEquals(expectedDate.getTime(), convertedDate.getTime());
    }
    
    @Test
    public void testConvertTimeZone_SameTimeZone() {
        // 创建一个固定的时间点进行测试
        Date date = new Date(1234567890000L); // 2009-02-14 07:31:30 UTC
        
        // 使用DateUtil.convertTimeZone方法，源时区和目标时区相同
        Date convertedDate = DateUtil.convertTimeZone(date, "Asia/Shanghai", "Asia/Shanghai");
        
        // 使用ZonedDateTime API实现相同功能
        ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.of("Asia/Shanghai"));
        ZonedDateTime sameZoneDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
        Date expectedDate = Date.from(sameZoneDateTime.toInstant());
        
        // 验证结果一致
        assertEquals(expectedDate, convertedDate);
        assertEquals(expectedDate.getTime(), convertedDate.getTime());
        assertEquals(date.getTime(), convertedDate.getTime());
    }
    
    @Test
    public void testConvertTimeZone_CurrentTime() {
        // 使用当前时间进行测试
        Date date = new Date();
        
        // 使用DateUtil.convertTimeZone方法
        Date convertedDate = DateUtil.convertTimeZone(date, "Asia/Shanghai", "UTC");
        
        // 使用ZonedDateTime API实现相同功能
        ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.of("Asia/Shanghai"));
        ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));
        Date expectedDate = Date.from(utcDateTime.toInstant());
        
        // 验证结果一致
        assertEquals(expectedDate, convertedDate);
        assertEquals(expectedDate.getTime(), convertedDate.getTime());
    }
    
    @Test
    public void testConvertTimeZone_WithDaylightSavingTime() {
        // 创建一个处于夏令时期间的时间点进行测试 (2009-07-14)
        Date date = new Date(1247556690000L); // 2009-07-14 07:31:30 UTC
        
        // 使用DateUtil.convertTimeZone方法
        Date convertedDate = DateUtil.convertTimeZone(date, "America/New_York", "UTC");
        
        // 使用ZonedDateTime API实现相同功能
        ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.of("America/New_York"));
        ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));
        Date expectedDate = Date.from(utcDateTime.toInstant());
        
        // 验证结果一致
        assertEquals(expectedDate, convertedDate);
        assertEquals(expectedDate.getTime(), convertedDate.getTime());
    }
    
    @Test
    public void testConvertTimeZone_StandardTime() {
        // 创建一个处于标准时间的时间点进行测试 (2009-01-14)
        Date date = new Date(1231918290000L); // 2009-01-14 07:31:30 UTC
        
        // 使用DateUtil.convertTimeZone方法
        Date convertedDate = DateUtil.convertTimeZone(date, "America/New_York", "UTC");
        
        // 使用ZonedDateTime API实现相同功能
        ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.of("America/New_York"));
        ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));
        Date expectedDate = Date.from(utcDateTime.toInstant());
        
        // 验证结果一致
        assertEquals(expectedDate, convertedDate);
        assertEquals(expectedDate.getTime(), convertedDate.getTime());
    }
}