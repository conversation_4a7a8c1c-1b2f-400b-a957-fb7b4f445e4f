package com.linzi.pitpat.core.util.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.Date;

@Data
@NoArgsConstructor
public class West8StartEndDto {

    private ZonedDateTime startTime;
    private ZonedDateTime endTime;

    public West8StartEndDto(ZonedDateTime startTime, ZonedDateTime endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
    }
}
