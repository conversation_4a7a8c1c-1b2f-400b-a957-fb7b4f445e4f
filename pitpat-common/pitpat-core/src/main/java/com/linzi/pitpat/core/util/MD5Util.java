package com.linzi.pitpat.core.util;

import com.google.common.hash.Hashing;

import java.nio.charset.StandardCharsets;

/**
 * 后面考虑命名为DigestUtils
 */
public class MD5Util {

    public static String sha256(String raw) {
        return Hashing.sha256().hashString(raw, StandardCharsets.UTF_8).toString();
    }
    public static String sha1(String raw) {
        return Hashing.sha1().hashString(raw, StandardCharsets.UTF_8).toString();
    }

    /**
     * MD5 在速度和安全性上没有优势，
     *
     * 如果为了安全 建议使用 sha256, 即使sha256 在速度上也比 MD5快
     * 如果为了 速度 建议使用 goodFastHash
     * @param raw
     * @return
     */
    @Deprecated
    public static String md5(String raw) {
        return Hashing.md5().hashString(raw, StandardCharsets.UTF_8).toString();
    }
}
