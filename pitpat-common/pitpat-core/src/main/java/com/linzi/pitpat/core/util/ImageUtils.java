package com.linzi.pitpat.core.util;

import com.linzi.pitpat.core.constants.Constants;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.Arrays;

/**
 * 图片处理工具类
 *
 * <AUTHOR>
 */
public class ImageUtils
{
    private static final Logger log = LoggerFactory.getLogger(ImageUtils.class);

    public static InputStream getFile(String imagePath,String localPath)
    {
        try
        {
            byte[] result = readFile(imagePath, localPath);
            result = Arrays.copyOf(result, result.length);
            return new ByteArrayInputStream(result);
        }
        catch (Exception e)
        {
            log.error("获取图片异常 {}", e);
        }
        return null;
    }


    /**
     * 读取文件为字节数据
     *
     * @param url 地址
     * @param localPath 本机地址
     * @return 字节数据
     */
    public static byte[] readFile(String url,String localPath)
    {
        InputStream in = null;
        ByteArrayOutputStream baos = null;
        try
        {
            if (url.startsWith("http")) {
                // 网络地址
                URL urlObj = new URL(url);
                URLConnection urlConnection = urlObj.openConnection();
                urlConnection.setConnectTimeout(30 * 1000);
                urlConnection.setReadTimeout(60 * 1000);
                urlConnection.setDoInput(true);
                in = urlConnection.getInputStream();
            } else {
                String downloadPath = localPath + subAfter(url, Constants.RESOURCE_PREFIX,false);
                in = new FileInputStream(downloadPath);
            }
            return IOUtils.toByteArray(in);
        }
        catch (Exception e)
        {
            log.error("获取文件路径异常 {}", e);
            return null;
        }
        finally
        {
            IOUtils.closeQuietly(in);
            IOUtils.closeQuietly(baos);
        }
    }

    private static String subAfter(String str, String separator, boolean lastIndex) {
        int pos = lastIndex ? str.lastIndexOf(separator) : str.indexOf(separator);
        if (pos >= 0) {
            return str.substring(pos + separator.length());
        }
        return str;
    }

    /**
     * 网络图片读取
     * @param url
     * @return
     */
    public static BufferedImage getBufferedImage(String url,String localPath) {
        InputStream is = getFile(url,localPath);
        BufferedImage read = null;
        try {
            read = ImageIO.read(is);
        } catch (Exception e) {
            log.error("图片加载异常 {}", e);
            return null;
        }
        return read;
    }

}
