package com.linzi.pitpat.core.util;

import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.KeyGenerator;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2018/12/29
 * AES对称加密和解密工具类
 */
@Component
@Slf4j
public class AesUtil {
    private static final String IV_STRING = "16-Bytes--String";
    private static final String charset = "UTF-8";

    /**
     * 加密
     * 1.构造密钥生成器
     * 2.根据ecnodeRules规则初始化密钥生成器
     * 3.产生密钥
     * 4.创建和初始化密码器
     * 5.内容加密
     * 6.返回字符串
     */
    public static String AESEncode(String encodeRules, String content) {
        try {
            /*1.构造密钥生成器，指定为AES算法,不区分大小写*/
            KeyGenerator keygen = KeyGenerator.getInstance("AES");
            /*2.根据ecnodeRules规则初始化密钥生成器*/
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(encodeRules.getBytes());
            /*生成一个128位的随机源,根据传入的字节数组*/
            keygen.init(128, secureRandom);
            /*3.产生原始对称密钥*/
            SecretKey original_key = keygen.generateKey();
            /*4.获得原始对称密钥的字节数组*/
            byte[] raw = original_key.getEncoded();
            /*5.根据字节数组生成AES密钥*/
            SecretKey key = new SecretKeySpec(raw, "AES");
            /*6.根据指定算法AES自成密码器*/
            Cipher cipher = Cipher.getInstance("AES");
            /*7.初始化密码器，第一个参数为加密(Encrypt_mode)或者解密解密(Decrypt_mode)操作，第二个参数为使用的KEY*/
            cipher.init(Cipher.ENCRYPT_MODE, key);
            /*8.获取加密内容的字节数组(这里要设置为utf-8)不然内容中如果有中文和英文混合中文就会解密为乱码*/
            byte[] byte_encode = content.getBytes(StandardCharsets.UTF_8);
            /*9.根据密码器的初始化方式--加密：将数据加密*/
            byte[] byte_AES = cipher.doFinal(byte_encode);
            /*10.将加密后的数据转换为字符串*/
            /*这里用Base64Encoder中会找不到包,解决办法：在项目的Build path中先移除JRE System Library，再添加库JRE System Library，重新编译后就一切正常了。*/
            /*11.将字符串返回*/
            return new String(Base64.getEncoder().encode(byte_AES));
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | InvalidKeyException | IllegalBlockSizeException |
                 BadPaddingException e) {
            e.printStackTrace();
        }

        /*如果有错就返加null*/
        return null;
    }

    /**
     * 解密
     * 解密过程：
     * 1.同加密1-4步
     * 2.将加密后的字符串反纺成byte[]数组
     * 3.将加密内容解密
     */
    public static String AESDncode(String encodeRules, String content) {
        try {
            /*1.构造密钥生成器，指定为AES算法,不区分大小写*/
            KeyGenerator keygen = KeyGenerator.getInstance("AES");
            /*2.根据ecnodeRules规则初始化密钥生成器:生成一个128位的随机源,根据传入的字节数组*/
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(encodeRules.getBytes());
            keygen.init(128, secureRandom);
            /*3.产生原始对称密钥*/
            SecretKey original_key = keygen.generateKey();
            /*4.获得原始对称密钥的字节数组*/
            byte[] raw = original_key.getEncoded();
            /*5.根据字节数组生成AES密钥*/
            SecretKey key = new SecretKeySpec(raw, "AES");
            /*6.根据指定算法AES自成密码器*/
            Cipher cipher = Cipher.getInstance("AES");
            /*7.初始化密码器，第一个参数为加密(Encrypt_mode)或者解密(Decrypt_mode)操作，第二个参数为使用的KEY*/
            cipher.init(Cipher.DECRYPT_MODE, key);
            /*8.将加密并编码后的内容解码成字节数组*/
            byte[] byte_content = Base64.getDecoder().decode(content);
            /*解密*/
            byte[] byte_decode = cipher.doFinal(byte_content);
            return new String(byte_decode, StandardCharsets.UTF_8);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | InvalidKeyException | IllegalBlockSizeException |
                 BadPaddingException e) {
            log.error("Error when decode aes string: {}", e.getMessage(), e);
        }

        /*如果有错就返加null*/
        return null;
    }

    /**
     * 加密-客户端使用
     *
     * @param content
     * @param key
     * @return
     */
    public static String aesEncryptString(String content, String key) {
        try {
            byte[] contentBytes = content.getBytes(charset);
            byte[] keyBytes = key.getBytes(charset);
            byte[] encryptedBytes = aesEncryptBytes(contentBytes, keyBytes);
            Base64.Encoder encoder = Base64.getEncoder();
            return encoder.encodeToString(encryptedBytes);
        } catch (InvalidKeyException | NoSuchAlgorithmException | NoSuchPaddingException |
                 InvalidAlgorithmParameterException | IllegalBlockSizeException | BadPaddingException |
                 UnsupportedEncodingException e) {
            log.error("Error when encrypt aes string: {}", e.getMessage(), e);
        }
        return "";
    }

    /**
     * 解密-客户端使用
     *
     * @param content
     * @param key
     * @return
     */
    public static String aesDecryptString(String content, String key) {
        if (content == null || content.length() == 0) {
            return "";
        }
        try {
            Base64.Decoder decoder = Base64.getDecoder();
            byte[] encryptedBytes = decoder.decode(content);
            byte[] keyBytes = key.getBytes(charset);
            byte[] decryptedBytes = aesDecryptBytes(encryptedBytes, keyBytes);
            return new String(decryptedBytes, charset);
        } catch (Exception e) {
            log.error("aesDecryptString异常, content={}, key={} ",content, key, e);
        }
        return content;
    }

    public static String aesDecrypt(String content, String key) {
        try {
            Base64.Decoder decoder = Base64.getDecoder();
            byte[] encryptedBytes = decoder.decode(content);
            byte[] keyBytes = key.getBytes(charset);
            byte[] decryptedBytes = aesDecryptBytes(encryptedBytes, keyBytes);
            return new String(decryptedBytes, charset);
        } catch (InvalidKeyException | NoSuchAlgorithmException | NoSuchPaddingException |
                 InvalidAlgorithmParameterException | IllegalBlockSizeException | BadPaddingException |
                 UnsupportedEncodingException e) {
            log.error("aesDecryptString异常 ",e );
            throw new BizException("非法请求", CommonError.PARAM_ERROR.getCode());
        }
    }

    public static byte[] aesEncryptBytes(byte[] contentBytes, byte[] keyBytes) throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, InvalidAlgorithmParameterException, IllegalBlockSizeException, BadPaddingException, UnsupportedEncodingException {
        return cipherOperation(contentBytes, keyBytes, Cipher.ENCRYPT_MODE);
    }

    public static byte[] aesDecryptBytes(byte[] contentBytes, byte[] keyBytes) throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, InvalidAlgorithmParameterException, IllegalBlockSizeException, BadPaddingException, UnsupportedEncodingException {
        return cipherOperation(contentBytes, keyBytes, Cipher.DECRYPT_MODE);
    }

    private static byte[] cipherOperation(byte[] contentBytes, byte[] keyBytes, int mode) throws UnsupportedEncodingException, NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, InvalidAlgorithmParameterException, IllegalBlockSizeException, BadPaddingException {
        SecretKeySpec secretKey = new SecretKeySpec(keyBytes, "AES");

        byte[] initParam = IV_STRING.getBytes(charset);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(initParam);

        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(mode, secretKey, ivParameterSpec);

        return cipher.doFinal(contentBytes);
    }


    public static void main(String[] args) throws Exception {
//        Scanner scanner = new Scanner(System.in);
//
//        /*加密*/
//        log.info("使用AES对称加密，请输入加密的规则");
//        String encodeRules=scanner.next();
//        log.info("请输入要加密的内容:");
//        String content = scanner.next();
//        log.info("根据输入的规则"+encodeRules+"加密后的密文是:"+AesUtil.AESEncode(encodeRules, content));
//
//        /*解密*/
//        log.info("使用AES对称解密，请输入加密的规则：(须与加密相同)");
//        encodeRules=scanner.next();
//        log.info("请输入要解密的内容（密文）:");
//        content = scanner.next();
//        log.info("根据输入的规则"+encodeRules+"解密后的明文是:"+AesUtil.AESDncode(encodeRules, content));
//        log.info(AesUtil.AESEncode("1234567891234567", "Aa123456"));
//        log.info(AesUtil.AESDncode("1234567891234567", "BhmzKKsGnbVzKERPrD22Gw=="));
//        log.info(aesEncryptString("{}","txIoVB16rgF9lCIn"));
        //System.out.println(aesEncryptString("{\"userId\":\"1\"}","txIoVB16rgF9lCIn"));
        //System.out.println(aesDecryptString("O+9tH2EtTsVfpaAlwzb1cA==","1234567891234567"));
/* */
        String key = "1234567891234567";

        String value = aesEncryptString("1111qqqq",key);
        System.out.println(value);
        String a = AESDncode("pitpat", "KZKRLptNwmQf/PjX+//arw==");
        String b = AESDncode("pitpat", "3SzoFIABPZl95RiXLnGZu1ymONUnqKvkeZQ/IxM3NLs=");
        System.out.println(a);
        System.out.println(b);
        String s = AESEncode("pitpat", "drcllkylgbxotilm");
        System.out.println(s);

    }

}
