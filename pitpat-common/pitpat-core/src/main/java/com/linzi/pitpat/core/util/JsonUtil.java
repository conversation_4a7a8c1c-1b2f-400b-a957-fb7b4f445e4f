package com.linzi.pitpat.core.util;

import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import com.linzi.pitpat.core.datetype.ZonedDateTimeDeserializer;
import com.linzi.pitpat.core.datetype.ZonedDateTimeSerializer;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * https://blog.csdn.net/weixin_41998947/article/details/137520719
 */
public class JsonUtil {

    private static final Logger log = LoggerFactory.getLogger(JsonUtil.class);
    /**
     * -- GETTER --
     *  返回原生 ObjectMapper 对象
     *
     * @return ObjectMapper
     */
    // 注册自定义的 ZonedDateTime 序列化器
    @Getter
    private static final ObjectMapper objectMapper = JsonMapper.builder()
            .addModule(new ParameterNamesModule())
            .addModule(new Jdk8Module())
            .addModule(new JavaTimeModule()
                    .addSerializer(ZonedDateTime.class, new ZonedDateTimeSerializer())
                    .addDeserializer(ZonedDateTime.class, new ZonedDateTimeDeserializer())
            )
            .enable(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS)

            .build();

    static {
        //序列化时是否输出为 null 的值
        objectMapper
                .setSerializationInclusion(JsonInclude.Include.NON_NULL)
                .setVisibility(PropertyAccessor.FIELD, Visibility.ANY)
                //反序列化时，未知属性是否报错
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false)
        ;
    }

    /**
     * 仅适用Map, 无法传递泛型参数，因为运行时会被擦除。 如果T是一个参数化的类型，如List<String>，那么这种方法就无法正确地传递参数化的类型信息，此时请使用 {@link readValue(Object content, TypeReference<T> valueTypeRef)} 并提供一个明确的TypeReference实例。
     *
     * @param content json String
     * @param <K>     Key 类型信息
     * @param <V>     Value 类型信息
     * @return Map<K, V>
     */
    public static <K, V> Map<K, V> readValue(String content) {
        return readValue(content, new TypeReference<>() {
        });
    }

    /**
     * 仅适用Map, 无法传递泛型参数，因为运行时会被擦除。 如果T是一个参数化的类型，如List<String>，那么这种方法就无法正确地传递参数化的类型信息，此时请使用 {@link readValue(Object content, TypeReference<T> valueTypeRef)} 并提供一个明确的TypeReference实例。
     *
     * @param content json String
     * @param <K>     Key 类型信息
     * @param <V>     Value 类型信息
     * @return Map<K, V>
     */
    @Deprecated
    public static <K, V> Map<K, V> readValue(Object content) {
        return readValue(writeString(content));
    }


    /**
     * 将 json 转为 entity 对象
     *
     * @param content
     * @param valueType
     * @param <T>
     * @return
     */
    public static <T> T readValue(String content, Class<T> valueType) {
        try {
            if (!StringUtils.hasText(content)) {
                return null;
            }
            return objectMapper.readValue(content, valueType);
        } catch (JsonProcessingException e) {
            log.error("error read json date: {}", content);
            throw new IllegalArgumentException(e);
        }
    }

    /**
     * 将 json 转为 entity 对象
     *
     * @param content
     * @param valueType
     * @param <T>
     * @return
     */
    public static <T> T readValue(Object content, Class<T> valueType) {
        return readValue(writeString(content), valueType);
    }

    /**
     * 将 json 转换为 entity List 对象，保留类型
     *
     * @param content
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> List<T> readList(String content, Class<T> clazz) {
        if (!StringUtils.hasText(content)) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(content, objectMapper.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (IOException e) {
            log.error("error read json data: {}", content);
            throw new IllegalArgumentException(e);
        }
    }


    /**
     * 将 json 转换为 entity List 对象，保留类型
     *
     * @param content
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> List<T> readList(Object content, Class<T> clazz) {
        return readList(writeString(content), clazz);
    }


    /**
     * 将java 对象转换为 json
     *
     * @param content
     * @return
     */
    public static String writeString(Object content) {
        try {
            if (Objects.isNull(content)) {
                return null;
            }
            if (content instanceof String str) {
                return str;
            }

            return objectMapper.writeValueAsString(content);
        } catch (JsonProcessingException e) {
            log.error("error write json date: {}", content);
            throw new IllegalArgumentException(e);
        }
    }

    /**
     * 将java 对象转换为 json,保留格式
     *
     * @param content
     * @return
     */
    public static String writePrettyString(Object content) {
        try {
            if (Objects.isNull(content)) {
                return null;
            }
            if (content instanceof String str) {
                return str;
            }

            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(content);
        } catch (JsonProcessingException e) {
            log.error("error write json date: {}", content);
            throw new IllegalArgumentException(e);
        }
    }

    /**
     * 是否强制使用jackson 序列化，即使 content 是 string
     *
     * @param content
     * @param checkString
     * @return
     */
    public static String writeString(Object content, boolean checkString) {
        try {
            if (Objects.isNull(content)) {
                return null;
            }
            if (checkString) {
                if (content instanceof String str) {
                    return str;
                }
            }

            return objectMapper.writeValueAsString(content);
        } catch (JsonProcessingException e) {
            log.error("error write json date: {}", content);
            throw new IllegalArgumentException(e);
        }
    }

    public static byte[] writeBytes(Object content) {
        try {
            if (Objects.isNull(content)) {
                return null;
            }
            return objectMapper.writeValueAsBytes(content);
        } catch (JsonProcessingException e) {
            log.error("error write json date: {}", content);
            throw new IllegalArgumentException(e);
        }
    }

    /**
     * 原生Jackson api,参数一致，捕获受检异常
     *
     * @param content
     * @param valueTypeRef
     * @param <T>
     * @return
     */
    public static <T> T readValue(String content, TypeReference<T> valueTypeRef) {
        try {
            if (!StringUtils.hasText(content)) {
                return null;
            }
            return objectMapper.readValue(content, valueTypeRef);
        } catch (JsonProcessingException e) {
            log.error("error read json date: {}", content);
            throw new IllegalArgumentException(e);
        }
    }

    /**
     * 原生Jackson api,参数一致，捕获受检异常,Object 内部会调用 writeString(content)
     *
     * @param content
     * @param valueTypeRef
     * @param <T>
     * @return
     */
    public static <T> T readValue(Object content, TypeReference<T> valueTypeRef) {
        return readValue(writeString(content), valueTypeRef);
    }

}

