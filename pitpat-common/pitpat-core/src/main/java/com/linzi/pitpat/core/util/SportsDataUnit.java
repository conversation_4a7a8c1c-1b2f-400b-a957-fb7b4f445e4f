package com.linzi.pitpat.core.util;


import com.linzi.pitpat.core.constants.CommonConstants;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * @description: 里程单位换算
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
public class SportsDataUnit {
    private static BigDecimal KPH_TO_MPH_UNIT = new BigDecimal("1.600");

    public static BigDecimal kphToMph(BigDecimal mileage) {
        return BigDecimalUtil.divHalfDown(mileage, KPH_TO_MPH_UNIT, 2);
    }

    public static Integer kphToMphPace(Integer measureUnit, Integer averagePace) {
        if (measureUnit == 0) {
            return averagePace;
        }
        BigDecimal pace = BigDecimalUtil.multiply(new BigDecimal(averagePace), KPH_TO_MPH_UNIT);
        return pace.intValue();
    }

    /**
     * 获取用户计量单位的里程
     *
     * @param measureUnit
     * @param mileage     计量单位,0：公里/小时 ，1：英里/小时
     * @return
     */
    public static BigDecimal getRunMileage(Integer measureUnit, BigDecimal mileage) {
        if (measureUnit == 0) {
            return mileage;
        }
        return kphToMph(mileage);
    }

    /**
     * 速度转配速
     *
     * @param velocity
     * @return
     */
    public static Integer velocityToPace(BigDecimal velocity) {
        if (velocity.compareTo(BigDecimal.ZERO) <= 0) {
            return 0;
        }
        return BigDecimalUtil.divHalfDown(new BigDecimal(3600), velocity, 0).intValue();
    }

    /**
     *
     * @param weight   千克
     * @param distance km
     * @return
     */
    public static BigDecimal getCalories(BigDecimal weight, BigDecimal distance) {

        return new BigDecimal(weight.multiply(distance).multiply(new BigDecimal(1.036)).intValue());
    }

    /**
     * 速度转配速
     *
     * @param pace
     * @return
     */
    public static BigDecimal paceToVelocity(Integer pace) {
        if (Objects.isNull(pace) || pace == 0) {
            return BigDecimal.ZERO;
        }

        return BigDecimalUtil.divHalfDown(new BigDecimal(3600), new BigDecimal(pace), 2);
    }

    /**
     * 计算配速
     *
     * @param runTime
     * @param mileage
     * @return
     */
    public static Integer getPace(Integer runTime, BigDecimal mileage) {
        if (Objects.isNull(runTime)) {
            return 0;
        }
        if (mileage.compareTo(BigDecimal.ZERO) == 0) {
            return 0;
        }
        return BigDecimalUtil.divHalfDown(new BigDecimal(runTime), mileage, 0).intValue();
    }

    /**
     * 计算速度
     *
     * @param runTime s
     * @param mileage m
     * @return
     */
    public static BigDecimal getVelocity(Integer runTime, BigDecimal mileage) {
        if (Objects.isNull(runTime)) {
            return BigDecimal.ZERO;
        }
        if (runTime == 0 || mileage.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        return mileage.multiply(new BigDecimal(3600)).divide(new BigDecimal(1000).multiply(new BigDecimal(runTime)), 2, BigDecimal.ROUND_DOWN);
    }


    /**
     * 计算步频
     *
     * @param totalStepNum
     * @param runTime
     * @return
     */
    public static Integer getStepFrequency(Integer totalStepNum, Integer runTime) {
        if (Objects.isNull(totalStepNum) || Objects.isNull(runTime)) {
            return 0;
        }
        if (runTime == 0) {
            return 0;
        }
        return totalStepNum * 60 / runTime;
    }

    /**
     * 计算步幅
     *
     * @param totalStepNum
     * @param totalRunMileage m
     * @return cm
     */
    public static Integer getStride(Integer totalStepNum, BigDecimal totalRunMileage) {
        if (Objects.isNull(totalStepNum) || Objects.isNull(totalRunMileage)) {
            return 0;
        }
        if (totalStepNum == 0) {
            return 0;
        }
        Double stride = totalRunMileage.doubleValue() * 100 / totalStepNum;

        return stride.intValue();
    }

    public static int getUnitConversionValue(int measureUnit) {
        if (measureUnit == 0) {
            return 1000;
        }

        return BigDecimalUtil.multiply(new BigDecimal(1000), KPH_TO_MPH_UNIT).intValue();
    }

    /**
     * 单位转换
     *
     * @param mileage     单位米
     * @param measureUnit
     */
    public static BigDecimal conversionUnit(BigDecimal mileage, int measureUnit) {
        return conversionUnit(mileage, measureUnit, 2);
    }

    /**
     * 单位转换
     *
     * @param mileage     单位米
     * @param measureUnit
     */
    public static BigDecimal conversionUnit(BigDecimal mileage, int measureUnit, int digit) {
        if (measureUnit == 0) {
            return BigDecimalUtil.divHalfDown(mileage, new BigDecimal(1000), digit);
        }
        return BigDecimalUtil.divHalfDown(mileage, new BigDecimal(1600), digit);
    }


    /**
     * 单位转换
     *
     * @param velocity 单位公里/小时
     * @return m/min
     */
    public static BigDecimal conversionVelocityToMinute(BigDecimal velocity) {
        return BigDecimalUtil.multiply(velocity, new BigDecimal(1000)).divide(new BigDecimal(60), 1, BigDecimal.ROUND_HALF_UP);
    }


    /**
     * 单位转换
     *
     * @param velocity 单位公里/小时
     * @return m/s
     */
    public static BigDecimal conversionVelocityToSecond(BigDecimal velocity) {
        return BigDecimalUtil.multiply(velocity, new BigDecimal(1000)).divide(new BigDecimal(3600), 1, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 单位转换
     *
     * @param velocity 单位公里/小时 -> 米/小时
     * @return m/s
     */
    public static BigDecimal conversionVelocityToMetre(BigDecimal velocity, int measureUnit) {
        if (measureUnit == 0) {
            return BigDecimalUtil.multiply(velocity, new BigDecimal(1000));
        } else if (measureUnit == 1) {
            return BigDecimalUtil.multiply(velocity, new BigDecimal(1600));
        } else {
            return velocity;
        }
    }

    /**
     * 单位转换
     *
     * @param velocity 米/小时 -> 单位公里/小时
     * @return m/s
     */
    public static BigDecimal conversionVelocity(BigDecimal velocity, int measureUnit) {
        if (measureUnit == 0) {
            return BigDecimalUtil.divide(velocity, new BigDecimal(1000));
        } else if (measureUnit == 1) {
            return BigDecimalUtil.divide(velocity, new BigDecimal(1600));
        } else {
            return velocity;
        }
    }

    /**
     * 老版本计算能力值
     *
     * @param velocity 公里/小时
     * @return
     */
    @Deprecated
    public static BigDecimal getCapabilityValue(BigDecimal velocity) {
        //公式 跑力值=0.2*速度(m/min)+0.9*速度(m/min)*坡度(%)-3.5
        if (velocity.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal averVelocity = conversionVelocityToMinute(velocity);
        //坡度默认为0
        BigDecimal gradientPercentage = new BigDecimal(BigInteger.ZERO).divide(new BigDecimal(100), 1, BigDecimal.ROUND_HALF_UP);
        BigDecimal capabilityValue = BigDecimalUtil.multiply(new BigDecimal(0.2), averVelocity).add(BigDecimalUtil.multiply(new BigDecimal(0.9), averVelocity, gradientPercentage)).subtract(new BigDecimal(3.5)).setScale(1, BigDecimal.ROUND_HALF_UP);

        return capabilityValue.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : capabilityValue;
    }


    /**
     * 计算竞技值
     * 竞技值公式=0.44*速度(m/min)+0.2*时长(min)
     *
     * @param runMileage 运动里程（m）
     * @param runTime    运动时长（s）
     * @return
     */
    public static BigDecimal getCapabilityValue(BigDecimal runMileage, Integer runTime) {
        if (Optional.ofNullable(runMileage).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) <= 0
                || Optional.ofNullable(runTime).orElse(0) <= 0) {
            return BigDecimal.ZERO;
        }

        //时长（min）
        BigDecimal minTime = new BigDecimal(runTime).divide(new BigDecimal("60"), 10, BigDecimal.ROUND_HALF_UP);

        //速度(m/min)
        BigDecimal speed = runMileage.divide(minTime, 10, BigDecimal.ROUND_HALF_UP);

        //第一个值(0.44*速度)
        BigDecimal val1 = new BigDecimal("0.44").multiply(speed);

        //第二个值(0.2*时长)
        BigDecimal val2 = new BigDecimal("0.2").multiply(minTime);

        //竞技值 = 第一个值 + 第二个值
        BigDecimal capabilityValue = val1.add(val2).setScale(1, BigDecimal.ROUND_HALF_UP);

        //计算竞技值(0-100)
        capabilityValue = capabilityValue.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : capabilityValue;
        capabilityValue = capabilityValue.compareTo(new BigDecimal("100")) > 0 ? new BigDecimal("100") : capabilityValue;
        return capabilityValue;
    }


    /**
     * 计算脂肪消耗
     *
     * @param kilocalorie
     * @return
     */
    public static Integer getFatConsumption(BigDecimal kilocalorie) {
        if (Objects.isNull(kilocalorie)) {
            return 0;
        }
        return BigDecimalUtil.multiply(kilocalorie, new BigDecimal(1000)).divide(new BigDecimal(7700), 2, BigDecimal.ROUND_HALF_UP).intValue();
    }

    /**
     * 计算目标卡路里
     *
     * @param distanceTarget
     * @param timeTarget
     * @param averagePace
     * @param weight
     * @return
     */
    public static Integer getCalorieTarget(BigDecimal distanceTarget, Integer timeTarget, Integer averagePace, Double weight) {
        if (distanceTarget.compareTo(BigDecimal.ZERO) <= 0 && timeTarget <= 0) {
            return 0;
        }

        if (Objects.isNull(weight)) {
            weight = 80d;
        }
        if (distanceTarget.compareTo(BigDecimal.ZERO) > 0) {
            // 体重 * 米/1000 * 1.036
            return BigDecimalUtil.multiply(new BigDecimal(weight), SportsDataUnit.conversionUnit(distanceTarget, 0), CommonConstants.CALORIE_CALCULATE_RATIO).intValue();
        } else {
            BigDecimal time = new BigDecimal(timeTarget).divide(new BigDecimal(60), 1, BigDecimal.ROUND_HALF_UP);
            BigDecimal pace = new BigDecimal(averagePace).divide(new BigDecimal(60), 1, BigDecimal.ROUND_HALF_UP);
            if (pace.compareTo(BigDecimal.ZERO) == 0) {
                return 0;
            }
            //计算预计里程 = (体重  *  跑步时间 * 平均速度 / 1000 ) * 1.036
            return BigDecimalUtil.multiply(new BigDecimal(weight), time, CommonConstants.CALORIE_CALCULATE_RATIO).divide(pace, 1, BigDecimal.ROUND_HALF_UP).intValue();
        }
    }

    /**
     * 获得最大心率
     *
     * @param birthday 生日
     * @return
     */
    public static int getUserMaxHeart(ZonedDateTime birthday) {
        if (Objects.isNull(birthday)) {
            return 190;
        }

        int year = ZonedDateTime.now().getYear();
        int birthdayYear = birthday.withZoneSameInstant(ZoneId.systemDefault()).getYear();
        int age = year - birthdayYear;

        return 220 - age;
    }

    /**
     * 计算距离
     *
     * @param seconds
     * @param velocity
     * @return
     */
    public static BigDecimal getComputeMileage(int seconds, BigDecimal velocity) {
        //速度转换,m/s
        BigDecimal velocityToSecond = conversionVelocityToSecond(velocity);

        return BigDecimalUtil.multiply(new BigDecimal(seconds), velocityToSecond);
    }

    /**
     * 计算爬升距离
     *
     * @param averFallingGradient 坡度
     * @param airlineDistance
     * @return
     */
    public static BigDecimal getClimbingMileage(Double averFallingGradient, BigDecimal airlineDistance) {
        if (averFallingGradient.compareTo(0.0) <= 0 || airlineDistance.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        //c^2 = a^2 + b^2
        //计算坡度百分比，坡度=a/b 不用这个
        //计算坡度百分比，坡度=a/c
//        double gradientPercentage = new BigDecimal(averFallingGradient).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).doubleValue();
//        double a2 = Math.pow(gradientPercentage, 2.0) + 1;
//        double c2 = Math.pow(airlineDistance.doubleValue(), 2.0);
//        double sqrt = Math.sqrt(BigDecimalUtil.divide(c2, a2).doubleValue());
//        return new BigDecimal(sqrt).setScale(0,BigDecimal.ROUND_HALF_UP);
        return BigDecimalUtil.multiply(new BigDecimal(averFallingGradient), airlineDistance);
    }

    public static BigDecimal conversionVelocity(BigDecimal velocity, int measureUnit, int digit) {
        if (measureUnit == 0) {
            return BigDecimalUtil.divHalfUp(velocity, new BigDecimal(1000), digit);
        } else if (measureUnit == 1) {
            return BigDecimalUtil.divHalfUp(velocity, new BigDecimal(1600), digit);
        } else {
            return BigDecimalUtil.divHalfUp(velocity, new BigDecimal(60), digit);
        }
    }

    public static Integer getPace(BigDecimal runTime, BigDecimal mileage) {
        if (Objects.isNull(runTime)) {
            return 0;
        }
        if (mileage.compareTo(BigDecimal.ZERO) == 0) {
            return 0;
        }
        return BigDecimalUtil.divHalfDown(runTime, mileage, 0).intValue();
    }

    public static BigDecimal getVelocity(BigDecimal runTime, BigDecimal mileage) {
        if (Objects.isNull(runTime) || runTime.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        if (mileage.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        return mileage.multiply(new BigDecimal(3600)).divide(new BigDecimal(1000).multiply(runTime), 2, BigDecimal.ROUND_DOWN);
    }


    public static BigDecimal getTreadFrequency(BigDecimal runTime, Integer rotateNum) {
        if (Objects.isNull(runTime) || runTime.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        if (Objects.equals(rotateNum, 0)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(rotateNum).multiply(new BigDecimal(60)).divide(runTime, 2, RoundingMode.HALF_UP);
    }


    /**
     * 转换成小单位
     *
     * @param goal
     * @param milepostType
     * @return
     */
    public static Integer smallUnits(BigDecimal goal, Integer milepostType) {
        if (Objects.isNull(goal)) {
            return 0;
        }
        if (milepostType == 0) {
            return BigDecimalUtil.multiply(goal, new BigDecimal(1000)).intValue();
        } else if (milepostType == 1) {
            return BigDecimalUtil.multiply(goal, new BigDecimal(1600)).intValue();
        } else {
            return BigDecimalUtil.multiply(goal, new BigDecimal(60)).intValue();
        }
    }

    /**
     * 转换大单位
     *
     * @param goal
     * @param milepostType
     * @param digit
     * @return
     */
    public static BigDecimal bigUnits(Integer goal, Integer milepostType, int digit) {
        if (Objects.isNull(goal)) {
            return BigDecimal.ZERO;
        }
        if (milepostType == 0) {
            return BigDecimalUtil.divHalfUp(new BigDecimal(goal), new BigDecimal(1000), digit);
        } else if (milepostType == 1) {
            return BigDecimalUtil.divHalfUp(new BigDecimal(goal), new BigDecimal(1600), digit);
        } else {
            return BigDecimalUtil.divHalfUp(new BigDecimal(goal), new BigDecimal(60), digit);
        }
    }

    /**
     * 速度 m/s
     *
     * @param runTime
     * @param runMileage
     * @return
     */
    public static BigDecimal getVelocityMS(Integer runTime, BigDecimal runMileage) {
        if (Objects.isNull(runTime)) {
            return BigDecimal.ZERO;
        }
        if (runTime == 0 || runMileage.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        return runMileage.divide(new BigDecimal(runTime), 2, BigDecimal.ROUND_DOWN);
    }
}
