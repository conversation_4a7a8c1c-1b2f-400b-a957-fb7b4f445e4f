package com.linzi.pitpat.core.datetype;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public class ZonedDateTimeSerializer extends JsonSerializer<ZonedDateTime> {

    private static final long serialVersionUID = 1L;
    private final DateTimeFormatter formatter ;
    private final DateTimeFormatter defaultFormatter =  DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final Boolean enableTimestamp;

    public ZonedDateTimeSerializer() {
      this.formatter = defaultFormatter;
      this.enableTimestamp =true;
    }

    public ZonedDateTimeSerializer(DateTimeFormatter formatter) {
        this(formatter, false);
    }

    public ZonedDateTimeSerializer(DateTimeFormatter formatter, Boolean enableTimestamp) {
        this.formatter = formatter;
        this.enableTimestamp = enableTimestamp;
    }

    @Override
    public void serialize(ZonedDateTime value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (enableTimestamp) {
            // 将时间转换为 Asia/Shanghai 时区
            //ZonedDateTime shanghaiTime = value.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
            //以下结果输出一致
            //log.info("zoned utcTime={}", value.toInstant().toEpochMilli());
            //log.info("zoned shanghaiTime={}", shanghaiTime.toInstant().toEpochMilli());
            // 序列化为格式化字符串
            gen.writeNumber(value.toInstant().toEpochMilli());
        } else {
            // 将时间转换为 Asia/Shanghai 时区
            ZonedDateTime shanghaiTime = value.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
            // 序列化为格式化字符串
            gen.writeString(shanghaiTime.format(formatter));
        }
    }
}

