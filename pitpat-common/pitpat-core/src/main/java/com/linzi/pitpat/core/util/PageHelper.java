package com.linzi.pitpat.core.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.CaseFormat;
import com.linzi.pitpat.lang.PageQuery;
import com.linzi.pitpat.lang.Query;
import org.springframework.util.CollectionUtils;

import java.util.stream.Collectors;

public class PageHelper {

    /**
     * 构造 Mybatis plus Paging 对象
     *
     * @param pageQuery 分页查询对象
     * @param <T>       PageQuery
     * @return Paging
     */
    public static <T> Page<T> ofPage(PageQuery pageQuery) {
        return Page.of(pageQuery.getPageNum(), pageQuery.getPageSize());
    }

    /**
     * 构造 Mybatis plus order by sql
     *
     * @param query 查询对象
     * @return String
     */
    public static String ofOrderSql(Query query) {
        if (CollectionUtils.isEmpty(query.getOrders())) {
            return "";
        }
        String orderBy = query.getOrders().stream().map(item -> snakeCase(item.getColumn()) + " " + item.getDirection()).collect(Collectors.joining(","));
        return "order by " + orderBy;
    }

    private static String snakeCase(String column) {
        String s = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, column);
        return "`"+s+"`";
    }

}
