package com.linzi.pitpat.core.util;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@Slf4j
public class ListUtils {

    /**
     * List按指定长度分割
     * @param list the list to return consecutive sublists of （需要分隔的list）
     * @param size the desired size of each sublist (the last may be smaller) （分隔的长度）
     * <AUTHOR>
     * @date 2019-07-07 21:37
     */
    public static <T> List<List<T>> partition(List<T> list, int size){
        return  Lists.partition(list, size); // 使用guava
    }

    /**
     * 返回随机
     * @param list
     * @param <T>
     * @return
     */
    public static <T> T random(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Random random = new Random();
        int num = random.nextInt(list.size());
        return list.get(num);
    }

    public static <T> List<T> getRandomElements(List<T> list, int k) {

        Collections.shuffle(list);
        return list.stream().limit(k).collect(Collectors.toList());

    }

    public static <T> List<T> mergeAndRemoveDuplicates(List<T>... lists) {
        Set<T> set = new HashSet<>();
        for (List<T> list : lists) {
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            set.addAll(list);
        }
        return new ArrayList<>(set);
    }
}
