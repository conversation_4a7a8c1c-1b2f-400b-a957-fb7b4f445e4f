package com.linzi.pitpat.core.util.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@Deprecated
/**
 * 使用 DateTimeStartEnd 替代
 */
public class DateStartEnd {

    private Date startTime;
    private Date endTime;

    public DateStartEnd(Date startTime, Date endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
    }
}
