package com.linzi.pitpat.core.web;

import com.linzi.pitpat.lang.Err;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CommonError implements Err {

    /**
     * 成功
     */
    SUCCESS(200, "success"),
    /**
     * 参数缺失
     */
    PARAM_LACK(301, "param not exist"),
    /**
     * 参数错误
     */
    PARAM_ERROR(302, "param error"),

    /**
     * 参数错误
     */
    NO_PERMISSION_ERROR(303, "no permission error"),
    /**
     * 业务异常, 通用
     */
    BUSINESS_ERROR(998, "Please try again later"),

    /**
     * 设备类型错误
     */
    DEV_TYPE_ERROR(888, "dev type error"),

    /**
     * 未登录
     */
    NEED_LOGIN(1001, "need login"),
    /**
     * 验签失败
     */
    SIGN_ERROR(1002, "sign error"),
    /**
     * token缺失
     */
    TOKEN_LACK(1003, "invalid token"),

    /**
     * 请求重复
     */
    DUPLICATE_REQUEST(1004, "duplicate request"),
    /**
     * token失效
     */
    INVALID_TOKEN(1008, "invalid token"),
    /**
     * 系统异常, 提示 系统繁忙，请稍后再试
     */
    SYSTEM_ERROR(999, "The system is busy, Please try again later"),


    ;

    /**
     * 返回码
     */
    private final Integer code;

    /**
     * 返回消息
     */
    private final String msg;
}
