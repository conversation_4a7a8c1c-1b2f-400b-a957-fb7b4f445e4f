package com.linzi.pitpat.core.util;

import com.linzi.pitpat.core.util.dto.DateStartEnd;
import com.linzi.pitpat.core.util.dto.WeekDayDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.sql.Time;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class DateUtil extends org.apache.commons.lang3.time.DateUtils {

    public static String YYYY_MM = "yyyy-MM";

    public static String _MM = "MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    public static String YYYYMMDD = "yyyyMMdd";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static String MM_DD = "MM/dd";

    /**
     * 带时区格式，默认西7区，
     */
    public static String YYYY_MM_DD_HH_MM_SS_T = "yyyy-MM-dd HH:mm:ss-07:00";

    public static String MM = "MM月";

    public static String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";

    public static String YYYY_MM_DD_HH_MM_SS_CN = "yyyy年MM月dd日HH时mm分ss秒"; //2024年06月17日 11时08分57秒
    public static String YYYY_MM_DD_HH = "yyyy-MM-dd HH";
    public static String YYYY_MM_DD_HH_MM_UTC = "yyyy-MM-ddTHH:mm";

    public static String YYYY_MM_DD_HH_MM_SS_SSS = "yyyy-MM-dd HH:mm:ss.SSS";

    public static final String DEFAULT_PATTERN_WITH_HYPHEN_ = "yyyy_MM_dd";
    public static final String DATE_TIME_SHORT = "yyyy-MM-dd HH:mm:ss";

    public static String YYYY_MM_DD_DOT = "yyyy.MM.dd";
    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM",
            "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"};


    public static String HH = "HH";

    public static String HH_MM_SS = "HH:mm:ss";

    public static String HH_MM = "HH:mm";


    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static final String parseDateToStr(final String format, final Date date) {
        if (Objects.isNull(date)) {
            return "";
        }
        return new SimpleDateFormat(format).format(date);
    }

    public static String formatDate(final Date date, String format) {
        if (date == null) {
            return "";
        }
        return new SimpleDateFormat(format).format(date);
    }

    public static String parseFormatDate(Object str, String format) {
        if (str == null || StringUtils.isEmpty(str.toString())) {
            return "";
        }
        Date date = parseDate(str);
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }


    public static Date getBeforeMinute(Date date, int minute) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, -minute);
        Date beforeDate = calendar.getTime();
        return beforeDate;
    }

    /**
     * 获取时间角分符号值，单位分秒
     *
     * @param second
     * @return
     */
    public static String getDatePrime(Integer second) {
        if (Objects.isNull(second)) {
            return "0´0″";
        }
        Time time = getTime(second);
        int hour = time.getHours();
        int minuteRes = time.getMinutes();
        int secondRes = time.getSeconds();
        String hourStr = "";
        if (time.compareTo(new Time(1, 0, 0)) == 0) {
            minuteRes = second / 60;
        } else if (hour > 0) {
            hourStr = hour + "h";
            return hourStr + minuteRes + "´";
        }
        return hourStr + minuteRes + "´" + secondRes + "″";
    }

    public static Time getTime(Integer second) {
        if (Objects.isNull(second)) {
            return new Time(0, 0, 0);
        }
        LocalTime of = LocalTime.of(0, 0, 0);
        LocalTime localTime = of.plusSeconds(second);
        return new Time(localTime.getHour(), localTime.getMinute(), localTime.getSecond());
    }

    /**
     * Get start of date.
     *
     * @param date Date
     * @return Date Date
     */
    public static Date getStartOfDate(final Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * Get start of date.
     *
     * @param date     Date
     * @param timeZone
     * @return Date Date
     */
    public static Date getStartOfDate(Date date, TimeZone timeZone) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long offset = TimeZone.getDefault().getOffset(Calendar.ZONE_OFFSET) - timeZone.getOffset(Calendar.ZONE_OFFSET);
        calendar.add(Calendar.MILLISECOND, (int) offset);
        return calendar.getTime();
    }

    /**
     * 一天的结束时间，【注：只精确到毫秒】
     *
     * @param date
     * @return
     */
    public static Date getEndOfDate(final Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * Get start of date.
     *
     * @param date Date
     * @return Date Date
     */
    public static Date getEndOfDate(Date date, TimeZone timeZone) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        long offset = TimeZone.getDefault().getOffset(Calendar.ZONE_OFFSET) - timeZone.getOffset(Calendar.ZONE_OFFSET);
        calendar.add(Calendar.MILLISECOND, (int) offset);
        return calendar.getTime();
    }

    /**
     * 一天的结束时间，【注：只精确到毫秒】
     *
     * @param date
     * @return
     */
    public static Date getEndOfDateEndWithSecond(final Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }

    public static Date toDate(ZonedDateTime zonedDateTime) {
        if (zonedDateTime == null) {
            return null;
        }
        //ZoneId zoneId = ZoneId.systemDefault();
        //ZonedDateTime zdt = ZonedDateTime.atZone(zoneId);

        return Date.from(zonedDateTime.toInstant());
    }


    public static LocalDate toLocalDate(Date date) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();

        // atZone()方法返回在指定时区从此Instant生成的ZonedDateTime。
        LocalDate localDate = instant.atZone(zoneId).toLocalDate();
        return localDate;
    }

    public static ZonedDateTime toZonedDateTime(Date date) {
        return ZonedDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    public static final Date parseStr2Date(String value, final String format) {
        if (Objects.isNull(value)) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return sdf.parse(value);
        } catch (ParseException e) {

        }
        return null;
    }

    /**
     * 通过时区获取时间
     *
     * @param date
     * @param timeZone
     */
    public static String parseDateToStrByTimeZone(String format, Date date, TimeZone timeZone) {
        if (Objects.isNull(date)) {
            return "";
        }
        if (!StringUtils.hasText(format)) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        SimpleDateFormat df = new SimpleDateFormat(format);
        df.setTimeZone(timeZone);
        return df.format(date);
    }

    /**
     * 通过时区获取时间
     *
     * @param date
     * @param timeZone
     */
    public static String getDateByTimeZone(Date date, TimeZone timeZone) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        format.setTimeZone(timeZone);
        return format.format(date);
    }

    /**
     * 通过时区获取对应时区的时间
     * 实际时间戳不一致
     *
     * @param date
     * @param timeZone
     */
    public static Date getDate2ByTimeZone(Date date, TimeZone timeZone) {
        if (Objects.isNull(date)) {
            return null;
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        format.setTimeZone(timeZone);
        String dataStr = format.format(date);
        return parseDate(dataStr);
    }

    /**
     * 通过时区获取时间
     *
     * @param date
     * @param timeZone
     */
    public static String getDate3ByTimeZone(Date date, TimeZone timeZone) {
        if (Objects.isNull(date)) {
            return null;
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        format.setTimeZone(timeZone);
        String dataStr = format.format(date);
        return dataStr;
    }

    /**
     * 通过时区获取时间
     *
     * @param date
     * @param timeZone
     */
    public static String getDate3ByTimeZone(Date date, TimeZone timeZone, String formatStr) {
        if (Objects.isNull(date)) {
            return null;
        }
        SimpleDateFormat format = new SimpleDateFormat(formatStr);
        format.setTimeZone(timeZone);
        String dataStr = format.format(date);
        return dataStr;
    }

    // 2022-05-27T15:36:00Z
    public static Date getUtcDate(String date) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD_HH_MM);
            if (org.springframework.util.StringUtils.hasText(date)) {
                date = date.substring(0, 10) + " " + date.substring(11, 16);
                return sdf.parse(date);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }


    public static Date formateDate(String gmtCreate, String yyyyMmDdHhMmSs) {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(yyyyMmDdHhMmSs);
            return simpleDateFormat.parse(gmtCreate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }


    public static Date formateDate(Date gmtCreate, String yyyyMmDdHhMmSs) {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(yyyyMmDdHhMmSs);
            String b = simpleDateFormat.format(gmtCreate);
            return simpleDateFormat.parse(b);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public static String formateDateStr(Date gmtCreate, String yyyyMmDdHhMmSs) {
        try {
            if (gmtCreate == null || !StringUtils.hasText(yyyyMmDdHhMmSs)) {
                return null;
            }
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(yyyyMmDdHhMmSs);
            String b = simpleDateFormat.format(gmtCreate);
            return b;
        } catch (Exception e) {
            log.error("异常", e);
        }
        return null;
    }


    public static String formateDateStrForEn(Date gmtCreate, String yyyyMmDdHhMmSs) {
        try {
            if (gmtCreate == null || !StringUtils.hasText(yyyyMmDdHhMmSs)) {
                return null;
            }
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(yyyyMmDdHhMmSs, Locale.ENGLISH);
            String b = simpleDateFormat.format(gmtCreate);
            return b;
        } catch (Exception e) {
            log.error("异常", e);
        }
        return null;
    }


    /**
     * >
     */
    public static boolean gt(Date date, Date endTime) {
        if (date.getTime() > endTime.getTime()) {
            return true;
        }
        return false;
    }

    /**
     * >
     */
    public static boolean ge(Date date, Date endTime) {
        if (date.getTime() >= endTime.getTime()) {
            return true;
        }
        return false;
    }

    /**
     * >
     */
    public static boolean lt(Date date, Date endTime) {
        if (date.getTime() < endTime.getTime()) {
            return true;
        }
        return false;
    }

    public static boolean le(Date date, Date endTime) {
        if (date.getTime() <= endTime.getTime()) {
            log.info("le {},{}", date.getTime(), endTime.getTime());
            return true;
        }
        return false;
    }

    public static Date formatMinites(Date date) {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(YYYY_MM_DD_HH_MM);
            String a = simpleDateFormat.format(date);
            return simpleDateFormat.parse(a);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return null;
    }

    // ==
    public static boolean eq(Date date1, Date date2) {
        if (date1.getTime() == date2.getTime()) {
            return true;
        }
        return false;
    }


    /**
     * judge the srcDate is between startDate and endDate
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static int betweenSecond(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            return -1;
        }
        Long a = endDate.getTime() - startDate.getTime();
        return (int) (a / 1000);
    }

    /**
     * yyyyMMdd
     */
    public static final String DEFAULT_PATTERN = "yyyyMMdd";

    public static String dateStr(Date date, String f) {
        SimpleDateFormat format = new SimpleDateFormat(f);
        if (date != null) {
            String str = format.format(date);
            return str;
        }
        return "";
    }

    public static String dateStr7(Date date) {
        return dateStr(date, DEFAULT_PATTERN);
    }


    public static int daysBetween(Date date1, Date date2) {
        DateFormat sdf = new SimpleDateFormat(DEFAULT_PATTERN);
        Calendar cal = Calendar.getInstance();
        try {
            Date d1 = sdf.parse(dateStr7(date1));
            Date d2 = sdf.parse(dateStr7(date2));
            cal.setTime(d1);
            long time1 = cal.getTimeInMillis();
            cal.setTime(d2);
            long time2 = cal.getTimeInMillis();
            return Integer.parseInt(String.valueOf((time2 - time1) / 86400000L));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return 0;
    }


    public static int daysBetweenYYYYMMDD(Date date1, Date date2) {
        return daysBetween(DateUtil.formateDate(date1, DateUtil.YYYY_MM_DD), DateUtil.formateDate(date2, DateUtil.YYYY_MM_DD));
    }


    public static int betweenMinutes(Date startDate, Date endDate) {
        Long a = endDate.getTime() - startDate.getTime();
        return (int) (a / (1000 * 60));
    }


    public static int betweenHours(Date startDate, Date endDate) {
        Long a = endDate.getTime() - startDate.getTime();
        return (int) (a / (1000 * 60 * 60));
    }

    public static int betweenDay(Date startDate, Date endDate) {
        Long a = endDate.getTime() - startDate.getTime();
        return (int) (a / (1000 * 60 * 60 * 24));
    }

    // 将毫秒差转换为天数，并且向上取整
    public static int betweenDayUp(Date startDate, Date endDate) {
        Long a = endDate.getTime() - startDate.getTime();
        return (int) Math.ceil((double) a / (1000 * 60 * 60 * 24));
    }

    /**
     * Get start of date.
     *
     * @param date Date
     * @return Date Date
     */
    public static Date startOfDate(final Date date) {
        Calendar cal = GregorianCalendar.getInstance();
        cal.setTime(date);

        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);

        return new Date(cal.getTime().getTime());
    }


    /**
     * 一天的结束时间，【注：只精确到毫秒】
     *
     * @param date
     * @return
     */
    public static Date endOfDate(final Date date) {
        Calendar cal = GregorianCalendar.getInstance();
        cal.setTime(date);

        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 999);

        return new Date(cal.getTime().getTime());
    }

    public static Date convertZonedDateTime2Date(ZonedDateTime zonedDateTime) {
        if (zonedDateTime == null) {
            return null;
        }
        //ZoneId zoneId = ZoneId.systemDefault();
        //ZonedDateTime zonedDateTime = ZonedDateTime.atZone(zoneId);
        //Instant instant = zonedDateTime.toInstant();
        return Date.from(zonedDateTime.toInstant());

    }

    public static Date getDate(String date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
        try {
            return simpleDateFormat.parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }


    public static Date getSameDay(Date activityStartTime) {
        try {
            SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("HH:mm:ss");
            SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat simpleDateFormat3 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String temp = simpleDateFormat2.format(new Date()) + " " + simpleDateFormat1.format(activityStartTime);
            return simpleDateFormat3.parse(temp);
        } catch (ParseException e) {
            log.error("解析异常", e);
        }
        return null;
    }

    public static long getTime(Date date) {
        return date.getTime() - 28800000;
    }

    /**
     * between 包括边界
     *
     * @param date
     * @param start
     * @param end
     * @return
     */
    public static boolean isBetweenE(Date date, Date start, Date end) {
        if (Objects.isNull(date)) {
            return false;
        }
        if (Objects.isNull(start) || Objects.isNull(end)) {
            return false;
        }
        return date.compareTo(start) >= 0 && date.compareTo(end) <= 0;


    }

    public static Integer getCurrentHH() {
        SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("HH");
        String hh = simpleDateFormat1.format(new Date());
        if (hh.startsWith("0")) {
            hh = hh.substring(1, hh.length());
        }
        return MapUtil.getInteger(hh, 0);

    }


    public static boolean isSameDay(final Date date1, final Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        final Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        final Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        return isSameDay(cal1, cal2);
    }

    public static String subHours8(String hh) {
        String dateStr = DateUtil.formateDateStr(new Date(), "yyyy-MM-dd");
        String date = dateStr + " " + hh + ":00";
        Date dateReal = DateUtil.formateDate(date, DateUtil.YYYY_MM_DD_HH_MM_SS);
        Date temp = DateUtil.addHours(dateReal, -8);
        String dateStrXXX = DateUtil.formateDateStr(temp, "HH:mm");
        return dateStrXXX;
    }


    public static String addHours8(String hh) {
        String dateStr = DateUtil.formateDateStr(new Date(), "yyyy-MM-dd");
        String date = dateStr + " " + hh + ":00";
        Date dateReal = DateUtil.formateDate(date, DateUtil.YYYY_MM_DD_HH_MM_SS);
        Date temp = DateUtil.addHours(dateReal, 8);
        String dateStrXXX = DateUtil.formateDateStr(temp, "HH:mm");
        return dateStrXXX;
    }

    public static String toHMS(Integer runTimeMillisecond) {
        if (Objects.isNull(runTimeMillisecond) || runTimeMillisecond == 0) {
            return "";
        }
        long days = runTimeMillisecond / (1000 * 60 * 60 * 24);
        long hours = (runTimeMillisecond % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60);
        long minutes = (runTimeMillisecond % (1000 * 60 * 60)) / (1000 * 60);
        long seconds = (runTimeMillisecond % (1000 * 60)) / 1000;
        long millisecond = (runTimeMillisecond % (1000));
        String timeStr = "";
        if (days > 0) {
            timeStr = timeStr + days + "d";
        }
        if (hours > 0) {
            timeStr = timeStr + hours + "h";
        }
        if (minutes > 0) {
            timeStr = timeStr + minutes + "min";
        }
        if (seconds > 0) {
            timeStr = timeStr + seconds + "s";
        }
        if (millisecond > 0) {
            timeStr = timeStr + millisecond + "ms";
        }
        return timeStr;
    }

    public static String getDateYYYY_MM_DD_HH_MM_SS() {

        return formateDateStr(new Date(), DateUtil.YYYY_MM_DD_HH_MM_SS);
    }


    public static Date getEndOfMonth(final Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + 1);
        calendar.set(Calendar.DATE, 0);

        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 0);
        return new Date(calendar.getTimeInMillis());
    }

    public static Date getStartOfMonth(final Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + 1);
        calendar.set(Calendar.DATE, 0);

        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return new Date(calendar.getTimeInMillis());
    }

    public static Date addMonths1(Date date, int months) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, months);
        return cal.getTime();
    }


    public static Date getFirstOfMonth(final Date date) {
        Date lastMonth = addMonths1(date, -1);
        lastMonth = getStartOfMonth(lastMonth);
        return addDays1(lastMonth, 1);
    }

    public static Date addDays1(final Date date, int days) {
        Calendar cal = GregorianCalendar.getInstance();
        if (date == null) {
            cal.setTime(new Date());
        } else {
            cal.setTime(date);
        }
        cal.add(Calendar.DAY_OF_MONTH, days);
        return new Date(cal.getTime().getTime());
    }

    public static Date getCurrentHHMMSS(Date taskEndTime) {
        String end = DateUtil.formateDateStr(taskEndTime, DateUtil.HH_MM_SS);
        String start = DateUtil.formateDateStr(new Date(), DateUtil.YYYY_MM_DD);
        String start_end = start + " " + end;
        return DateUtil.formateDate(start_end, DateUtil.YYYY_MM_DD_HH_MM_SS);
    }

    public static Date getTimeZoneDate(Date date, TimeZone timeZone) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.setTimeZone(timeZone);
        return new Date(calendar.getTimeInMillis());
    }

    public static Date addMonthsFirstDay(Date date, int months) {
        Date addMonths = DateUtil.addMonths(date, months);
        if (addMonths.getDay() == 1) {
            return addMonths;
        }
        return DateUtil.getFirstOfMonth(DateUtil.addMonths(addMonths, 1));
    }

    public static Date addMonthsFirstDay(Date date, int months, TimeZone timeZone) {
        // 将 Date 转为 ZonedDateTime，使用指定的时区
        ZoneId zoneId = timeZone.toZoneId();
        ZonedDateTime zonedDateTime = date.toInstant().atZone(zoneId);
        // 添加月份并设置为第一天，清零时间部分
        zonedDateTime = zonedDateTime.plusMonths(months)
                .withDayOfMonth(1)
                .withHour(0)
                .withMinute(1)
                .withNano(0);

        // 转回 Date 并返回
        return Date.from(zonedDateTime.toInstant());
    }

    public static Date addMonthsByZoneId(Date date, int monthsToAdd, String zoneId) {
        // 获取用户的时区
        TimeZone timeZone = TimeZone.getTimeZone(zoneId);

        // 使用Calendar处理日期和时区
        Calendar calendar = Calendar.getInstance(timeZone);
        calendar.setTime(date);

        // 添加月份
        calendar.add(Calendar.MONTH, monthsToAdd);

        // 将日期设置为月份的第一天，这里不需要特别设置，因为传入的日期应该已是月初
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }

    public static Date addYearsByZoneId(Date date, int yearsToAdd, String zoneId) {
        // 获取用户的时区
        TimeZone timeZone = TimeZone.getTimeZone(zoneId);

        // 使用Calendar处理日期和时区
        Calendar calendar = Calendar.getInstance(timeZone);
        calendar.setTime(date);

        // 添加年份
        calendar.add(Calendar.YEAR, yearsToAdd);

        // 设置为每年的1月1日0点
        calendar.set(Calendar.MONTH, Calendar.JANUARY);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }

    public static boolean isSameMonth(final Date date1, final Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        final Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        final Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);

        return cal1.get(Calendar.ERA) == cal2.get(Calendar.ERA) &&
                cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH);
    }

    public static Long getStampByZone(String timeStr, String zoneId) {
        if (zoneId == null) {
            zoneId = "UTC";
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 解析字符串为 ZonedDateTime
        ZonedDateTime dateTime = ZonedDateTime.parse(timeStr, formatter.withZone(ZoneId.of(zoneId)));
        //ZoneId originalZone = ZoneId.of(zoneId);
        //ZonedDateTime originalDateTime = dateTime.atZone(originalZone);

        return dateTime.toInstant().toEpochMilli();
    }

    /**
     * ERROR:
     *   *   new Date(1717952709000L)  临近时间，获取上海时间的当周开始结束时间是错误的。
     *      *      * 期望返回的是 6.10 - 6.16，但是返回的是6.2-6.9
     * @return
     */
    @Deprecated
    public static Date getStartOfWeek(Date date, TimeZone timeZone) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        long offset = TimeZone.getDefault().getOffset(Calendar.ZONE_OFFSET) - timeZone.getOffset(Calendar.ZONE_OFFSET);
        calendar.add(Calendar.MILLISECOND, (int) offset);
        return calendar.getTime();
    }

    /**
     * Get start of date.
     *
     * @param date Date
     * @return Date Date
     * ERROR
     * new Date(1717952709000L) 临近时间，获取上海时间的当周开始结束时间是错误的。
     *      * 期望返回的是 6.10 - 6.16，但是返回的是6.2-6.9
     *      使用
     */
    @Deprecated
    public static Date getEndOfWeek(Date date, TimeZone timeZone) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        long offset = TimeZone.getDefault().getOffset(Calendar.ZONE_OFFSET) - timeZone.getOffset(Calendar.ZONE_OFFSET);
        calendar.add(Calendar.MILLISECOND, (int) offset);
        return calendar.getTime();
    }

    /**
     * 根据时间戳获取对应时区 自然周开始时间
     * @param timestamp
     * @param zoneId
     * @return
     */
    public static Date getStartOfWeek(long timestamp, ZoneId zoneId) {
        // 使用时间戳创建 ZonedDateTime 对象
        Instant instant = Instant.ofEpochMilli(timestamp);
        ZonedDateTime zonedDateTime = instant.atZone(zoneId);

        // 获取当前日期，并找到本周星期一的日期
        LocalDate localDate = zonedDateTime.toLocalDate();
        LocalDate monday = localDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));

        // 将星期一的日期转换为 ZonedDateTime 对象
        ZonedDateTime mondayStart = monday.atStartOfDay(zoneId);

        // 将 ZonedDateTime 转换为 Date 对象
        return Date.from(mondayStart.toInstant());
    }

    /**
     * 根据时间戳获取对应时区 自然周结束时间
     * @param timestamp
     * @param zoneId
     * @return
     */
    public static Date getEndOfWeek(long timestamp, ZoneId zoneId) {
        // 使用时间戳创建 ZonedDateTime 对象
        Instant instant = Instant.ofEpochMilli(timestamp);
        ZonedDateTime zonedDateTime = instant.atZone(zoneId);

        // 获取当前日期，并找到本周星期日的日期
        LocalDate localDate = zonedDateTime.toLocalDate();
        LocalDate sunday = localDate.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

        // 将星期日的日期转换为 ZonedDateTime 对象，并设置为当天的结束时间
        ZonedDateTime sundayEnd = sunday.atTime(23, 59, 59, 999999999).atZone(zoneId);

        // 将 ZonedDateTime 转换为 Date 对象
        return Date.from(sundayEnd.toInstant());
    }

    public static Date getStartOfDayByZoneId(Date date, String zoneId) {
        // 将传入的日期转换为用户时区的零点时间
        LocalDate localDate = date.toInstant().atZone(ZoneId.of(zoneId)).toLocalDate();
        return Date.from(localDate.atStartOfDay(ZoneId.of(zoneId)).toInstant());
    }

    public static Date getStartOfWeekByZoneId(Date date, String zoneId) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.of(zoneId)).toLocalDate();

        // 找到当前日期对应的星期一
        LocalDate monday = localDate.with(DayOfWeek.MONDAY);

        // 将星期一调整为当天的零点时间
        return Date.from(monday.atStartOfDay(ZoneId.of(zoneId)).toInstant());
    }


    public static Date getStartOfMonth(Date date, TimeZone timeZone) {
        // 创建一个 Calendar 对象，并设置时区为用户时区
        Calendar calendar = Calendar.getInstance(timeZone);
        // 设置 Calendar 对象的日期为当月的一号
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        // 将小时、分钟和秒设置为零
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        // 获取对应时区当月的一月一号零点的时间
        return calendar.getTime();
    }
    public static Date getStartOfMonth(long timestamp, ZoneId zoneId) {
        // 使用时间戳创建 ZonedDateTime 对象
        Instant instant = Instant.ofEpochMilli(timestamp);
        ZonedDateTime zonedDateTime = instant.atZone(zoneId);

        // 获取当前日期，并找到本月1号的日期
        LocalDate localDate = zonedDateTime.toLocalDate();
        LocalDate monday = localDate.with(TemporalAdjusters.firstDayOfMonth());

        // 将星期一的日期转换为 ZonedDateTime 对象
        ZonedDateTime mondayStart = monday.atStartOfDay(zoneId);

        // 将 ZonedDateTime 转换为 Date 对象
        return Date.from(mondayStart.toInstant());
    }

    public static String getStrByDateAndZone(Date date, TimeZone timeZone) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(timeZone);
        return sdf.format(date);

    }

    public static String convertTimeStrByZone(String timeStr, String oriZone, String targetZone) {

        // 原始时区
        ZoneId originalZoneId = ZoneId.of(oriZone);

        // 目标时区
        ZoneId targetZoneId = ZoneId.of(targetZone);

        // 用于解析和格式化的DateTimeFormatter
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        ZonedDateTime utcZonedDateTime = ZonedDateTime.parse(timeStr, formatter.withZone(originalZoneId));

        // 将ZonedDateTime对象转换为目标时区的ZonedDateTime对象
        ZonedDateTime targetZonedDateTime = utcZonedDateTime.withZoneSameInstant(targetZoneId);

        // 格式化目标时区的ZonedDateTime对象为字符串
        String targetTimeString = targetZonedDateTime.format(formatter);

        return targetTimeString;
    }

    public static String getNowStrBYZoneId(String zoneId) {
        ZoneId zone = ZoneId.of(zoneId);
        ZonedDateTime zonedDateTime = ZonedDateTime.now(zone);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedTime = zonedDateTime.format(formatter);
        return formattedTime;
    }

    public static String getCurrentTime(TimeZone timeZone) {
        Date now = new Date();
        return DateUtil.getDate3ByTimeZone(now, timeZone, DATE_TIME_SHORT);
    }

    public static boolean isSameDay(Long time1, Long time2, TimeZone timeZone) {
        long day1 = (time1 + timeZone.getRawOffset()) / (24 * 60 * 60 * 1000L);
        long day2 = (time2 + timeZone.getRawOffset()) / (24 * 60 * 60 * 1000L);
        return day1 == day2;
    }

    public static Date getDateByStrAndZone(String timeStr, String zone) {
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 解析日期时间字符串为 ZonedDateTime
        ZonedDateTime zonedDateTime = ZonedDateTime.parse(timeStr, formatter.withZone(ZoneId.of(zone)));

        // 获取 ZoneId 实例
        //ZoneId zoneId = ZoneId.of(zone);

        // 将 ZonedDateTime 转换为 ZonedDateTime
        //ZonedDateTime zonedDateTime = ZonedDateTime.atZone(zoneId);

        // 最后将 ZonedDateTime 转换为 java.util.Date
        return Date.from(zonedDateTime.toInstant());
    }


    /**
     * 字符串“2024-10-18”按照时区转成Date
     * @param timeStr
     * @param zone
     * @return
     */
    public static Date getDateByDateStrAndZone(String timeStr, String zone) {
        // 将字符串转换为 LocalDate
        LocalDate localDate = LocalDate.parse(timeStr);

        // 获取纽约时区
        ZoneId zoneId = ZoneId.of(zone);

        // 将 LocalDate 转换为 ZonedDateTime
        ZonedDateTime zonedDateTime = localDate.atStartOfDay(zoneId);

        // 转换为 java.util.Date
        Date date = Date.from(zonedDateTime.toInstant());

        // 最后将 ZonedDateTime 转换为 java.util.Date
        return date;
    }


    public static String stringDayAddDays(String dateStr, int days) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD);
        LocalDate startDate = LocalDate.parse(dateStr, formatter);
        return startDate.plusDays(days).format(formatter);
    }


    public static int betweenStartDay(Date startTime, Date endTime) {
        Date startOfNow = DateUtil.startOfDate(startTime);
        Date startOfEnd = DateUtil.startOfDate(endTime);
        return betweenDay(startOfNow, startOfEnd);
    }

    public static int compare(Date c1, Date c2) {
        if (c1 == c2) {
            return 0;
        } else if (c1 == null) {
            return -1;
        } else if (c2 == null) {
            return 1;
        } else {
            return c1.compareTo(c2);
        }
    }

    /**
     *  获取日期的月份，（1-12，1代表一月）
     */
    public static int getDateOfMonth(Date date) {
        // 使用Calendar类来获取月份和日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int month = calendar.get(Calendar.MONTH); // 获取月份（0-11，0代表一月）
        return month+1;
    }
    /**
     *  获取日期（1-31）
     */
    public static int getDateOfDay(Date date) {
        // 使用Calendar类来获取月份和日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_MONTH);
    }

    public Date LocalDateTime2Date(LocalDateTime localDateTime) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
        Instant instant = zonedDateTime.toInstant();
        Date date = Date.from(instant);
        return date;
    }


    public static int dateToWeek(Date dateTime) {
        int[] weekDays = {7, 1, 2, 3, 4, 5, 6};
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dateTime);
        //指示一个星期中的某天
        int w = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) {
            w = 0;
        }
        return weekDays[w];
    }

    public static List<DateStartEnd> getWorkDay(Date monthStart, Date monthEnd) {
        if (monthEnd.getTime() < monthStart.getTime()) {
            return null;
        }
        List<DateStartEnd> list = new ArrayList<>();
        DateStartEnd dateStartEnd = null;
        while (monthStart.getTime() < monthEnd.getTime()) {
            Date currentDate = monthStart;
            int weekDay = dateToWeek(currentDate);
            if (weekDay < 6) {
                if (dateStartEnd == null) {
                    dateStartEnd = new DateStartEnd(currentDate, DateUtil.endOfDate(currentDate));
                } else {
                    dateStartEnd.setEndTime(DateUtil.endOfDate(currentDate));
                }
            } else {
                if (dateStartEnd != null) {
                    list.add(dateStartEnd);
                    dateStartEnd = null;
                }
            }
            monthStart = DateUtil.addDays1(monthStart, 1);
        }
        if (dateStartEnd != null) {
            list.add(dateStartEnd);
        }
        return list;
    }


    public static List<DateStartEnd> getWeekEndDay(Date monthStart, Date monthEnd) {
        if (monthEnd.getTime() < monthStart.getTime()) {
            return null;
        }
        List<DateStartEnd> list = new ArrayList<>();
        DateStartEnd dateStartEnd = null;
        while (monthStart.getTime() < monthEnd.getTime()) {
            Date currentDate = monthStart;
            int weekDay = dateToWeek(currentDate);
            if (weekDay > 5) {
                if (dateStartEnd == null) {
                    dateStartEnd = new DateStartEnd(currentDate, DateUtil.endOfDate(currentDate));
                } else {
                    dateStartEnd.setEndTime(DateUtil.endOfDate(currentDate));
                }
            } else {
                if (dateStartEnd != null) {
                    list.add(dateStartEnd);
                    dateStartEnd = null;
                }
            }
            monthStart = DateUtil.addDays1(monthStart, 1);
        }
        if (dateStartEnd != null) {
            list.add(dateStartEnd);
        }
        return list;
    }

    public static WeekDayDto getNumWeek(Date date) {
        return getNumWeek(date, null);
    }

    public static WeekDayDto getNumWeek(Date date, Date preDate) {
        int week = 0;
        int week1 = dateToWeek(date);                   // 今天是星期几
        int array[] = new int[]{1, 2, 3, 4, 5, 6, 7};
        int flagIndex = 0;
        for (int i = 0; i < array.length; i++) {
            if (Objects.equals(array[i], week1)) {
                flagIndex = i;
                break;
            }
        }
        log.info("当前时间为 date " + DateUtil.formateDateStr(date, DateUtil.YYYY_MM_DD_HH_MM_SS));
        Date date4 = DateUtil.addDays(date, 3 - flagIndex); // 获取星期4的时间
        // 如果这个星期的星期4 在上一个月，因此需要从上一个月去查找
        if (!DateUtil.formateDateStr(date, DateUtil.YYYY_MM).equals(
                DateUtil.formateDateStr(date4, DateUtil.YYYY_MM))
                && date.getTime() > date4.getTime()) {
            log.info("星期4属于上一个月的情况 date4 = " + DateUtil.formateDateStr(date4, DateUtil.YYYY_MM_DD_HH_MM_SS));
            return getNumWeek(date4, date);
        }

        Date dateEnd = DateUtil.addDays(date, 6 - flagIndex);      // 星期天
        Date dateStart = DateUtil.getFirstOfMonth(date4);      // 这个月的第一天
        WeekDayDto weekDayDto = new WeekDayDto();
        weekDayDto.setWeekEnd(DateUtil.endOfDate(dateEnd));

        while (dateStart.getTime() <= dateEnd.getTime()) {
            Date currentDate = dateStart;
            int weekDay = dateToWeek(currentDate);
            if (weekDay == 4) {
                week++;
            }
            log.info("当前时间为 dateStart " + DateUtil.formateDateStr(dateStart, DateUtil.YYYY_MM_DD_HH_MM_SS)
                    + ",compareDate= " + DateUtil.formateDateStr(dateEnd, DateUtil.YYYY_MM_DD_HH_MM_SS));
            dateStart = DateUtil.addDays1(dateStart, 1);
        }

        weekDayDto.setWeek(preDate != null ? dateToWeek(preDate) : dateToWeek(date));              // 星期几
        weekDayDto.setWeekStart(DateUtil.startOfDate(DateUtil.addDays1(date, -week1 + 1)));
        weekDayDto.setWeekN(week);
        weekDayDto.setCurrentMonth(DateUtil.formateDateStr(date4, DateUtil.YYYY_MM));
        String month = DateUtil.formateDateStr(date4, DateUtil._MM);
        if (month.startsWith("0")) {
            month = month.replace("0", "");
        }
        weekDayDto.setMonth(MapUtil.getInteger(month, 0));
        weekDayDto.setBeijingWeekStart(DateUtil.addHours(weekDayDto.getWeekStart(), -8));
        weekDayDto.setBeijingWeekEnd(DateUtil.addHours(weekDayDto.getWeekEnd(), -8));
        weekDayDto.setWeekStartStr(DateUtil.formateDateStr(weekDayDto.getWeekStart(), DateUtil.YYYY_MM_DD_HH_MM_SS));
        weekDayDto.setWeekEndStr(DateUtil.formateDateStr(weekDayDto.getWeekEnd(), DateUtil.YYYY_MM_DD_HH_MM_SS));
        return weekDayDto;
    }


    public static List<WeekDayDto> getWeekDays(Date currentMonth) {
        Date startMonth = DateUtil.getFirstOfMonth(currentMonth);
        int currnetWeek = dateToWeek(new Date());
        Date endMonth = DateUtil.addDays(new Date(), 7 - currnetWeek);

        Date currentDate = startMonth;
        List<Date> week4 = new ArrayList<>();
        for (int i = 0; i < 35; i++) {
            int week = dateToWeek(currentDate);
            if (week == 4) {
                week4.add(currentDate);
            }
            if (currentDate.getTime() > endMonth.getTime()) {
                break;
            }
            if (!DateUtil.formateDateStr(currentMonth, DateUtil.YYYY_MM).equals(   // 如果不是同一个月，则退出循环
                    DateUtil.formateDateStr(currentDate, DateUtil.YYYY_MM))) {
                break;
            }

            currentDate = DateUtil.addDays(currentDate, 1);
        }
        List<WeekDayDto> weekDayDtos = new ArrayList<>();
        for (Date week : week4) {
            weekDayDtos.add(getNumWeek(week));
        }
        return weekDayDtos;
    }

    public static Date convertTimeZone(Date date, String fromZone, String toZone) {

        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.setTimeZone(TimeZone.getTimeZone(fromZone));

        Calendar calNew = Calendar.getInstance();
        calNew.setTimeZone(TimeZone.getTimeZone(toZone));
        calNew.setTime(cal.getTime());

        return calNew.getTime();
    }

    //给站内信特殊情况使用
    public static String convertTimeZoneToString(Date date, String fromZone, String toZone) {
        SimpleDateFormat format = new SimpleDateFormat("MMMM dd, yyyy HH:mm:ss", Locale.ENGLISH);
        format.setTimeZone(TimeZone.getTimeZone(fromZone));  // Original timezone
        String originalDateStr = format.format(date);

        SimpleDateFormat targetFormat = new SimpleDateFormat("MMMM dd, yyyy HH:mm:ss", Locale.ENGLISH);
        targetFormat.setTimeZone(TimeZone.getTimeZone(toZone));  // Target timezone

        String timezoneDate = null;
        try {
            Date originalDate = format.parse(originalDateStr);
            timezoneDate = targetFormat.format(originalDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return timezoneDate;  // Returns the time string in the target timezone
    }

    //获取用户时区当日开始时间，并转成零时区
    public static Date getUserTodayStart(String userZoneId) {
        ZoneId zoneId = ZoneId.of(userZoneId);
        ZonedDateTime currentDateTime = ZonedDateTime.now(ZoneOffset.UTC);

        // 将用户时区应用于当前日期和时间
        ZonedDateTime userZonedDateTime = currentDateTime.withZoneSameInstant(zoneId);

        // 计算用户当天的开始时间和结束时间
        ZonedDateTime startOfDay = userZonedDateTime.truncatedTo(ChronoUnit.DAYS);

        // 转换为零时区的时间
        ZonedDateTime startOfDayUtc = startOfDay.withZoneSameInstant(ZoneOffset.UTC);

        // 转换为java.util.Date类型
        return Date.from(startOfDayUtc.toInstant());
    }

    //获取用户时区当日结束时间，并转成零时区
    public static Date getUserTodayEnd(String userZoneId) {
        ZoneId zoneId = ZoneId.of(userZoneId);
        ZonedDateTime currentDateTime = ZonedDateTime.now(ZoneOffset.UTC);

        // 将用户时区应用于当前日期和时间
        ZonedDateTime userZonedDateTime = currentDateTime.withZoneSameInstant(zoneId);

        // 计算用户当天的开始时间和结束时间
        ZonedDateTime startOfDay = userZonedDateTime.truncatedTo(ChronoUnit.DAYS);
        ZonedDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1);

        // 转换为零时区的时间
        ZonedDateTime endOfDayUtc = endOfDay.withZoneSameInstant(ZoneOffset.UTC);

        // 转换为java.util.Date类型
        return Date.from(endOfDayUtc.toInstant());
    }

    /**
     * 校验字符 是否满足 yyyy-MM-dd
     * @param dateStr 时间字符串
     * @return
     */
    public static boolean isValidYYYYMMDDHHMMSS(String dateStr) {
        String regex = "^(19|20)\\d{2}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])\\s(0[0-9]|1[0-9]|2[0-3]):(0[0-9]|[1-5][0-9]):(0[0-9]|[1-5][0-9])$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(dateStr);
        return matcher.matches();
    }

    //根据用户时间获取当天的开始时间
    public static Date getDayStartByZone(Date date, String zoneId) {
        // 将传递的日期转换为 ZonedDateTime 对象
        ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.of(zoneId));

        // 获取当天的开始时间和结束时间
        ZonedDateTime startDateTime = zonedDateTime.with(LocalTime.MIN);
        ZonedDateTime endDateTime = zonedDateTime.with(LocalTime.MAX);

        // 将 ZonedDateTime 对象转换为 Date 对象
        Date startDate = Date.from(startDateTime.toInstant());
        Date endDate = Date.from(endDateTime.toInstant());
        return startDate;
    }

    //根据用户时间获取当天的结束时间
    public static Date getDayEndByZone(Date date, String zoneId) {
        // 将传递的日期转换为 ZonedDateTime 对象
        ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.of(zoneId));

        // 获取当天的开始时间和结束时间
        ZonedDateTime startDateTime = zonedDateTime.with(LocalTime.MIN);
        ZonedDateTime endDateTime = zonedDateTime.with(LocalTime.MAX);

        // 将 ZonedDateTime 对象转换为 Date 对象
        Date endDate = Date.from(endDateTime.toInstant());
        return endDate;
    }

    //根据用户时间获取当月的开始时间
    public static Date getMonthStartByZone(int year, int month, String zoneId) {
        // 创建表示指定年份和月份的 LocalDate 对象
        LocalDate localDate = LocalDate.of(year, month, 1);

        // 将 LocalDate 对象转换为 ZonedDateTime 对象，使用用户时区
        ZonedDateTime startDateTime = localDate.atStartOfDay(ZoneId.of(zoneId));

        // 返回包含开始时间和结束时间的 DateRange 对象
        return Date.from(startDateTime.toInstant());
    }

    //根据用户时间获取当月的结束时间
    public static Date getMonthEndByZone(int year, int month, String zoneId) {
        // 创建表示指定年份和月份的 LocalDate 对象
        LocalDate localDate = LocalDate.of(year, month, 1);

        // 将 LocalDate 对象转换为 ZonedDateTime 对象，使用用户时区
        ZonedDateTime startDateTime = localDate.atStartOfDay(ZoneId.of(zoneId));

        // 获取当月的结束时间
        ZonedDateTime endDateTime = startDateTime.plusMonths(1).minusSeconds(1);

        // 返回包含开始时间和结束时间的 DateRange 对象
        return Date.from(endDateTime.toInstant());
    }

    //根据用户时间获取当年的开始时间
    public static Date getYearStartByZone(int year, String zoneId) {
        // 创建表示指定年份的 LocalDate 对象
        LocalDate localDate = LocalDate.of(year, 1, 1);

        // 将 LocalDate 对象转换为 ZonedDateTime 对象，使用用户时区
        ZonedDateTime startDateTime = localDate.atStartOfDay(ZoneId.of(zoneId));

        // 将 ZonedDateTime 对象转换为 Date 对象
        return Date.from(startDateTime.toInstant());
    }

    //根据用户时间获取当年的结束时间
    public static Date getYearEndByZone(int year, String zoneId) {
        // 创建表示指定年份的 LocalDate 对象
        LocalDate localDate = LocalDate.of(year, 1, 1);

        // 将 LocalDate 对象转换为 ZonedDateTime 对象，使用用户时区
        ZonedDateTime startDateTime = localDate.atStartOfDay(ZoneId.of(zoneId));

        // 获取当年的结束时间
        ZonedDateTime endDateTime = startDateTime.plusYears(1).minusSeconds(1);

        // 将 ZonedDateTime 对象转换为 Date 对象
        return Date.from(endDateTime.toInstant());

    }

    /**
     将零时区的时间，转成对应时区这个时间点的时间
     * 时间搓会改变
     */
    public static Date convertUtcToOtherDate(Date utcDate, String zoneId) {
        // 获取UTC时间的ZonedDateTime
        ZonedDateTime utcZonedDateTime = utcDate.toInstant().atZone(ZoneId.of("UTC"));

        // 将UTC的ZonedDateTime转换为东八区的ZonedDateTime
        ZonedDateTime targetZonedDateTime = utcZonedDateTime.withZoneSameLocal(ZoneId.of(zoneId));

        // 将东八区的ZonedDateTime转换为Date对象
        return Date.from(targetZonedDateTime.toInstant());
    }

    /**
     * 获取指定日期的零点时刻时间
     *
     * @param date 指定的日期
     * @return
     */
    public static Date getZeroTimeFrom(Date date) {
        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()).with(LocalTime.MIN);

        // 转换为Date对象
        return Date.from(zonedDateTime.toInstant());
    }

    public static ZonedDateTime fromUnixTimeStampMs(Long ms) {
        return ZonedDateTime.ofInstant(Instant.ofEpochMilli(ms), ZoneId.systemDefault());
    }

    //两个时间的差的天数
    public static Date betweenDay(Date date, Date startDay, Date endDay) {
        // 计算日期差
        long diffInMillies = Math.abs(endDay.getTime() - startDay.getTime());
        return new Date(date.getTime() + diffInMillies);
    }

    public static String format(Date date, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format, Locale.getDefault()).withZone(ZoneId.systemDefault());

        return Optional.ofNullable(date).map(date1 -> formatter.format(date1.toInstant())).orElse(null);
    }

    /**
     * 返回两个日期的时间差时差
     * @param begin
     * @param end
     * @return 时间差（秒）
     */
    public static long between(Date begin, Date end) {
        Assert.notNull(begin, "Begin date is null !");
        Assert.notNull(end, "End date is null !");
        long diff = end.getTime() - begin.getTime();
        if (diff < 0) {
            diff = begin.getTime() - end.getTime();
        }
        return diff / 1000;
    }

    public static Date endOfWeek(Date date) {
        Assert.notNull(date, "Date is null !");
        ZonedDateTime localDate = date.toInstant().atZone(ZoneId.systemDefault());

        ZonedDateTime endOfWeek = localDate.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
        return Date.from(endOfWeek.with(LocalTime.MAX).toInstant());
    }

    public static Date beginOfYear(LocalDate date) {
        YearMonth yearMonth = YearMonth.of(date.getYear(), 1); // 1代表1月
        LocalDate localDate = yearMonth.atDay(1);// 返回1月1日
        return Date.from(localDate.atTime(LocalTime.MIN).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date endOfYear(LocalDate date) {
        YearMonth yearMonth = YearMonth.of(date.getYear(), 12); // 12代表12月
        LocalDate localDate = yearMonth.atEndOfMonth();  // 返回12月的最后一天
        return Date.from(localDate.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获得过期时间
     * @param expiredTime
     * @return
     */
    public static Date getAddHours(Integer expiredTime) {
        Date date = new Date();
        if (date.getMinutes() > 0) {
            return DateUtil.addHours(DateUtil.setMinutes(DateUtil.setSeconds(DateUtil.addHours(new Date(), 1), 0), 0), expiredTime * 24);
        } else {
            return DateUtil.addHours(DateUtil.setMinutes(DateUtil.setSeconds(new Date(), 0), 0), expiredTime * 24);
        }
    }

    /**
     * 比较time2 是否等于或大于time1
     * @param time
     * @param time2
     * @return
     */
    public static boolean compareEqualOrAfter(ZonedDateTime time,ZonedDateTime time2){
        if (time == null) {
            return true;
        }
        if(time2 == null){
            return false;
        }
        return time.toInstant().toEpochMilli() <= time2.toInstant().toEpochMilli();
    }
}


