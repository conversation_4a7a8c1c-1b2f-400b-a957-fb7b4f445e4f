package com.linzi.pitpat.core.entity;

import com.linzi.pitpat.core.constants.I18nConstant;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 币种统一请求参数
 */
@Data
@NoArgsConstructor
public class Currency {

    /**
     * 货币名称
     */
    protected String currencyName;

    /**
     * 货币code，USD：美元，CAD：加币 EUR:欧元 GBP：英镑
     *
     * @see I18nConstant.CurrencyCodeEnum
     */
    protected String currencyCode;

    /**
     * 币种符号
     */
    protected String currencySymbol;


    public Currency(String currencyName, String currencyCode, String currencySymbol) {
        this.currencyName = currencyName;
        this.currencyCode = currencyCode;
        this.currencySymbol = currencySymbol;
    }
}
