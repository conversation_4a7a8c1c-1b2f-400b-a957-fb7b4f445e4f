package com.linzi.pitpat.core.constants.enums;

import com.linzi.pitpat.lang.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2022-05-19 15:48
 **/
@Getter
@AllArgsConstructor
public enum DingTalkTokenEnum implements IEnum<String> {
    TEST("测试群", "915e2181f04c2858fd3e58a9a96ea7c9005c88184ba1e01155c3adcb49c5868a", "SEC247842aa95124cd5c0ed5e22ccc4fc11f2f7891d9c998e9ac21e67f76446386a"),
    EXCEPTION_NOTIFICATION("异常通知群", "eb5c7b4b426f7ef8b0c64c6baedfe8539e7e8a2f04003ca9c39ab68826be587d", "SEC05567f2906431fca3e8bfb240eeb89026ead053a939851a1dadcd133de32e0f7"),
    PITPAT_NODE_MONITORING("PitPat节点监控", "af9480fb497789ea7c0c78b0f2c91e78d16fb81173e0e5b097058f947a260eb1", ""),
    PITPAT_ONLINE_ABNORMAL_MONITORING("PitPat线上异常监控", "7c7b6e54e01dbbf8cedd34275309fa2f5890b49e8732817f6551dd5ced928be3", ""),
    PITPAT_REGISTRATION_MONITORING("PitPat Registration Monitoring", "29c0015b0747fbeca8c348c5ea051df92b175a59b3d84144f4fa7f84c45fd384", ""),
    PITPAT_ABNORMAL_MONITORING_ALL("监控告警群", "5da5460d93fcf77f48eae75ea66fa0aa3931380825ac3ffb5ba576a33cb498e0", ""),
    PITPAT_ABNORMAL_MONITORING_CS("CS--CH+PH", "63e4df847e31d291f187b14f0cd9f5d59f31ab03a95403dfee279130985dc059", ""),
    PITPAT_ORDER_NOTIFICATION("PitPat订单通知群", "fa0a183972bb304de5004e7c4f9bb8a2050a8c42b2e8d147f01666ed1a4dfc44", "SEC2e36a3f54a2d2d1a01d915a1b97938b1d793acfacfe03b74806af59ee768db14"),
    PLUS_USER_RUNNING_NOTIFICATION_TEST("Plus会员运动通知测试群", "ad241d1daf92d5541672d09651d5a78981943141cf5d52acf1dac1158bac043d", ""),
    PLUS_USER_RUNNING_NOTIFICATION_ONLINE("Plus会员运动通知线上群", "4baed3c01b8f431682df2a06cbf16ebe43a247ea2ed466a16e82884f687c0dd4", ""),
    ROT_POND_NOTIFY("机器人池变化通知", "1f6b3c8ea5708416ee4df76aee5492247bd65cdbee0a7fc2de6f6c251345e0a1", "SEC131e91cf27f2c2f61450c52dd4583e86cd6a229af3b9cd7f8052ef0e6fdd08ac"),
    TEST_MONITOR("监控测试群", "26bcab29683fe0178ed327e0ff47754eea42fe54a81a59d91620212eeddc8aa9", "SEC9584bdc29015a23c4fcc55f09aabde781ecd146b6ce3eeced7719086fff37f06"),
    DEVICE_EXCEPTION_NOTIFICATION_GROUP("设备异常通知群", "6485df765c25613e306aed94f01a1b8744982c57f59dbcb29b3c011724e7e10c", "SEC9584bdc29015a23c4fcc55f09aabde781ecd146b6ce3eeced7719086fff37f06"),

    //新分类
    PITPAT_HARDWARE_MONITORING("PitPat异常监控群", "6f0ae720c6182b4334a5a49bd6c1cd85b9102592dcc16c4e5217f06fab29d665", ""),
    //新业务监控放在该群里面
    PITPAT_BUSINESS_MONITORING("[后端]业务告警群", "aa4db08a7c8f00024e7a295f7136e034af1deeebdfb6c0d6136ca1534fbbd812", ""),
    PITPAT_MANAGEMENT("[后端]团队管理", "55d8c82b79da60fda685689f5fc1b9ed67bb8b5e05263d7f68b8d1151a825131", ""),


    //PITPAT_FB_MARKETING("FB_marketing","9d975cf67d75e81f1f2348adcac77329704034534ff590cfa4c45b34199fcbfd","SEC8ca9ed3897dbf79f86e0895c8998feed9d4272e3a4cfdb0482176af8c899d7c3");
    PITPAT_FB_MARKETING("FB_marketing", "caa8c86236a11d9bd97fd60fcc94dda79a42008d194c33180d025dd7bbe5f4a2", "SEC3ed3656703eb72f04da1bfb1ecfd5132dd3be90b28925e42b84ebe642731180c"),

    PITPAT_JOIN_ROOM("用户塞监控提醒群", "c4bbc16cbbedff7d3e6ab4cd5244709ce2bc51f53db1829d5b8fc7335ba6b1c4", "SECb99e276e1126b197f6de901aabadfddd9f6dc33cefcf1feea5f7bb6464939502"),
    PITPAT_CREATE_ROOM("用户塞监控提醒群", "10f3f0842b03ec02b542f6c8aee804260386f18aff494e0b6ecda188761c332f", "SECf35f61256b99acee5445154e3d478b8bec2cc4a51b9a8b0418f6b7c3fe845ab9"),
    PITPAT_CREATE_ROOM_TEST("用户赛test监控提醒群", "e78a9e72e0b161a26eac45ce6310342ba24acab5b5f7726ffa6a24acd9018c14", "SECf757ece14c42d2c7c8c345ce78117a9a0283046f2217a5b78762416c4b0cfdcd"),

    SOCKET_DISCONNECT_MONITORING_ONLINE("[online]sockect断连监控群", "27ef34330943e48ddbf88a07eea0855f4b1f008e54011f15ffc81d01405e622a", "SECec3b5555a7503d6a4175ac49b747b024c95c52df3d02ca33b9578b5884de62d5"),
    SOCKET_DISCONNECT_MONITORING_TEST("[test]sockect断连监控群", "8b8901a3c448468ed9a78bdf58d52f282f69aa8a1b3cb382b875bbfbef59d72f", "SECb08b2e4dbbf7724f477cec94e31f357bfd03a06174fe72eada14623ee5f7142e"),
    HARDWARE_SN_MONITORING("[硬件]工厂SN码打印", "3dd488b9f33c7991904ee6d5c6140ee08a954a9e7f0bf83b03a96a7fcf27e34f", ""),
    HARDWARE_REGISTER_MONITORING("[硬件]工厂电控板注册", "cc2419d399b9c8419be708d94314aba5828d332e3a492f4a3e20b2cff50ec731", ""),

    MALL_ORDER_NOTICE_TEST("订单消息测试群", "0334659c26c1928858b15ef21268f066b5dd6adf4b8c407270821232fae45618", "SEC88cb8ce715fac1204d1c88d0e81d23a40d1a1d9a95c13ba8327ca654a194bb0c"),
    MALL_ORDER_NOTICE_ONLINE("订单消息线上群", "dbe7729fd653deaa7f8f5b68e468de926a4ed0fcd4d1027b71cc649c6740ad00", "SECdfa258c27e4a2abe55c34947b93ccb65b8a17490bb412dcaf72d9a6f83ca8c2a"),

    ;
    private final String name;

    private final String token;  //机器人 Webhook 地址中的 access_token

    private final String secret;

    public static DingTalkTokenEnum resolve(String token) {
        return Arrays.stream(DingTalkTokenEnum.values()).filter(e -> e.getToken().equals(token)).findFirst().orElse(EXCEPTION_NOTIFICATION);

    }

    @Override
    public String getCode() {
        return token;
    }
}

