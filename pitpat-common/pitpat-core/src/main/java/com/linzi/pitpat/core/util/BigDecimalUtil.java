/*
 *@Copyright (c) 2016, 浙江阿拉丁电子商务股份有限公司 All Rights Reserved.
 */
package com.linzi.pitpat.core.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * <AUTHOR> 2017年1月18日  12:51:33
 * @类描述：金额相关计算
 * @注意：本内容仅限于浙江阿拉丁电子商务股份有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
public class BigDecimalUtil {

    public static final BigDecimal ONE_MINUTE = new BigDecimal(60);
    /**
     * 加法,保留小数点两位
     *
     * @param v1
     * @param v2
     * @return
     */
    public static BigDecimal add(BigDecimal v1, BigDecimal v2) {
        v1 = v1 == null ? new BigDecimal(0) : v1;
        v2 = v2 == null ? new BigDecimal(0) : v2;
        v1 = v1.add(v2).setScale(2, RoundingMode.HALF_UP);
        return v1;
    }


    /**
     * @param array
     * @return
     */
    public static BigDecimal add(BigDecimal... array) {
        BigDecimal result = BigDecimal.ZERO;
        if (array == null || array.length == 0) {
            return BigDecimal.ZERO;
        }
        for (int i = 0; i < array.length; i++) {
            result = add(result, array[i]);
        }
        return result;
    }

    /**
     * 加法,保留小数点两位
     *
     * @param v1
     * @param v2
     * @return
     */
    public static BigDecimal add(double v1, double v2) {
        BigDecimal value = new BigDecimal(v1).add(new BigDecimal(v2)).setScale(2, RoundingMode.HALF_UP);
        return value;
    }

    /**
     * 减法v1-v2
     *
     * @param v1
     * @param v2
     * @return
     */
    public static BigDecimal subtract(BigDecimal v1, BigDecimal v2) {
        v1 = v1 == null ? new BigDecimal(0) : v1;
        v2 = v2 == null ? new BigDecimal(0) : v2;
        v1 = v1.subtract(v2).setScale(2, RoundingMode.HALF_UP);
        return v1;
    }

    /**
     * @param array
     * @return
     */
    public static BigDecimal subtract(BigDecimal... array) {
        if (array == null || array.length == 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal result = array[0];
        for (int i = 1; i < array.length; i++) {
            result = subtract(result, array[i]);
        }
        return result;
    }

    /**
     * 减法v1-v2
     *
     * @param v1
     * @param v2
     * @return
     */
    public static BigDecimal subtract(double v1, double v2) {
        BigDecimal value = new BigDecimal(v1).subtract(new BigDecimal(v2)).setScale(2, RoundingMode.HALF_UP);
        return value;
    }

    /**
     * 乘法 v1*v2
     *
     * @param v1
     * @param v2
     * @return
     */
    public static BigDecimal multiply(double v1, double v2) {
        BigDecimal value = new BigDecimal(v1).multiply(new BigDecimal(v2)).setScale(2, RoundingMode.HALF_UP);
        return value;
    }

    /**
     * 乘法 v1*v2
     *
     * @param v1
     * @param v2
     * @return
     */
    public static BigDecimal multiply(BigDecimal v1, BigDecimal v2) {
        v1 = v1 == null ? new BigDecimal(0) : v1;
        v2 = v2 == null ? new BigDecimal(0) : v2;
        v1 = v1.multiply(v2).setScale(2, RoundingMode.HALF_UP);
        return v1;
    }

    public static BigDecimal mul(Object... obj) {
        BigDecimal v1 = objToBigDecimalDefault(obj[0], BigDecimal.ZERO);
        for (int i = 1; i < obj.length; i++) {
            v1 =  multiply(v1,objToBigDecimalDefault(obj[i],BigDecimal.ZERO));
        }
        return v1;
    }

    private static BigDecimal objToBigDecimalDefault(Object obj, BigDecimal defaultValue) {
        if (null == obj) {
            return defaultValue;
        }
        try {
            return new BigDecimal(obj.toString());
        } catch (Exception e) {
            return defaultValue;
        }
    }


    /**
     * 多个Bigdecimal相乘
     *
     * @param array
     * @return
     */
    public static BigDecimal multiply(BigDecimal... array) {
        BigDecimal result = BigDecimal.ONE;
        if (array == null || array.length == 0) {
            return BigDecimal.ZERO;
        }
        for (int i = 0; i < array.length; i++) {
            result = multiply(result, array[i]);
        }
        return result;
    }

    /**
     * 除法 v1/v2(v2为0时未抛异常，注意不传空)
     *
     * @param v1
     * @param v2
     * @return
     */
    public static BigDecimal divide(double v1, double v2) {
        BigDecimal value = new BigDecimal(v1).divide(new BigDecimal(v2), 2, RoundingMode.HALF_UP);
        return value;
    }

    /**
     * 除法 v1/v2(v2为0时未抛异常，注意不传空)
     *
     * @param v1
     * @param v2
     * @return
     */
    public static BigDecimal divide(BigDecimal v1, BigDecimal v2) {
        v1 = v1 == null ? new BigDecimal(0) : v1;
        v2 = v2 == null ? new BigDecimal(0) : v2;
        v1 = v1.divide(v2, 2, RoundingMode.HALF_UP);
        return v1;
    }

    public static BigDecimal div(BigDecimal v1, Object... v2) {
        for (Object v : v2) {
            v1 = divide(v1, MapUtil.getBigDecimal(v ,new BigDecimal(1) ));
        }
        return v1;
    }


    public static BigDecimal divide6(BigDecimal v1, BigDecimal v2) {
        v1 = v1 == null ? new BigDecimal(0) : v1;
        v2 = v2 == null ? new BigDecimal(0) : v2;
        v1 = v1.divide(v2, 6, RoundingMode.HALF_UP);
        return v1;
    }



    public static BigDecimal divide_scale1(BigDecimal v1, BigDecimal v2) {
        v1 = v1 == null ? new BigDecimal(0) : v1;
        v2 = v2 == null ? new BigDecimal(0) : v2;
        v1 = v1.divide(v2, 1, RoundingMode.HALF_UP);
        return v1;
    }


    public static BigDecimal convert_miles(BigDecimal v1, BigDecimal v2) {
        BigDecimal a = divide(v1,v2);
        BigDecimal b = divide_scale1(v1,v2);
        if(a.compareTo(b) > 0 ){
            log.info("比较 convert_miles v1=" + v1 + ",v2="+v2 + ",a =" + a + ",b =" + b );
            return BigDecimalUtil.getScale( b.add(new BigDecimal(0.1)),2);
        }
        log.info("last convert_miles v1=" + v1 + ",v2="+v2 + ",a =" + a + ",b =" + b );
        return  BigDecimalUtil.getScale( b ,2) ;
    }


    /**
     * 除法，针对除不尽的情况做进一位处理
     * 例如：10/3 = 3.33 进位处理 3.34
     *
     * @param d1    除数
     * @param d2    被除数
     * @param digit 保留小数点(默认两位小数)
     * @return
     * <AUTHOR>
     */
    public static BigDecimal divHalfUp(BigDecimal d1, BigDecimal d2, Integer digit) {
        if (null == digit) {
            digit = 2;
        }
        BigDecimal result = d1.divide(d2, digit, BigDecimal.ROUND_UP);
        return result;
    }

    /**
     * 除法，针对除不尽的情况做进一位处理
     * 例如：10/3 = 3.33 进位处理 3.34
     *
     * @param d1    除数
     * @param d2    被除数
     * @param digit 保留小数点(默认两位小数)
     * @return
     * <AUTHOR>
     */
    public static BigDecimal divHalfDown(BigDecimal d1, BigDecimal d2, Integer digit) {
        if (null == digit) {
            digit = 2;
        }
        if (d2.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal result = d1.divide(d2, digit, BigDecimal.ROUND_DOWN);
        return result;
    }

    public static BigDecimal valueOf(String s) {
        if (!StringUtils.hasText(s)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(s);
    }


    /**
     * @param value
     * @param scale 保留几位有效果数字
     * @return
     */
    public static BigDecimal getScale(BigDecimal value, int scale){
        if(value == null){
            return BigDecimal.ZERO;
        }
        return  value.setScale(scale, BigDecimal.ROUND_HALF_UP);
    }



    /**
     * @description: 截短模式
     *
     * @author: jacky
     * @version: V1.0
     * @date: 2023/12/4 下午10:42
     **/
    public static BigDecimal getDownScale(BigDecimal value, int scale){
        if(value == null){
            return BigDecimal.ZERO;
        }
        return  value.setScale(scale, RoundingMode.DOWN);
    }


    /**
     * @param value
     * @param scale 保留几位有效果数字
     * @return
     */
    public static BigDecimal getScaleHavfDown(BigDecimal value, int scale){
        if(value == null){
            return BigDecimal.ZERO;
        }
        return  value.setScale(scale, BigDecimal.ROUND_HALF_DOWN);
    }

    public static BigDecimal removeEndOfZero(BigDecimal value) {
        if (value == null) {
            return null;
        }
        String a = value + "";
        int index = a.indexOf(".");
        if (index == -1) {
            return value;
        }
        if (a.endsWith("0")) {
            char[] b = a.toCharArray();
            // 如果以0结尾，从最后一个小数点向前数，看0的个数
            int j = 0;
            for (int i = b.length - 1; i >= 0; i--) {
                if (b[i] != '0') {
                    break;
                }
                j++;
            }
            int dot = a.length() - index - 1;
            int scale = dot - j;
            return BigDecimalUtil.getScale(value, scale);
        }
        return value;
    }
    // 将磅转换为克
    public static BigDecimal convertToGrams(int weightInPounds) {
        // 磅转换为克
        BigDecimal pounds = new BigDecimal(weightInPounds);
        BigDecimal grams = pounds.multiply(new BigDecimal("45359237"));
        return grams.divide(new BigDecimal("100000"), BigDecimal.ROUND_HALF_UP);
    }

    // 将克转换回磅
    public static BigDecimal convertToPounds(BigDecimal weightInGrams) {
        // 克转换回磅
        BigDecimal pounds = weightInGrams.multiply(new BigDecimal("100000"));
        BigDecimal grams = new BigDecimal("45359237");
        return pounds.divide(grams, 0, BigDecimal.ROUND_HALF_UP);
    }

    public static BigDecimal max(BigDecimal n1, BigDecimal n2) {
        if (n1 == null) {
            return n2;
        }
        if(n2==null){
            return n1;
        }
        return n1.compareTo(n2)>0?n1:n2;
    }

    public static BigDecimal min(BigDecimal n1, BigDecimal n2) {
        if (n1 == null) {
            return n2;
        }
        if(n2==null){
            return n1;
        }
        return n1.compareTo(n2)>0?n2:n1;
    }

    public static BigDecimal smallValueGeZero(BigDecimal n1, BigDecimal n2) {
        final BigDecimal num1 = Objects.requireNonNullElse(n1, BigDecimal.ZERO);
        final BigDecimal num2 = Objects.requireNonNullElse(n2, BigDecimal.ZERO);
        if (num1.compareTo(BigDecimal.ZERO) <= 0) {
            return num2;
        }
        if (num2.compareTo(BigDecimal.ZERO) <= 0) {
            return num1;
        }
        return num1.compareTo(num2) < 0 ? num1 : num2;
    }
}
