package com.linzi.pitpat.core.util;


import java.math.BigDecimal;
import java.math.RoundingMode;

public class SpeedUtil {

    /**
     * speed 多少公里每小时
     * second 多少秒
     */
    public static int mileage(BigDecimal speed, int second) {
        return speed.multiply(new BigDecimal(1000 * second))
                .divide(new BigDecimal(3600), 2, RoundingMode.HALF_DOWN).intValue();
    }

    /**
     * speed 多少公里每小时
     * second 多少秒
     */
    public static int mileageUp(BigDecimal speed, int second) {
        //与游戏算法一致，先算速度m/s
        BigDecimal speedMs = speed.multiply(new BigDecimal(1000)).divide(new BigDecimal(3600), 2, RoundingMode.HALF_UP);
        return speedMs.multiply(new BigDecimal(second)).intValue();
    }


    /**
     * speed 多少公里每小时
     * second 多少秒
     */
    public static BigDecimal mileageBigDecimal(BigDecimal speed, int second) {
        return speed.multiply(new BigDecimal(1000 * second))
                .divide(new BigDecimal(3600), 2, RoundingMode.HALF_DOWN);
    }

    /**
     * km每小时转化为m每秒
     * @param kmh
     * @return
     */
    public static BigDecimal speedKmh2Ms(BigDecimal kmh) {
        return kmh.multiply(new BigDecimal(1000))
                .divide(new BigDecimal(3600), 10, RoundingMode.HALF_UP);
    }

    public static int second(BigDecimal speed, Integer targetMileage) {
        BigDecimal m = speed.multiply(new BigDecimal(1000))
                .divide(new BigDecimal(3600), 10, RoundingMode.HALF_UP);
        BigDecimal second = new BigDecimal(targetMileage).divide(m, 2, RoundingMode.HALF_DOWN);
        int intSecond= second.intValue();
        if(second.compareTo(new BigDecimal(intSecond)) > 0 ){
            return intSecond + 1 ;
        }
        return intSecond;
    }

    public static BigDecimal getV(BigDecimal m , int second) {
        return BigDecimalUtil.divide(m.multiply(new BigDecimal(3.6)), new BigDecimal(second));
    }



    /**
     *
     * @param target 米
     * @param v  千米/h
     * @return
     */
    public static int getRunTime(BigDecimal target, BigDecimal v) {
        return target.multiply(new BigDecimal(3.6))
                .divide(v, 2, RoundingMode.HALF_DOWN).intValue();
    }



    /**
     *
     * @param target 米
     * @param v  千米/h
     * @return
     */
    public static int getRunTimeHalfUp(BigDecimal target, BigDecimal v) {
        BigDecimal a =  target.multiply(new BigDecimal(3.6))
                .divide(v, 6, RoundingMode.HALF_UP);
        a = a.setScale(0,BigDecimal.ROUND_UP);                      //向上取整数
        return a.intValue();
    }

    public static void main(String[] args) {
//        System.out.println(getRunTimeHalfUp(new BigDecimal(280) ,new BigDecimal(10.9)));
        int runTime = getRunTime(new BigDecimal(500), new BigDecimal(2.2));
        System.out.println(runTime);

    }

}
