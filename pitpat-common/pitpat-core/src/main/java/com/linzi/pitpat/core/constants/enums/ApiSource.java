package com.linzi.pitpat.core.constants.enums;

import lombok.AllArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

@AllArgsConstructor
public enum ApiSource {
    APP("app"),
    H5("h5"),
    GAME("game"),
    DEVICE("device");
    private String name;

    private static final ApiSource[] VALUES;

    static {
        VALUES = values();
    }

    public static ApiSource reslove(String name) {
        return Arrays.stream(VALUES).filter(item -> Objects.equals(name, item.name)).findFirst().orElse(null);
    }
}
