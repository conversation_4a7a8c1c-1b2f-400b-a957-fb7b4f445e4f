package com.linzi.pitpat.core.constants.enums;

import com.linzi.pitpat.lang.IEnum;
import lombok.Getter;

import java.util.Arrays;

/**
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@Getter
public enum AppType implements IEnum<Integer> {
    IOS(2, "ios"),
    ANDROID(1, "android");

    private final static AppType[] VALUES = values();

    private final Integer code;
    private final String name;

    AppType(Integer type, String name) {
        this.code = type;
        this.name = name;
    }

    public static AppType resolve(Integer code) {
        return Arrays.stream(VALUES).filter(e -> e.getCode().equals(code)).findFirst().orElse(ANDROID);
    }
}
