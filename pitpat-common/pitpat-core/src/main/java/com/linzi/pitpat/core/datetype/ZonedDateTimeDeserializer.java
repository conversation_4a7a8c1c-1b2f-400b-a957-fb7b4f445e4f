package com.linzi.pitpat.core.datetype;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.core.JsonTokenId;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.datatype.jsr310.deser.JSR310DateTimeDeserializerBase;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * 自定义 ZonedDateTimeDeserializer 参考 InstantDeserializer， 除了支持自定的 yyyy-MM-dd HH:mm:ss 格式，还支持 @JsonFormat注解
 */
@Slf4j
public class ZonedDateTimeDeserializer extends JSR310DateTimeDeserializerBase<ZonedDateTime> {

    // 自定义日期时间格式，例如 'yyyy-MM-dd HH:mm:ss'
    private static final DateTimeFormatter customFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    // 标准的 ISO 格式解析器
    private static final DateTimeFormatter isoZonedFormatter = DateTimeFormatter.ISO_ZONED_DATE_TIME;
    private static final DateTimeFormatter isoInstantFormatter = DateTimeFormatter.ISO_INSTANT;

    protected final BiFunction<ZonedDateTime, ZoneId, ZonedDateTime> adjust = ZonedDateTime::withZoneSameInstant;
    protected final Function<TemporalAccessor, ZonedDateTime> parsedToValue = ZonedDateTime::from;

    public ZonedDateTimeDeserializer() {
        super(ZonedDateTime.class, null);
    }

    protected ZonedDateTimeDeserializer(Class<ZonedDateTime> supportedType, DateTimeFormatter f) {
        super(supportedType, f);
    }

    @Override
    protected JSR310DateTimeDeserializerBase<ZonedDateTime> withDateFormat(DateTimeFormatter dtf) {
        return dtf == this._formatter ? this : new ZonedDateTimeDeserializer(ZonedDateTime.class, dtf);
    }

    @Override
    protected JSR310DateTimeDeserializerBase<ZonedDateTime> withLeniency(Boolean leniency) {
        return new ZonedDateTimeDeserializer(ZonedDateTime.class, this._formatter);
    }

    @Override
    protected JSR310DateTimeDeserializerBase<ZonedDateTime> withShape(JsonFormat.Shape shape) {
        return this;
    }

    @Override
    public ZonedDateTime deserialize(JsonParser parser, DeserializationContext context) throws IOException {
        switch (parser.currentTokenId()) {
            case JsonTokenId.ID_STRING:
                return this.fromString(parser, context, parser.getText());
            case JsonTokenId.ID_NUMBER_INT:
                return this.fromLong(context, parser.getLongValue());
            default:
                throw new IOException(String.format("Unexpected token (%s), expected one of %s for %s value", parser.currentToken(), new JsonToken[]{JsonToken.VALUE_STRING, JsonToken.VALUE_NUMBER_INT, JsonToken.VALUE_NUMBER_FLOAT}, this.handledType().getName()));
        }
    }

    private ZonedDateTime fromLong(DeserializationContext context, long longValue) {
        return Instant.ofEpochMilli(longValue).atZone(context.getTimeZone().toZoneId());
    }

    private ZonedDateTime fromString(JsonParser parser, DeserializationContext context, String text) {
        int dots = _countPeriods(text);
        if (dots >= 0) { // negative if not simple number
            try {
                if (dots == 0) {
                    return fromLong(context, Long.parseLong(text));
                }
                //if (dots == 1) {
                //    return _fromDecimal(ctxt, new BigDecimal(string));
                //}
            } catch (NumberFormatException e) {
                // fall through to default handling, to get error there
            }
        }

        TemporalAccessor acc;
        if (text.contains("T")) {
            //if (text.endsWith("Z")) {
            //    Instant instant = isoInstantFormatter.parse(text, Instant::from);
            //    return ZonedDateTime.ofInstant(instant, context.getTimeZone().toZoneId());
            //} else {
            acc = ZonedDateTime.parse(text, isoZonedFormatter.withZone(context.getTimeZone().toZoneId()));
            //}
        } else {

            if (_formatter != null) {
                acc = ZonedDateTime.parse(text, _formatter);//.withZoneSameInstant(ZoneId.of("UTC")); // shouldAdjustToContextTimezone
                //acc= ZonedDateTime.parse(text, _formatter).withZoneSameInstant(ZoneId.of("UTC"));
            } else {
                acc = ZonedDateTime.parse(text, customFormatter.withZone(context.getTimeZone().toZoneId()));
            }
        }

        ZonedDateTime value = parsedToValue.apply(acc);
        if (shouldAdjustToContextTimezone(context)) {
            return adjust.apply(value, getZone(context));
            //acc = acc.withZoneSameInstant(ZoneId.of("UTC"));
        }
        return value;


        //  ZonedDateTime::withZoneSameInstant,
        //a -> ZonedDateTime.ofInstant(Instant.ofEpochSecond(a.integer, a.fraction), a.zoneId),
        //  a -> ZonedDateTime.ofInstant(Instant.ofEpochMilli(a.value), a.zoneId),
    }

    // Helper method to find Strings of form "all digits" and "digits-comma-digits"
    protected int _countPeriods(String str) {
        int commas = 0;
        for (int i = 0, end = str.length(); i < end; ++i) {
            int ch = str.charAt(i);
            if (ch < '0' || ch > '9') {
                if (ch == '.') {
                    ++commas;
                } else {
                    return -1;
                }
            }
        }
        return commas;
    }

    protected boolean shouldAdjustToContextTimezone(DeserializationContext context) {
        return context.isEnabled(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE);
    }

    private ZoneId getZone(DeserializationContext context) {
        // Instants are always in UTC, so don't waste compute cycles
        return (_valueClass == Instant.class) ? null : context.getTimeZone().toZoneId();
    }

}
