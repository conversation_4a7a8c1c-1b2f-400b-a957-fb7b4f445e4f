package com.linzi.pitpat.core.entity;

import com.linzi.pitpat.core.constants.I18nConstant;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;

/**
 * 多币种金额
 */
@Data
@NoArgsConstructor
public class CurrencyAmount extends Currency{

    /**
     * 币种金额
     */
    protected BigDecimal amount;

    public CurrencyAmount(Currency currency,BigDecimal amount) {
        BeanUtils.copyProperties(currency,this);
        this.amount = I18nConstant.currencyFormat(currency.getCurrencyCode(),amount);
    }
}
