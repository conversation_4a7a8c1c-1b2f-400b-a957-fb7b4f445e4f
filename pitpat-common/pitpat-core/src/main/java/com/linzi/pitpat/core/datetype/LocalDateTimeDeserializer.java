package com.linzi.pitpat.core.datetype;


import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public class LocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> implements Serializable {

    private static final long serialVersionUID = 1L;
    private final DateTimeFormatter formatter;
    private final Boolean enableTimestamp;

    public LocalDateTimeDeserializer(DateTimeFormatter formatter) {
        this(formatter, false);
    }

    public LocalDateTimeDeserializer(DateTimeFormatter formatter, Boolean enableTimestamp) {
        this.formatter = formatter;
        this.enableTimestamp = enableTimestamp;
    }

    @Override
    public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        ZonedDateTime shanghaiTime;
        if (enableTimestamp) {
            long dateTimeStr = p.getValueAsLong();
            shanghaiTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(dateTimeStr), ZoneId.of("UTC"));
        } else {
            String dateTimeStr = p.getValueAsString();
            shanghaiTime = ZonedDateTime.parse(dateTimeStr, formatter.withZone(ZoneId.of("Asia/Shanghai")));
        }
        return shanghaiTime.withZoneSameInstant(ZoneOffset.UTC).toLocalDateTime();
    }
}
