package com.linzi.pitpat.core.util;

import lombok.extern.slf4j.Slf4j;
import org.lionsoul.ip2region.xdb.Searcher;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * ip地址解析工具类
 */
@Slf4j
public class RegionUtil {

    // 未知地址
    public static final String UNKNOWN = "XX XX";
    static Searcher searcher = null;

    /**
     * 初始化IP库
     */
    static {
        try {
            ClassPathResource classPathResource = new ClassPathResource("ip2region/ip2region.xdb");
            byte[] bytes = toByteArray(classPathResource.getInputStream());
            searcher = Searcher.newWithBuffer(bytes);
        } catch (Exception e) {
            log.error("ip地址解析工具类-----init ip region error:", e);
        }
    }

    private static byte[] toByteArray(InputStream inputStream) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            byteArrayOutputStream.write(buffer, 0, bytesRead);
        }
        return byteArrayOutputStream.toByteArray();
    }

    /**
     * 解析IP
     *
     * @param ip
     * @return
     */
    public static Map<String, String> getRegion(String ip) {
        Map<String, String> geoAddress = new HashMap<>();
        if(!StringUtils.hasText(ip)){
            return  geoAddress;
        }
        try {
            // db
            if (searcher == null || !StringUtils.hasText(ip)) {
                log.error("ip地址解析工具类-----DbSearcher is null");
                geoAddress = Map.of("country", UNKNOWN, "province", UNKNOWN, "city", UNKNOWN);
            }

            String result = searcher.search(ip);
            if (StringUtils.hasText(result)) {
                if(log.isDebugEnabled()){
                    log.debug("ip地址解析工具类-----获取地理位置 ip={},result={}", ip, result);
                }
                String[] obj = result.split("\\|");
                String country = obj[0];
                String province = obj[2];
                String city = obj[3];
                geoAddress = Map.of("country", country, "province", province, "city", city);
            }

        } catch (Exception e) {
            log.error("ip地址解析工具类-----error:", e);
        }
        return geoAddress;
    }

    public static String getCountry(String ip) {
        Map<String, String> geoAddress = getRegion(ip);
        if (CollectionUtils.isEmpty(geoAddress)){
            return "0";
        }
        return geoAddress.get("country");
    }

    /**
     * 解析IP
     *
     * @param ip
     * @return
     */
    public static String getRealAddressByIP(String ip) {
        try {
            // 内网不查询
            ip = Arrays.asList("0:0:0:0:0:0:0:1", "::1").contains(ip) ? "127.0.0.1" : ip;
            Map<String, String> region = getRegion(ip);
            if (Objects.equals(region.get("city"), "内网IP")) {
                return region.get("city");
            }
            return String.format("%s %s %s", region.get("country"), region.get("province"), region.get("city"));
        } catch (Exception e) {
            log.error("ip地址解析工具类-----error:", e);
        }
        return UNKNOWN;
    }
}
