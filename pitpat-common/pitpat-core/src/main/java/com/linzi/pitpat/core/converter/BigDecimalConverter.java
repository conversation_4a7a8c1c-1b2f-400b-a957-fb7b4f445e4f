package com.linzi.pitpat.core.converter;

import org.mapstruct.Named;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

public class BigDecimalConverter {

    /**
     * 默认转出的 BigDecimal 保留两位精度
     *
     * @param value
     * @return
     */
    @Named("mapBigDecimalScale")
    public static BigDecimal setBigDecimalScale(BigDecimal value) {
        if (Objects.isNull(value)) {
            return BigDecimal.ZERO;
        }
        return value.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * BigDecimal to double 默认两位精度
     *
     * @param value
     * @return
     */
    @Named("mapBigDecimalToDoubleScale")
    public static Double setBigDecimalToDoubleScale(BigDecimal value) {
        if (Objects.isNull(value)) {
            return 0D;
        }
        return value.setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * BigDecimal to float 默认两位精度
     *
     * @param value
     * @return
     */
    @Named("mapBigDecimalToFloatScale")
    public static Float setBigDecimalToFloatScale(BigDecimal value) {
        if (Objects.isNull(value)) {
            return 0F;
        }
        return value.setScale(2, RoundingMode.HALF_UP).floatValue();
    }
}
