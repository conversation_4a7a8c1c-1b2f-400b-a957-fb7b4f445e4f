package com.linzi.pitpat.core.util;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.linzi.pitpat.core.constants.enums.DingTalkMsgTypeEnum;
import com.linzi.pitpat.core.util.dto.DingTalkRequestDto;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Objects;

/**
 * @description:
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/

/**
 * 钉钉消息通知 消息类型：https://open.dingtalk.com/document/isvapp/robot-reply-send-message
 */
@Slf4j
public class DingTalkUtils {

    private static final String URL = "https://oapi.dingtalk.com/robot/send";


    /**
     * 仅在线上环境发送消息
     *
     * @param requestDto
     */
    public static void sendMsgOnline(DingTalkRequestDto requestDto, String profile) {
        if (!EnvUtils.isOnline(profile)) {
            log.warn("测试环境不推送钉钉通知, accessToken = {}", requestDto.getAccessToken());
            return;
        }
        sendMsg(requestDto);
    }

    /**
     * 支持 文本消息 DingTalkRequestDto.of() 和 markdown 消息 DingTalkRequestDto.ofMarkdown()
     * 这个方法还可以进一步进化，使用
     * 调用钉钉官方接口发送钉钉消息（新版本，需要配置安全设置）
     *
     * @param requestDto payload
     */
    public static void sendMsg(DingTalkRequestDto requestDto) {
        String url = URL + "?access_token=" + requestDto.getAccessToken();
        if (StringUtils.hasText(requestDto.getSecret())) {
            Long timestamp = System.currentTimeMillis();
            String sign = getSign(requestDto.getSecret(), timestamp);
            url += "&sign=" + sign + "&timestamp=" + timestamp;
        }
        DingTalkClient client = new DefaultDingTalkClient(url);
        OapiRobotSendRequest request = new OapiRobotSendRequest();

        //默认值
        if (Objects.isNull(requestDto.getMsgType())) {
            requestDto.setMsgType(DingTalkMsgTypeEnum.TEXT);
        }

        request.setMsgtype(requestDto.getMsgType().getCode());

        if (Objects.equals(requestDto.getMsgType().getCode(), DingTalkMsgTypeEnum.MARKDOWN.getCode())) {
            OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
            markdown.setTitle("New Message");
            //支持markdown 类型的at
            StringBuilder builder = new StringBuilder();
            if (!CollectionUtils.isEmpty(requestDto.getMobile())) {
                requestDto.getMobile().forEach(mobile -> builder.append("@").append(mobile).append(" "));
            }
            builder.append(requestDto.getMessage());
            markdown.setText(builder.toString());
            request.setMarkdown(markdown);
        } else if (Objects.equals(requestDto.getMsgType().getCode(), DingTalkMsgTypeEnum.TEXT.getCode())) {
            OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
            text.setContent(requestDto.getMessage());
            request.setText(text);
        }

        // 被@人的手机号(在text内容里要有@手机号)
        if (!CollectionUtils.isEmpty(requestDto.getMobile())) {
            OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
            at.setAtMobiles(requestDto.getMobile());
            request.setAt(at);
        }

        try {
            OapiRobotSendResponse response = client.execute(request);
            log.info("【发送钉钉群消息】消息响应结果：{}", response.getBody());
        } catch (ApiException e) {
            log.error("【发送钉钉群消息】请求钉钉接口异常，accessToken={}, errMsg = {}", requestDto.getAccessToken(), e.getMessage(), e);
        }
    }

    /**
     * 计算签名
     *
     * @param secret    密钥，机器人安全设置页面，加签一栏下面显示的SEC开头的字符
     * @param timestamp
     * @return
     */
    private static String getSign(String secret, Long timestamp) {
        try {
            if (!StringUtils.hasText(secret)) {
                return null;
            }
            String stringToSign = timestamp + "\n" + secret;

            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
            String sign = URLEncoder.encode(new String(Base64.getEncoder().encode(signData)), StandardCharsets.UTF_8);
            log.info("【发送钉钉群消息】获取到签名sign = {}", sign);
            return sign;
        } catch (Exception e) {
            log.error("【发送钉钉群消息】计算签名异常", e);
            return null;
        }
    }


}
