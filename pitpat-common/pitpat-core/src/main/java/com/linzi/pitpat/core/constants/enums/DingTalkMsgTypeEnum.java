package com.linzi.pitpat.core.constants.enums;

import com.linzi.pitpat.lang.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;


@Getter
@AllArgsConstructor
public enum DingTalkMsgTypeEnum implements IEnum<String> {
    LINK("link", "link类型"),
    TEXT("text", "text类型"),
    ACTION_CARD("actionCard", "actionCard类型"),
    FEED_CARD("feedCard", "feedCard类型"),
    MARKDOWN("markdown", "markdown类型");

    private final String code;
    private final String name;

    public static final DingTalkMsgTypeEnum[] VALUES = values();

    public static DingTalkMsgTypeEnum get(String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        return Arrays.stream(VALUES).filter(o -> value.equals(o.code)).findFirst().orElse(null);
    }


}
