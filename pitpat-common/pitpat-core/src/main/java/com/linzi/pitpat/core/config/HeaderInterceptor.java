package com.linzi.pitpat.core.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class HeaderInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (Objects.nonNull(attrs)) {
            HttpServletRequest request = attrs.getRequest();
            List<String> headerList = List.of("uuid", "deviceName", "systemVersion", "appType", "zoneId", "ipAddr", "appVersion", "emailAddress", "email", "token", "X-Forwarded-For", "X-Real-IP", "Proxy-Client-IP", "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR");
            headerList.forEach(headerName -> {
                String headerValue = request.getHeader(headerName);
                requestTemplate.header(headerName, headerValue);
                log.debug("headerName:{},headerValue:{}", headerName, headerValue);
            });
        }
    }
}
