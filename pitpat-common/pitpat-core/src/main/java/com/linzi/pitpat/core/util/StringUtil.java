package com.linzi.pitpat.core.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Deque;
import java.util.List;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.IntStream;

/**
 * 字符串工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class StringUtil extends org.apache.commons.lang3.StringUtils {


    public static String combinePrefix(List<? extends Object> list, String propertyName, String prefix, String split) {
        return combine(list, propertyName, prefix, null, split);
    }


    public static String combine(List<? extends Object> list, String propertyName, String split) {
        return combine(list, propertyName, null, null, split);
    }

    public static String combine(List<? extends Object> list, String propertyName, String prefix, String suffix, String split) {
        StringBuffer sb = new StringBuffer();
        try {
            if (list == null && list.size() == 0) {
                return null;
            }
            prefix = isBlank(prefix) ? "" : prefix;
            suffix = isBlank(suffix) ? "" : suffix;
            Field field = list.get(0).getClass().getDeclaredField(propertyName);
            field.setAccessible(true);
            for (int i = 0; i < list.size(); i++) {
                Object o = list.get(i);
                Object x = field.get(o);
                sb.append(prefix).append(x).append(suffix);
                if (i < list.size() - 1) {
                    sb.append(split);
                }
            }
        } catch (Exception e) {
            log.error("异常", e);
        }
        return sb.toString();
    }

    // 判断整数数字的正则表达式
    private static Pattern NUMBER_PATTERN = Pattern.compile("^[-\\+]?[\\d]*$");

    /**
     * 校验是否为数字
     */
    public static boolean checkStrIsNum(String str) {
        Matcher isNum = NUMBER_PATTERN.matcher(str);
        if (!isNum.matches()) {
            return false;
        }
        return true;
    }

    public static boolean startsWithAny(String str, String... prefixes) {
        for (String prefix : prefixes) {
            if (str.startsWith(prefix)) {
                return true;
            }
        }
        return false;
    }

    public static List<String> splitToList(String source, String sep) {
        List<String> result = new ArrayList<String>();
        if (isBlank(source)) {
            return result;
        }
        String[] tempResult = source.split(sep);
        for (String item : tempResult) {
            result.add(item);
        }
        return result;
    }


    public static String generatePassword(int length) {
        if (length < 3) {
            throw new IllegalArgumentException("Password length must be at least 3.");
        }

        AtomicInteger atomicInteger = new AtomicInteger();
        boolean hasUpperCase = false;
        boolean hasLowerCase = false;
        boolean hasDigit = false;
        String password = "";
        while (!hasUpperCase) {
            password = NanoId.randomNanoId();
            for (char ch : password.toCharArray()) {
                if (Character.isUpperCase(ch)) {
                    hasUpperCase = true;
                } else if (Character.isLowerCase(ch)) {
                    hasLowerCase = true;
                } else if (Character.isDigit(ch)) {
                    hasDigit = true;
                }

                // 如果已经满足所有条件，提前退出循环
                if (hasUpperCase && hasLowerCase && hasDigit) {
                    break;
                }
                atomicInteger.incrementAndGet();
            }
            log.info("循环={},password={}", atomicInteger.get(),password);
        }

        return password;
    }

    public static boolean listcontains(String configValue, Object id) {
        if (!StringUtils.hasText(configValue)) {
            return false;
        }
        List<String> list = splitToList(configValue, ",");
        return list.contains(id + "");
    }


    /**
     * 是否含有表情
     *
     * @return
     */
    public static boolean containsEmoji(String source) {
        int len = source.length();
        boolean isEmoji = false;
        for (int i = 0; i < len; i++) {
            char hs = source.charAt(i);
            if (!isEmojiCharacter(hs)) {
                return true;
            }
            if (0xd800 <= hs && hs <= 0xdbff) {
                if (source.length() > 1) {
                    char ls = source.charAt(i + 1);
                    int uc = ((hs - 0xd800) * 0x400) + (ls - 0xdc00) + 0x10000;
                    if (0x1d000 <= uc && uc <= 0x1f77f) {
                        return true;
                    }
                }
            } else {
                // non surrogate
                if (0x2100 <= hs && hs <= 0x27ff && hs != 0x263b) {
                    return true;
                } else if (0x2B05 <= hs && hs <= 0x2b07) {
                    return true;
                } else if (0x2934 <= hs && hs <= 0x2935) {
                    return true;
                } else if (0x3297 <= hs && hs <= 0x3299) {
                    return true;
                } else if (hs == 0xa9 || hs == 0xae || hs == 0x303d || hs == 0x3030 || hs == 0x2b55 || hs == 0x2b1c || hs == 0x2b1b || hs == 0x2b50 || hs == 0x231a) {
                    return true;
                }
                if (!isEmoji && source.length() > 1 && i < source.length() - 1) {
                    char ls = source.charAt(i + 1);
                    if (ls == 0x20e3) {
                        return true;
                    }
                }
            }
        }
        return isEmoji;
    }

    private static boolean isEmojiCharacter(char codePoint) {
        return (codePoint == 0x0) || (codePoint == 0x9) || (codePoint == 0xA) || (codePoint == 0xD) || ((codePoint >= 0x20) && (codePoint <= 0xD7FF)) || ((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) || ((codePoint >= 0x10000) && (codePoint <= 0x10FFFF));
    }

    public static String sub(String value, int end) {
        if (!StringUtils.hasText(value)) {
            return "";
        }
        if (value.length() <= end) {
            return value;
        }
        return value.substring(0, end);
    }

    public static boolean eqList(String compare, String... list) {
        for (String l : list) {
            if (l.equals(compare)) {
                return true;
            }
        }
        return false;
    }


    public static String getEquipmentTypeName(String equipmentType) {
        if (!StringUtils.hasText(equipmentType)) {
            return "";
        }
        String name = equipmentType.replaceAll(",", "、").replace("0", "None").replace("1", "Treadmill").replace("2", "Walking Pad").replace("3", "Yoga Mat")
                //  0：无器械，1：跑步机，2：走步机，3：瑜伽垫, 4 走跑一体机（跑步形态） , 5 走跑一体机（走步形态）
                .replace("4", "2-in-1(Running Mode)").replace("5", "2-in-1(Walking Mode)")
                .replace("6", "Indoor Exercise Bike")
                .replace("7", "Indoor Rowing Machine")
                ;
        return name;
    }


    public static boolean containsList(String compare, String... list) {
        if (!StringUtils.hasText(compare)) {
            return false;
        }
        for (String l : list) {
            if (compare.contains(l)) {
                return true;
            }
        }
        return false;
    }


    public static boolean isABCDSSPlus(String model) {
        if (eqList(model, "A", "B", "C", "D", "S", "S+", "SS", "E1", "E2", "E3", "E4", "E5", "E6", "F1", "F2", "F3", "F4")) {
            return true;
        }
        return false;
    }


    public static void main(String[] args) {
        IntStream.range(0,100000).forEach(i->{
            generatePassword(10);
        });
        System.out.println(StringUtil.containsList("bracelet1", "bracelet", "wristband", "watch"));
    }


    public static String getDotStrByInd(String vRange, int index) {
        String vranges[] = vRange.split(",");
        return vranges[index];
    }


    public static String getRandomStr(String[] vranges) {
        Random random = new Random();
        int index = random.nextInt(vranges.length);
        return vranges[index];
    }

    public static Integer getSwitchInteger(String codeSwitch) {
        if (!StringUtils.hasText(codeSwitch)) {
            return 0;
        }
        return Integer.valueOf(codeSwitch);
    }


    public static String objectToString(Object obj, String defaultValue) {
        try {
            if (obj == null) {
                return defaultValue;
            }
            return obj.toString();
        } catch (Exception e) {

        }
        return defaultValue;
    }


    /**
     * 检查是否存在未闭合的字符串
     *
     * @param content
     * @return
     */
    public static boolean haveUnClosedBrackets(String content) {
        if (!StringUtils.hasText(content)) {
            return false;
        }
        Deque<Character> stack = new ArrayDeque<>();
        for (char c : content.toCharArray()) {
            if (c == '{') {
                stack.push(c);
            } else if (c == '}') {
                if (stack.isEmpty()) {
                    return true;
                }
                stack.pop();
            }
        }
        return !stack.isEmpty();
    }


}
