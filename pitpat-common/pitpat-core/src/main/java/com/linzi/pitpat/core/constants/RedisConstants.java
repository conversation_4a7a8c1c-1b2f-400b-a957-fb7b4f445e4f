package com.linzi.pitpat.core.constants;

/**
 * @description:
 * @author: pan<PERSON>
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
public class RedisConstants {


    public static final long SECOND_OF_FIVE= 1 * 5;

    public static final long SECOND_OF_HALF_MINITS = 1 * 30;
    public static final long SECOND_OF_ONE_MINITS = 1 * 60;
    public static final long SECOND_OF_TWO_MINITS = 2 * 60;
    public static final long SECOND_OF_TEN_MINITS = 10 * 60L;
    public static final long SECOND_OF_THREE = 30L;// 30秒
    public static final long SECOND_OF_HALF_HOUR = 30 * 60;
    public static final long SECOND_OF_AN_HOUR = 60 * 60L;
    public static final long SECOND_OF_ONE_DAY = 24 * 60 * 60L;




    public static final long SECOND_OF_HALF_HOUR_LONG = 30 * 60L;
    public static final int SECOND_OF_AN_HOUR_INT = 60 * 60;

    public static final long SECOND_OF_ONE_WEEK = 7 * 24 * 60 * 60L;
    public static final long SECOND_OF_ONE_MONTH = 30 * 24 * 60 * 60L;

    public static final long MILLISECOND_OF_TWO_HOUR = 2 * 60 * 60 * 1000L;


    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 用户好友关注绑定key
     */
    public static final String USER_FRIEND_ADD_KEY = "user_friend_add_key_";

    public static final String PITPAT_USER_CODE_KEY = "pitpat_user_code_key";

    public static final String REDIS_IM_USER_SIG = "im_user_sig:";

    public static final String RAINMAKER_OAUTH2_KEY = "rainmaker_oauth2_key:";

    public static final String USER_PAY = "user_pay:";

    public static final String USER_REFUND = "user_refund:";

    public static final String USER_PAY_SUCCESS = "user_pay_success:";

    public static final String USER_CASH = "user_cash:";

    public static final String USER_ACCOUNT = "user_account:";

    public static final String SERIES_LEVEL_ENROLL = "series_level_enroll:";

    /**
     * 支付密码错误次数
     */
    public static final String USER_PAY_ERROR_PASSWORD_COUNT = "user_pay_error_password_count_";

    public static final String RUN_DATA_DETAIL_SECOND_KEY = "run_data_detail_second_key:";

    public static final String RUN_DATA_DETAIL_SECOND_LIST_KEY = "run_data_detail_second_list_key:";

    public static final String RUN_DATA_DETAIL_SECOND_DATA_KEY = "run_data_detail_second_data_key:";

    public static final String RUN_ACTIVITY_USER_DATA_DETAILS_ID = "run_activity_user_data_details_id:";

    public static final String RAINMAKER_INFO_KEY = "rainmaker_info_key:";
    public static final String RAINMAKER_TEMPORARY_PASSWORD = "rainmaker_temporary_password_key:";

    public static final String ACTIVITY_END_DATA_KEY = "activity_end_data_user_id:";
    /**
     * 参与活动
     */
    public static final String ACTIVITY_PARTICIPATION_KEY = "activity_participation_key:";

    public static final String GENERATE_ORDER_KEY = "generate_order_key:";

    public static final String ORDER_KEY = "order_key:";

    public static final String GOODS_STOCK_KEY = "goods_stock_key:";
    /**
     * 参与活动
     */
    public static final String MIND_MATCH_KEY = "MIND_MATCH_KEY:";
    /**
     * 已发放奖励
     */
    public static final String AWARDS_ISSUED_KEY = "awards_issued_key:";
    /**
     * 已发放奖励人数
     */
    public static final String AWARDS_ISSUED_COUNT_KEY = "awards_issued_count_key:";
    /**
     * 已发放奖励人数
     */
    public static final String AWARD_ISSUED_FRIEND_RANKING = "award_issued_friend_ranking:";
    /**
     * 已发放奖励人数
     */
    public static final String AWARD_ISSUED_ALL_USER_RANKING = "award_issued_all_user_ranking:";
    public static final String _OFFICIAL_CUMULATIVE_RUN_GOAL_AWARD = "_official_cumulative_run_goal_award:";

    public static final String MAIN_ACTIVITY_COMPLETE_AWARD = "main_activity_complete_award:";

    public static final String SURPLUS_MILEAGE_OFFICIAL_CUMULATIVE_RUN_NOTIFICATION = "surplus_mileage_official_cumulative_notification:";

    public static final String ACTIVITY_ID = "activity_id:";

    /**
     * 参与活动
     */
    public static final String MEDAL_EXP_HANDLER = "MEDAL_EXP_HANDLER:";
    public static final String RUN_DATA_DETAILS_END_STATUS = "run_data_details_end_status:";




    /**
     * 参与活动
     */
    public static final String MIND_MATCH_GET_USER_ID = "MIND_MATCH_GET_USER_ID_";

    /**
     * 被限制的机器人名单
     */
    public static final String ROT_LIMIT_JOIN_ACTIVITY = "ROT_LIMIT_JOIN_ACTIVITY_";



    /**
     * 参与活动
     */
    public static final String robot_online_lock_key = "robot_online_lock_key:";

    /**
     * 参与活动
     */
    public static final String RUN_ONLINE_NUMBER = "run_online_number";
    public static final String USER_ACCOUNT_ADD = "user_account_add:";

    public static final String USER_ACCOUNT_3_PUSH = "user_account_3_push:";

    public static final String EXCHANGE_COUPON = "exchange_coupon:";

    public static final String IMPORT_LABEL_USER = "import_label_user:";

    public static final String USE_COUPON = "use_coupon:";
    public static final String USE_COUPON_PRE = "use_coupon_pre:";

    public static final String USER_CURRENT_ACTIVITY_ID = "user_current_activity_id:";

    public static final String CLAIM_COUPON_ID = "claim_coupon_id:";

    public static final String INVITE_FRIEND_HELP_ID = "invite_friend_help_id:";

    public static final String HELPER_ID = "helper_id:";

    public static final String ASSIST_ACTIVITY_ID = "assist_activity_id:";

    public static final String ASSIST_ACTIVITY_INITIATOR_ID = "assist_activity_initiator_id:";

    public static final String RUN_ACTIVITY_ID = "run_activity_id:";


    public static final String CLAIM_COUPON_USER_ID = "claim_coupon_user_id:";

    public static final String  START_TASK_USER_ID = "start_task_user_id:";

    public static final String NEW_PERSON_TASK_ID = "new_person_task_id:";

    public static final String LOCK_ACTIVITY_TEAM_CREATE = "L_activity_team_create:";

    public static final String FEEDBACK_USER_ID = "feedback_user_id:";

    public static final String CLAIM_COUPON_ACTIVITY_ID = "claim_coupon_activity_id:";
    /**
     * 参与活动
     */
    public static final String OPERATION_ACTIVITY_PARTICIPATION_KEY = "operation_activity_participation_key:";

    public static final String AWARD_RECEIVED = "award_received:";

    public static final String FESTIVAL_HAS_NEW_AWARD_KEU = "FESTIVAL_HAS_NEW_AWARD_KEU:";

    /**
     * 活动弹窗标识
     */
    public static final String ACTIVITY_POP_FLAG = "activity_pop_flag:";

    public static final String SAME_COURSE_USER_LISTS_FLAG = "same_course_user_lists_flag:";

    public static final String USER_GUIDE_PAGE_ROUTE_POP = "user_guide_page_route_pop:";

    public static final String USER_GUIDE_PAGE_ACTIVITY_POP = "user_guide_page_activity_pop:";

    /**
     * 社区内容已给mq发送标识
     */
    public static final String COMMUNITY_CONTENT_PUBLISHED = "community_content_published:";
    public static final String NEW_USER_OFFLINE_PK_JOIN = "new_user_offline_pk_join:";
    /**
     * 用户路线跑步记录
     */
    public static final String USER_RUN_ROUTE = "user_run_route:";
    //用户关注站内信提醒
    public static final String USER_ATTENTION_REMIND = "user_attention_remind:";
    public static final String USER_H5_POP_HALF_FLAG_DAY = "user_h5_pop_half_flag_day:";
    public static final String USER_H5_POP_HALF_FLAG_WEEK = "user_h5_pop_half_flag_week:";
    public static final String USER_H5_POP_HALF_FLAG_VERSION = "user_h5_pop_half_flag_version:";

    //奖励中心缓存
    public static final String AWARD_CENTER = "award_center:";

    //团队赛队伍数据缓存
    public static final String TEAM_ACTIVITY_TEAM_INFO = "team_activity_team_info:";
    public static final String USER_FREEZE_HOMEPAGE_POP = "user_freeze_homepage_pop:";

    //关注用户的机器人id缓存
    public static final String FOLLOW_USER_ROBOT_ID = "follow_user_robot_id:";

    //关注可用机器人锁
    public static final String FOLLOW_CAN_USE_ROBOT = "follow_can_use_robot";

    //活动剩余机器人缓存
    public static final String ACTIVE_REMAINING_ROBOT_CACHE = "active_remaining_robot_cache:";
    public static final String BATTLE_PASS_CUMULATIVE_RUN_ACTIVITY = "battle_pass_cumulative_run_activity_mil:";

    public static final String USER_TASK_RUN_FINISHED_POP_URL_FLAG = "user_task_run_finished_pop_url_flag:";

    //用户跑步活动数据处理标识
    public static final String USER_RUN_ACTIVITY_DETAIL_PROCESS = "user_run_activity_detail_process:";

    /**
     * 活动房间标识
     */
    public static final String ACTIVITY_ROOM_FLAG = "activity_room_flag:";

    public static final String USER_REPORT_POP = "user_report_pop:";

    /**
     * 电控版新增/修改
     */
    public static final String CONTROL_PANEL_BATCH_ADD_OR_UPDATE = "control_panel_batch_add_or_update:";

    /**
     * 传感器参数新增/修改
     */
    public static final String SENSOR_ADD_OR_UPDATE = "sensor_add_or_update:";

    // 积分兑换规则
    public static final String EXCHANGE_SCORE_RULE = "exchange_score_rule:";

    //热门课程缓存
    public static final String HOT_COURSE_LIST = "hot_course_list:";

    //拉伸课程缓存
    public static final String STRETCH_COURSE_LIST = "stretch_course_list:";

    //有氧课程缓存
    public static final String AEROBIC_COURSE_LIST = "aerobic_course_list:";

    //力量课程缓存
    public static final String MUSCLE_COURSE_LIST = "muscle_course_list:";

    //不同国家最大用户id缓存
    public static final String COUNTRY_MAX_USER_ID = "country_max_user_id:";

    //离线pk排行榜缓存
    public static final String OFFLINE_PK_RANK = "prod:offline_pk_rank:";


    //活动获取服装奖励
    public static final String WEAR_AWARD_OBTAIN = "wear_award_obtain";
    //活动服装奖励缓存集合
    public static final String ACTIVITY_WEAR_AWARD_CACHE_LIST = "prod:activity_wear_award_cache_list";

    //活动获取服装奖励
    public static final String OFFLINE_START_PK = "offline_start_pk:";

    //活动获取服装奖励
    public static final String HARDWARE_RUN_PUSH = "hardware_run_push:";
    //用户换服装
    public static final String USER_CHANGE_WEAR = "user_change_wear";

    //加拿大临时支付劵
    public static final String CANADA_TEMP_COUPONS = "canada_temp_coupons";

    //加拿大临时支付劵
    public static final String USER_RUN_DATA_PROCESS = "user_run_data_process:";

    //跑步路线数量统计
    public static final String RUN_ROUTE_COUNT = "run_route_count:";
    //跑步结束新活动处理
    public static final String RUN_DATA_END_ACTIVITY_RESULT = "run_data_end_activity_result:";
    //活动结束新活动处理
    public static final String ACTIVITY_DATA_END_ACTIVITY_RESULT = "activity_data_end_activity_result:";
    //段位赛活动处理
    public static final String RANKED_ACTIVITY_DATA_END_ACTIVITY_RESULT = "ranked_activity_data_end_activity_result:";
    //段位赛用户结束活动
    public static final String RANKED_ACTIVITY_FINISHED_RESULT = "ranked_activity_data_finished_result:";

    public static final String RANKED_LEVEL_RESULT="ranked_level_result:";

    public static final String PROP_RANKED_LEVEL_RESULT="prop:ranked_level_result:";

    /**
     * 段位赛匹配
     */
    public static final String RANKED_ACTIVITY_MATCH_KEY = "ranked_activity_match_key:";
    public static final String RANKED_ACTIVITY_MATCH_USERS_KEY = "ranked_activity_match_users_key:";
    public static final String RANKED_ACTIVITY_MATCH_CHECK_KEY = "ranked_activity_match_check_key:";

    /**
     * 道具赛匹配
     */
    public static final String PROP_RANKED_ACTIVITY_MATCH_KEY = "prop_ranked_activity_match_key:";
    public static final String PROP_RANKED_ACTIVITY_MATCH_USERS_KEY = "prop_ranked_activity_match_users_key:";
    public static final String PROP_RANKED_ACTIVITY_MATCH_CHECK_KEY = "prop_ranked_activity_match_check_key:";

    /**
     * 聚合任务执行标识
     */
    public static final String AGGREGATION_TASK_EXECUTION = "aggregation_task_execution:";
    public static final String AGGREGATION_TASK_EXECUTION_FIRST = "aggregation_task_execution_first:";

    public static final String CERTIFICATE_EXECUTION = "CERTIFICATE_EXECUTION:";
    public static final String USERMEDAL_EXECUTION = "USERMEDAL_EXECUTION:";
    public static final String USERAWARD_SEND = "USERAWARD_SEND:";

    public static final String UPDATE_PIC_RANDOM = "UPDATE_PIC_RANDOM";

    public static final String UPDATE_PIC_RANDOM_RUNNING = "UPDATE_PIC_RANDOM_RUNNING";
    public static final String NEW_PERSON_ACTIVITY_CONFIG_LEVEL_MAP = "new_person_activity_config_level_map:";
    public static final String FIRST_PAYMENT_TIME="firstPaymentTime:";

    public static final String ZNS_USER_ID_KEY = "ZNS_USER_ID_KEY";
    public static final String TEAM_GRADE_CALCULATE="team_grade_calculate:";

    public static final String ENROLL_TEAM="enroll_team:";

    public static final String AWS_SES_SEND_QUOTA="aws_ses_send_quota";
    //手动发放奖励
    public static final String MANUAL_SEND_AWARD="manual_send_award";

    public static final String USER_NO_FINISH_ACTIVITY ="user_no_finish_activity:";
    public static final String ROT_COUNTRY_MODE_KEY = "ROT_COUNTRY_MODE_KEY:";
    public static final String ROT_COUNTRY_MODE_QUEUE = "prod:ROT_COUNTRY_MODE_QUEUE:";
    public static final String ROT_QUEUE_CONFIG = "prod:ROT_QUEUE_CONFIG:";
    public static final String ROT_QUEUE_POLL_PUSH = "ROT_QUEUE_POLL_PUSH:";

    public static final String ROT_QUEUE_ALL_COUNTRY = "ROT_QUEUE_ALL_COUNTRY:";
    public static final String GROUP_IMPORT_DEVICE="group_import_device:";

    public static final String ACTIVITY_COUNTRY = "ACTIVITY_COUNTRY:";
    //会员批量修改失败结果
    public static final String VIP_BATCH_UPDATE_TIME = "vip_batch_update_time";
    //会员批量导入失败结果
    public static final String VIP_BATCH_IMPORT= "vip_batch_import";
    public static final String MIGRATE_ROBOT_DATA = "MIGRATE_ROBOT_DATA";

    public static final String TEAM_ACTIVITY_ROOM_MAP = "TEAM_ACTIVITY_ROOM_MAP:";
    public static final String LOCK_CLUB_MEMBER_CHANGE = "LOCK_CLUB_MEMBER_CHANGE:";
    public static final String CANCEL_BATTLE_PASS = "CANCEL_BATTLE_PASS";

    public static String SHARE_SUCCESS_CALLBACK = "SHARE_SUCCESS_CALLBACK:";

    /**
     * 升级弹框标记
     */
    public static final String ACCOUNT_UPGRADE_POP = "account:upgrade:pop:";
    public static String USER_FRIEND_DATA = "USER_FRIEND_DATA:";

    //开启H5活动包操作
    public static final String ACTIVITY_PACK_OPEN="activity_pack_open";

    public static final String ACTIVITY_PACK_OPEN_NEW="activity_pack_open_new";
    //是否已经开启H5活动包
    public static final String ACTIVITY_PACK_OPEN_TURE="activity_pack_open_true";
    public static final String ACTIVITY_PACK_OPEN_TRUE_NEW = "activity_pack_open_true_new";

    public static String OLD_ACTIVITY_START = "OLD_ACTIVITY_START:";
    //活动包是否弹窗过
    public static final String ACTIVITY_PACK_OPEN_POP="activity_pack_open_pop";
    public static final String ACTIVITY_PACK_OPEN_POP_JULY="activity_pack_open_pop_july";

    //活动结束奖励人工审核下发
    public static final String ACTIVITY_SEND_AWARD_REVIEW_ACTIVITY_LOCK = "activity_send_award_review_activity_lock:";
    public static final String ACTIVITY_SEND_AWARD_REVIEW_ACTIVITY = "activity_send_award_review_activity:";

    public static final String OFFLINE_PK_USER_TIMES = "offline_pk_user_times:";

    public static final String PAYPAL_SUBSCRIPTION_LOCK = "PAYPAL_SUBSCRIPTION_LOCK:";


    public static final String APP_ROOM_NUMBER = "app_room_number:";

    public static final String APP_ROOM_MAP = "app_room_map:";
    public static final String APP_ROOM_MEMBER_MAP = "app_room_member_map:";
    // 用户准备标志
    public static final String APP_ROOM_MEMBER_STATUS = "app_room_member_status:";

    public static final String APP_ROOM_SINGLE_SET = "app_room_single_set:";
    // 用户所在房间号标志记录
    public static final String APP_USER_ROOM = "app_user_room:";
    public static final String APP_ROOM_USER_PASS = "app_room_user_pass:";
    public static final String APP_ROOM_ONLINE_USERS = "app_room_online_users:";
    public static final String APP_NPC_ROOM="app_npc_room";

    public static final String VIP_EXPIRE_REMIND = "vip_expire_remind";
    public static final String PROP_RANK_CAL = "PROP_RANK_CAL:";

    public static final String LOCK_ROOM_ACTIVITY_START = "LOCK_ROOM_ACTIVITY_START:";
    public static final String LOCK_ROOM_TICKET_USE = "LOCK_ROOM_TICKET_USE:";
    public static final String LOCK_ROOM_TICKET_REFUND = "LOCK_ROOM_TICKET_REFUND:";

    // 标签打标计数器key
    public static final String LABEL_COUNTER_PREFIX = "label_counter_prefix:";
    //打标记录更新锁key
    public static final String LABEL_RECORD_UPDATE = "label_record_update:";
    //打标记录失败标记
    public static final String LABEL_RECORD_FAIL_FLAG = "label_record_fail_flag:";

    public static final String LOCK_COMPANION_USER_ACCOUNT = "LOCK_COMPANION_USER_ACCOUNT:";
    public static final String PAYPAL_CHECK_REFUND = "PAYPAL_CHECK_REFUND:";

    public static final String USER_COMPETITION_FLAG = "user_competition_flag:";
    public static final String USER_COMPETITION_POP = "user_competition_pop:";
    public static final String USER_COMPETITION_ACTIVITY_FLAG = "user_competition_activity_flag:";

    public static final String TURBOLINK_SEND_AWARD = "TURBOLINK_SEND_AWARD:";

    public static final String BATTLE_SETTLE = "BATTLE_SETTLE:";

    public static final String JOIN_CAMPAIGN = "JOIN_CAMPAIGN:";

    public static final String STAGE_ACTIVITY_ENTER = "STAGE_ACTIVITY_ENTER:";

    public static final String STAGE_ACTIVITY_RERANK = "STAGE_ACTIVITY_RERANK:";

    public static final String STAGE_ACTIVITY_STAGE_RERANK = "STAGE_ACTIVITY_STAGE_RERANK:";

    public static final String  USER_RUN_DATA_COUNT = "user_run_data:count:";






}
