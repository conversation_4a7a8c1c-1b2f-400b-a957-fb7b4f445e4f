package com.linzi.pitpat.core.util;

import lombok.extern.slf4j.Slf4j;

import java.time.DayOfWeek;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAccessor;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.Objects;

/**
 * JDK1.8  日期工具类
 *
 * <AUTHOR>
 * @date 2023年11月27日 13:41:22
 */
@Slf4j
public class ZonedDateTimeUtil {

    /**
     * 年-月-日 时:分:秒 标准样式
     */
    public final static String PATTERN_DATETIME = "yyyy-MM-dd HH:mm:ss";

    /**
     * 年月日时
     */
    public final static String PATTERN_DATETIME_DATE_HOUR = "yyyyMMddHH";

    /**
     * 年月日
     */
    public final static String PATTERN_DATETIME_DATE_DAY = "MM.dd.yyyy";

    public static final String  PATTERN_DATE = "yyyyMMdd";

    public static String format(TemporalAccessor temporalAccessor, String pattern) {
        if (temporalAccessor != null) {
            return DateTimeFormatter.ofPattern(pattern).format(temporalAccessor);
        }
        return null;
    }

    /**
     * 判断当前时间是否满足本地时间
     *
     * @param startTime  本地开始时间
     * @param endTime    本地结束时间
     * @param zoneOffset 用户时区偏移量
     * @return
     */
    public static boolean betweenNowLocalDate(Date startTime, Date endTime, Integer zoneOffset) {
        ZoneId zone = ZoneOffset.ofOffset("UTC", ZoneOffset.ofHours(zoneOffset));
        return betweenLocalDate(startTime, endTime, new Date(), zone);
    }


    /**
     * 判断时间是否满足本地时间区间
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param targetTime 指定时间
     * @param zone       时区
     * @return
     */
    private static boolean betweenLocalDate(Date startTime, Date endTime, Date targetTime, ZoneId zone) {
        //检验时间
        if (startTime == null && endTime == null) {
            return false;
        }
        if (targetTime == null) {
            return false;
        }
        if (zone == null) {
            zone = ZoneOffset.ofOffset("UTC", ZoneOffset.UTC);
        }

        //时间转字符串
        String localStartTimeStr = "1970-01-01 00:00:00";
        String localEndTimeStr = "2099-12-31 59:59:59";
        if (startTime != null) {
            Date localStartTime = DateUtil.addHours(startTime, 8); //本地开始时间 (0时区+8就是运营设置的时间，就是本地时间)
            localStartTimeStr = DateUtil.formateDateStr(localStartTime, PATTERN_DATETIME); //转字符串 2023-12-13 08:00:00
        }
        if (endTime != null) {
            Date localEndTime = DateUtil.addHours(endTime, 8); //本地结束时间 (0时区+8就是运营设置的时间，就是本地时间)
            localEndTimeStr = DateUtil.formateDateStr(localEndTime, PATTERN_DATETIME); //转字符串 2023-12-13 23:59:59
        }
        String targetTimeStr = DateUtil.formateDateStr(targetTime, PATTERN_DATETIME); //转字符串

        //将开始时间、结束时间、目标时间转换为用户所在时区的时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(PATTERN_DATETIME);
        ZonedDateTime userZoneActivityStartTime = ZonedDateTime.parse(localStartTimeStr, formatter.withZone(zone));
        ZonedDateTime userZoneActivityEndTime = ZonedDateTime.parse(localEndTimeStr, formatter.withZone(zone));
        ZonedDateTime userZoneTargetTime = ZonedDateTime.parse(targetTimeStr, formatter.withZone(ZoneId.systemDefault())).withZoneSameInstant(zone);

        //ZonedDateTime userZoneActivityStartTime = localStart.atZone(zone);
        //ZonedDateTime userZoneActivityEndTime = localEnd.atZone(zone);
        //ZonedDateTime userZoneTargetTime = localTarget.atZone(ZoneId.systemDefault()).withZoneSameInstant(zone);

        //判断指定时间是否在活动时间范围内
        boolean b = userZoneTargetTime.isAfter(userZoneActivityStartTime) && userZoneTargetTime.isBefore(userZoneActivityEndTime);
        log.info("ZonedDateTimeUtil#betweenLocalDate-----判断是否满足本地时间,结果：{},zone:{}," +
                        "targetTimeStr:{}," +
                        "startTimeStr:{}," +
                        "endTimeStr:{}," +
                        "targetTime:{}," +
                        "startTime:{}," +
                        "endTime:{}",
                b, zone, targetTimeStr, localStartTimeStr, localEndTimeStr, userZoneTargetTime, userZoneActivityStartTime, userZoneActivityEndTime);
        return b;
    }

    /**
     * 本日的开始时间
     *
     * @param time
     * @return
     */
    public static ZonedDateTime startOfDay(ZonedDateTime time) {
        return time.with(LocalTime.MIN);
    }

    /**
     * 本日的开始时间
     *
     * @param time
     * @return
     */
    public static ZonedDateTime endOfDay(ZonedDateTime time) {
        return time = time.with(LocalTime.MAX);
    }


    /**
     * 获取当天开始时间
     *
     * @return ZonedDateTime
     */
    public static ZonedDateTime startOfDay() {
        return ZonedDateTime.now().with(LocalTime.MIN);
    }

    /**
     * 获取当天结束时间
     *
     * @return ZonedDateTime
     */
    public static ZonedDateTime endOfDay() {
        return ZonedDateTime.now().with(LocalTime.MAX);
    }


    /**
     * 获取周开始时间(周一)
     *
     * @return
     */
    public static ZonedDateTime startOfWeek() {
        return ZonedDateTime.now().with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).with(LocalTime.MIN);
    }

    /**
     * 获取周开始结束时间(周日)
     *
     * @return
     */
    public static ZonedDateTime endOfWeek() {
        return ZonedDateTime.now().with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY)).with(LocalTime.MAX);
    }

    /**
     * 本月的开始时间
     *
     * @param time
     * @return
     */
    public static ZonedDateTime startOfMonth(ZonedDateTime time) {
        return time.with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN);
    }

    /**
     * 本月的结束时间
     *
     * @param time
     * @return
     */
    public static ZonedDateTime endOfMonth(ZonedDateTime time) {
        return time.with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);
    }


    /**
     * 获取某月的开始时间
     *
     * @return 月开始的时间
     */
    public static ZonedDateTime startOfMonth() {
        return startOfMonth(ZonedDateTime.now());
    }


    /**
     * 获取某月的结束时间
     *
     * @return 月开始的时间
     */
    public static ZonedDateTime endOfMonth() {
        return endOfMonth(ZonedDateTime.now());
    }

    /**
     * 本月的开始结束时间
     *
     * @param time
     * @return
     */
    public static ZonedDateTime[] rangeOfMonth(ZonedDateTime time) {
        ZonedDateTime[] range = new ZonedDateTime[2];
        range[0] = startOfMonth(time);
        range[1] = endOfMonth(time);
        return range;
    }

    public static ZonedDateTime convertFrom(Date date) {
        if (date == null) {
            return null;
        }

        // 将 Instant 转换为 ZonedDateTime
        return ZonedDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    public static  Date convertFrom( ZonedDateTime zonedDate) {
        if (zonedDate == null) {
            return null;
        }
        Instant instant = zonedDate.toInstant();
        // 将 Instant 转换为 Date
        return Date.from(instant);
    }

    /**
     * 将数据库中0时区的时间，转化为用户时区对应的时刻，在得到当前0时区的时间是
     * 例如处理 活动时间中的跟随用户时间
     * @param zonedDateTime
     * @return
     */

    public static ZonedDateTime convertToUserZoneThenZoneZero(ZonedDateTime zonedDateTime,ZoneId zoneId){
        ZonedDateTime userZoneTime = zonedDateTime.withZoneSameInstant(zoneId);
        // 然后转换到UTC时区(保持同一时刻)
        return userZoneTime.withZoneSameInstant(ZoneId.systemDefault());
    }

    public static int betweenDay(ZonedDateTime start, ZonedDateTime end) {
        if (Objects.isNull(start) || Objects.isNull(end)) {
            return 0;
        }
        long between = ChronoUnit.SECONDS.between(start, end);
        return (int) (between / (60 * 60 * 24));
    }

    public static long calculateRemainingSecondsInDay(ZonedDateTime dateTime) {
        ZonedDateTime endOfDay = dateTime.toLocalDate()
                .atStartOfDay(dateTime.getZone())
                .plusDays(1);
        return Duration.between(dateTime, endOfDay).getSeconds();
    }



}
