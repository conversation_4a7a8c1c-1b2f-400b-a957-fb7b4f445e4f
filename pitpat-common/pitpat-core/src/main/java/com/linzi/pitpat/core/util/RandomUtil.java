package com.linzi.pitpat.core.util;

import java.util.concurrent.ThreadLocalRandom;

public class RandomUtil {

    private static ThreadLocalRandom getRandom() {
        return ThreadLocalRandom.current();
    }

    public static int randomInt(int limit) {
        return getRandom().nextInt(limit);
    }

    public static int randomInt(int min, int max) {
        return getRandom().nextInt(min, max);
    }

    public static long randomLong(long x, long y) {
        long num = -1;
        //说明：两个数在合法范围内，并不限制输入的数哪个更大一些
        if (x < 0 || y < 0) {
            return num;
        } else {
            long max = Math.max(x, y);
            long min = Math.min(x, y);
            long mid = max - min;//求差
            //产生随机数
            num = (long) (Math.random() * (mid + 1)) + min;
        }
        return num;
    }

}
