package com.linzi.pitpat.core.util;


import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;

/**
 * 获取IP方法
 *
 * <AUTHOR>
 */
@Slf4j
public class IpUtil {

    /**
     * X-Forwarded-For: Standard header used by proxies to forward the client's IP address. This can contain multiple IP addresses in a comma-separated list. X-Real-IP: Another
     * header commonly used to forward the client's IP address. Proxy-Client-IP: Used by some proxies to indicate the client's IP address. WL-Proxy-Client-IP: Similar to
     * Proxy-Client-IP, used by certain WebLogic proxies. HTTP_CLIENT_IP: Sometimes used by proxies to pass the client's IP address. HTTP_X_FORWARDED_FOR: Similar to
     * X-Forwarded-For, used by some proxies.
     */
    private static final List<String> IP_HEADERS = List.of(
            "X-Forwarded-For",
            "X-Real-IP",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR");

    public static String getRemoteIp(HttpServletRequest request) {
        if (request == null) {
            return null;
        }
        for (String header : IP_HEADERS) {
            String rawIp = request.getHeader(header);
            if (StringUtils.hasText(rawIp)) {
                if (log.isDebugEnabled()) {
                    log.debug("header={},rawIp={}", header, rawIp);
                }
                return getFirstRealIp(rawIp);
            }
        }
        String remoteIp = request.getRemoteAddr();
        return "0:0:0:0:0:0:0:1".equals(remoteIp) ? "127.0.0.1" : remoteIp;
    }

    private static String getFirstRealIp(String ipList) {
        if (StringUtils.hasText(ipList) && !"unknown".equalsIgnoreCase(ipList)) {
            String[] ips = ipList.split(",");
            for (String ip : ips) {
                if (!"unknown".equalsIgnoreCase(ip)) {
                    if (log.isDebugEnabled()) {
                        log.debug("realIp={}", ip);
                    }
                    return ip;
                }
            }
        }
        return null;
    }

    public static String getHostIp() {
        try {
            return InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
        }
        return "127.0.0.1";
    }

    public static String getHostName() {
        try {
            return InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException e) {
        }
        return "未知";
    }
}
