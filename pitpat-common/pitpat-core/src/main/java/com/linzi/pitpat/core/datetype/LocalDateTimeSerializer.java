package com.linzi.pitpat.core.datetype;


import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
public class LocalDateTimeSerializer extends JsonSerializer<LocalDateTime> {

    private static final long serialVersionUID = 1L;
    private final DateTimeFormatter formatter;
    private final Boolean enableTimestamp;

    public LocalDateTimeSerializer(DateTimeFormatter formatter) {
        this(formatter, false);
    }

    public LocalDateTimeSerializer(DateTimeFormatter formatter, Boolean enableTimestamp) {
        this.formatter = formatter;
        this.enableTimestamp = enableTimestamp;
    }

    @Override
    public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        if(enableTimestamp){
            ZonedDateTime utcTime = value.atZone(ZoneId.of("UTC")); // 将 LocalDateTime 转换为 UTC 时区的 ZonedDateTime
            //ZonedDateTime shanghaiTime = utcTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai")); // 转换为上海时区
            //结果输出一致
            //log.info("utcTime={}", utcTime.toInstant().toEpochMilli());
            //log.info("shanghaiTime={}", shanghaiTime.toInstant().toEpochMilli());
            gen.writeNumber(utcTime.toInstant().toEpochMilli());
        }else {
            ZonedDateTime utcTime = value.atZone(ZoneId.of("UTC")); // 将 LocalDateTime 转换为 UTC 时区的 ZonedDateTime
            ZonedDateTime shanghaiTime = utcTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai")); // 转换为上海时区
            gen.writeString(shanghaiTime.format(formatter));
        }
    }
}
