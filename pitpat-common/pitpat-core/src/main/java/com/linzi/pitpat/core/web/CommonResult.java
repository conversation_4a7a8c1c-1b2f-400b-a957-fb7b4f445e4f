package com.linzi.pitpat.core.web;

import com.linzi.pitpat.lang.Err;
import com.linzi.pitpat.lang.Result;

/**
 * Result 的包装类，提供常用的 success, fail 等 api
 */
public class CommonResult<T> extends Result<T> {

    /**
     * 构造器
     *
     * @param code
     * @param msg
     */
    private CommonResult(Integer code, String msg) {
        super(code , msg);
    }

    /**
     * 构造器
     *
     * @param code
     * @param msg
     * @param data
     */
    private CommonResult(Integer code, String msg, T data) {
        super(code , msg, data);
    }

    /**
     * 构造器
     *
     * @param err
     * @param data
     */
    private CommonResult(Err err, T data) {
        super(err.getCode() , err.getMsg(), data);
    }

    /**
     * 构造器
     *
     * @param err
     */
    private CommonResult(Err err) {
        super(err.getCode() , err.getMsg());
    }

    /**
     * fail 静态方法
     *
     * @param msg 错误消息
     * @param <T> 泛型
     * @return CommonResult<T>
     */
    public static <T> Result<T> fail(String msg) {
        return fail(CommonError.BUSINESS_ERROR.getCode(), msg);
    }

    /**
     * fail 静态方法
     *
     * @param err 错误接口
     * @param <T> 泛型
     * @return CommonResult<T>
     */
    public static <T> Result<T> fail(Err err) {
        return fail(err.getCode(), err.getMsg());
    }

    /**
     * fail 静态方法
     *
     * @param code 错误代码
     * @param msg  错误消息
     * @param <T>  泛型
     * @return CommonResult<T>
     */
    public static <T> Result<T> fail(Integer code, String msg) {
        return new CommonResult<>(code, msg);
    }

    /**
     * ？为什么需要这个接口
     *
     * @param code
     * @param msg
     * @param <T>
     * @return
     */
    ///TODO 单元测试 "/couponExchange")
    public static <T> Result<T> fail(Integer code, String msg, Object data) {
        return new CommonResult<>(code, msg, (T)data);
    }

    /**
     * success 静态方法
     *
     * @param <T> 泛型
     * @return CommonResult<T>
     */
    public static <T> Result<T> fail(T data) {
        return new CommonResult<T>(CommonError.BUSINESS_ERROR, data);
    }
    /**
     * success 静态方法
     *
     * @param <T> 泛型
     * @return CommonResult<T>
     */
    public static <T> Result<T> success() {
        return new CommonResult<T>(CommonError.SUCCESS);
    }

    /**
     * success 静态方法
     *
     * @param data 返回结果
     * @param <T>  泛型
     * @return CommonResult<T>
     */
    public static <T> Result<T> success(T data) {
        return new CommonResult<T>(CommonError.SUCCESS, data);
    }

}
