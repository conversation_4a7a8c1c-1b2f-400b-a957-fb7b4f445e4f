package com.linzi.pitpat.core.util;

import com.linzi.pitpat.core.util.dto.West8StartEndDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.ParseException;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * @description: 数字类型工具类
 * @author: panhao
 * @version: V1.0
 * @date: 2019-10-24 17:20
 **/
@Slf4j
public class NumberUtils {

    /**
     * 功能描述:
     * 〈string数组转long集合〉
     * @param arr
     * @return
     */
   public static  List<Long> stringToLong(String[] arr) {
        return Arrays.stream(arr).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
    }

    /**
     * 功能描述:
     * 〈string数组转long集合〉
     * @param arr
     * @return
     */
    public static  List<Integer> stringToInt(String[] arr) {
        return Arrays.stream(arr).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList());
    }

    /**
     * 功能描述:
     * 〈string数组转Double集合〉
     * @param arr
     * @return
     */
   public static  List<Double> stringToDouble(String[] arr) {
        return Arrays.stream(arr).mapToDouble(Double::parseDouble).boxed().collect(Collectors.toList());
    }
    /**
     * 功能描述:
     * 〈判断是否是一个数字类型〉
     *
     * @params : [str]
     * @return : boolean
     * <AUTHOR> cwl
     * @date : 2019/6/6 17:59
     */
    public static boolean isNumber(String str) {
        if(str == null){
            return  false;
        }
        Number answer = null;
        try {
            answer = NumberFormat.getInstance().parse(str);
        } catch (ParseException ignored) {
        }
        return Objects.nonNull(answer);
    }

   public static  BigDecimal getRandomStep(String vrange, BigDecimal step,int scale ) {
        step =  BigDecimalUtil.getScale(step,scale);
        String vs [] = vrange.split("~");
        BigDecimal max = MapUtil.getBigDecimal(vs[1],new BigDecimal(0));
        BigDecimal min = MapUtil.getBigDecimal(vs[0],new BigDecimal(0));
        List<BigDecimal> lst = new ArrayList<>();
        for(BigDecimal i = min ;i .compareTo(max) <= 0 ;i  = i.add(step)){
            i = BigDecimalUtil.getScale(i,scale);
            lst.add(i);
        }
        Random random = new Random();
        return lst.get(random.nextInt(lst.size()) );
    }

    public static  List<BigDecimal> getRandomStepList(BigDecimal target,String vrange, BigDecimal step,int scale ) {
        for(;;){
            BigDecimal sum = BigDecimal.ZERO;
            List<BigDecimal> bigDecimalList = new ArrayList<>();
            for (int i = 0; sum.compareTo(target) <= 0; i++) {
                BigDecimal randomTarget = NumberUtils.getRandomStep(vrange,step, scale);
                BigDecimal oldSum = sum ;
                sum = sum.add(randomTarget);
                if(sum.compareTo(target) >=0){
                    randomTarget = target.subtract(oldSum);
                }
                bigDecimalList.add(randomTarget);
            }
            log.info("getRandomStepList 本次随机距离是 " + bigDecimalList);
            if(bigDecimalList.get(bigDecimalList.size()-1).compareTo(new BigDecimal(100)) > 0 ){
                return bigDecimalList;
            }
        }

    }

    /**
     *  根据随机计划的边界和步长生成随机速度如 [5,12,0.5] -> [5, 5.5, 6, 6.6, 7, 7.5,...,11, 11.5 ,12],然后随机取一个值
     * @param randomPlain [5,12,0.5]
     * @return 随机值
     */
    public static  BigDecimal getConfigRandom(String randomPlain ){
        String[] configs = randomPlain.split(",");
        List<BigDecimal> bigDecimals = new ArrayList<>();
        for(BigDecimal i = MapUtil.getBigDecimal(configs[0],BigDecimal.ZERO);
                i.compareTo( MapUtil.getBigDecimal(configs[1],BigDecimal.ZERO)) <=0;
                i = i .add(MapUtil.getBigDecimal(configs[2],BigDecimal.ZERO))){
            bigDecimals.add(i);
        }
        Random random = new Random();
        int r = random.nextInt(bigDecimals.size());
        return bigDecimals.get(r);
    }

    public static  Integer getRandomInt(Integer min, Integer max, Integer step) {
        if(max == null || min == null || step == null){
            return 0 ;
        }
        if(min.equals(max)){
            return max;
        }
        List<Integer> list = new ArrayList<>();
        for (int i = min; i < max; i = i + step) {
            list.add(i);
        }
        Random random = new Random();
        return list.get(random.nextInt(list.size()));
    }

   public static  Date objToDate(Long date ) {
        if(date == null){
            return new Date();
        }
        return new Date(date);
    }

    /**
     * 获取目标im房间号
     * @return
     */
   public static  Long getGoalImNumber(Long activityId , Integer runningGoal, Integer completeRuleType) {
        log.info("getGoalImNumber activityId = " + activityId +  ",runningGoal="+runningGoal + ",completeRuleType="+completeRuleType);
        if (Objects.isNull(runningGoal)) {
            return activityId;
        }
        if (runningGoal < 100) {
            Long roomId = Long.valueOf(activityId+""+runningGoal);
            return (long)handleOverflowWithBitMask(activityId,roomId);
        }
        if (Objects.isNull(completeRuleType) || completeRuleType == 1) {
            runningGoal = runningGoal/100;
        }else {
            runningGoal = runningGoal/60;
        }
       Long roomId = Long.valueOf(activityId + "" + runningGoal);
       return (long)handleOverflowWithBitMask(activityId,roomId);
    }

    /**
     * 截取房间id，防止房间号超过Integer 最大值(安卓用int接收参数，超过会报错)
     */
    public static int handleOverflowWithBitMask(Long activityId,long value) {
        // 首先处理溢出，保留在Integer范围内的位
        int result = (int) (value & 0x7FFFFFFF);

        // 创建一个确保值大于1000的掩码
        // 1000 = 0b1111101000，所以至少需要设置第10位和第9位
        // 0x3E8 = 1000，0x400 = 1024
        int minValueMask = 0x400; // 设置第10位为1，确保至少为1024

        // 使用按位或操作确保结果大于1000
        result = result | minValueMask;

        log.info("[handleOverflowWithBitMask]--截取房间id，防止房间号超过Integer 最大值,activityId={},value={},result={}", activityId, value, result);
        return result;
    }




    public static  boolean geZero(Integer number) {
        if (Objects.isNull(number)) {
            return false;
        }
        return number > 0;
    }

    public static  boolean geZero(Long number) {
        if (Objects.isNull(number)) {
            return false;
        }
        return number > 0;
    }

    public static  String getMonthEngilish(Date date) {
        String mm = DateUtil.formateDateStr(date, DateUtil._MM);
        String bb[] = new String[]{"January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"};
        if(mm.startsWith("0")){
            mm = mm.substring(1,mm.length());
        }
        int index = MapUtil.getInteger(mm,1) -1 ;
        System.out.println(index);
        return bb[index];

    }

    /**
     * 基于ZonedDateTime的西八区起止时间获取方法
     * @return West8StartEndDto 包含起止时间的DTO对象
     */
    public static  West8StartEndDto getWest8StartEndTime() {
        // 获取当前时间
        ZonedDateTime now = ZonedDateTime.now();

        // 获取今天的08:00时间点
        ZonedDateTime today8AM = now.with(LocalTime.MIN).plusHours(8);

        West8StartEndDto west8StartEndDto = null;
        // 如果当前时间大于今天的08:00
        if (now.isAfter(today8AM)) {
            // 起始时间为今天的08:00，结束时间为明天的08:00
            west8StartEndDto = new West8StartEndDto(today8AM, today8AM.plusDays(1));
        } else {
            // 起始时间为昨天的08:00，结束时间为今天的08:00
            ZonedDateTime yesterday8AM = today8AM.minusDays(1);
            west8StartEndDto = new West8StartEndDto(yesterday8AM, today8AM);
        }

        return west8StartEndDto;
    }

    public static  List<Long> stringToLong2(String str) {
        if (!StringUtils.hasText(str)) {
            return null;
        }
        boolean isArray = str.contains("[");
        if (isArray) {
            return JsonUtil.readList(str, Long.class);
        } else {
            return NumberUtils.stringToLong(StringUtil.split(str, ","));
        }
    }

    public static   boolean leZero(Integer number) {
        if (Objects.isNull(number)) {
            return true;
        }
        return number <= 0;
    }

    /**
     *
     * @param mainActivityId
     * @param goals
     * @param targetType
     * @param timeStyle
     * @return 1 自由跑房间号 app 自己给游戏映射 -1 roomId
     * modified by guqy v3.4.0 roomId 无目标但是 waitTime > 0 需要房间号
     */
   public static   Integer getGoalImNumber(Long mainActivityId, List<Integer> goals, Integer targetType, Integer timeStyle,Integer waitTime) {
        if(waitTime == 0){
            if (CollectionUtils.isEmpty(goals)) {
                return 1;
            }
            if (Objects.isNull(timeStyle) || timeStyle == 1) {
                return 1;
            }
            if (Objects.isNull(targetType) || targetType == 0) {
                return 1;
            }
        }
        goals = goals.stream().sorted().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(goals)){
            // 无目标无数据补个0
            goals.add(0);
        }
        //房间号处理
        Long roomNumber =  NumberUtils.getGoalImNumber(mainActivityId , goals.get(0), targetType);
        return roomNumber.intValue();
    }

    /**
     * 毫秒转换成时间
     * @param ms
     * hh:mm:ss.xx： 时分秒.xx，其中xx为ms保留两位（99即为990ms），四舍五入；
     * mm:ss.xx     如果时长不足60min则不显示小时
     * hh:mm:ss.xx  如果时长超过60min则显示小时
     * @return
     */
    public static  String formatTime(Long ms) {
        Integer ss = 1000;
        Integer mi = ss * 60;
        Integer hh = mi * 60;
        Integer dd = hh * 24;

        Long day = ms / dd;
        Long hour = (ms - day * dd) / hh;
        Long minute = (ms - day * dd - hour * hh) / mi;
        Long second = (ms - day * dd - hour * hh - minute * mi) / ss;
        Long milliSecond = ms - day * dd - hour * hh - minute * mi - second * ss;

        StringBuilder sb = new StringBuilder();
        if(hour > 0) {
            sb.append(hour).append(":");
        }
        if(minute > 0) {
            sb.append(minute).append(":");
        }else {
            sb.append("00:");
        }
        if(second > 0) {
            sb.append(second).append(":");
        }else {
            sb.append("00:");
        }
        if(milliSecond <= 0 ) {
            sb.append("000");
        }else if(milliSecond < 10 ) {
            sb.append("00").append(milliSecond);
        }else if(milliSecond < 100 ) {
            sb.append("0").append(milliSecond);
        }else if(milliSecond < 1000 ) {
            sb.append(milliSecond);
        }else {
            sb.append(999);
        }
        return sb.toString();
    }

    public static Long convertActivityId(Long activityId){
        if(Arrays.asList(0L, 1L).contains(activityId)){
            return 1L;
        }
        return  activityId;
    }

    public static boolean geZero(BigDecimal number) {
        if (Objects.isNull(number)) {
            return false;
        }
        return number.compareTo(BigDecimal.ZERO) >= 0;
    }
}
