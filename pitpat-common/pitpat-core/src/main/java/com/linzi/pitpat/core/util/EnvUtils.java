package com.linzi.pitpat.core.util;

import org.springframework.util.StringUtils;

public class EnvUtils {

    // 获取当前环境
    public static boolean isOnline(String env) {
        if(!StringUtils.hasText(env)){
            return false;
        }
        if ("pre".equals(env) || "prod".equals(env) || env.startsWith("online")) {
            return true;
        }
        return false;
    }


    // 获取当前环境
    public static boolean isReallyOnline(String env) {
        if(!StringUtils.hasText(env)){
            return false;
        }
        if ("prod".equals(env) || env.startsWith("online")) {
            return true;
        }
        return false;
    }



    // 是否是测试环境
    public static boolean isLinux(String env) {
        if(!StringUtils.hasText(env)){
            return false;
        }
        if ("pre".equals(env) || "prod".equals(env)
                || env.startsWith("online") || env.startsWith("test")) {
            return true;
        }
        return false;
    }



    // 是否是测试环境
    public static boolean isDev(String env) {
        if(!StringUtils.hasText(env)){
            return false;
        }
        if ("dev".equals(env)) {
            return true;
        }
        return false;
    }



    // 获取当前环境
    public static boolean isTest(String env) {
        if(!StringUtils.hasText(env)){
            return false;
        }
        if (env.startsWith("test")) {
            return true;
        }
        return false;
    }
}
