package com.linzi.pitpat.core.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;

/**
 * 支付常量类
 */

public class PayConstant {

    /**
     * 支付渠道 (0:余额, 1: paypal, 2:东南亚)
     */
    @Getter
    @AllArgsConstructor
    public enum PayChannelEnum {
        PAY_CHANNEL_0(0, "余额"),
        PAY_CHANNEL_1(1, "paypal"),
        PAY_CHANNEL_2(2, "东南亚支付"),
        ;

        public final Integer type;
        public final String name;

        public static PayChannelEnum findByCode(Integer type) {
            if (type == null) {
                return null;
            }
            return Arrays.stream(PayChannelEnum.values()).filter(o -> type.equals(o.type)).findFirst().orElse(null);
        }

        /**
         * 查询币种的支付方式
         *
         * @param currencyCode
         * @return
         */
        public static PayChannelEnum findByCurrencyCode(String currencyCode) {
            if (ObjectUtils.isEmpty(currencyCode)) {
                return null;
            }
            I18nConstant.CurrencyCodeEnum currencyCodeEnum = I18nConstant.CurrencyCodeEnum.findByCode(currencyCode);
            if (currencyCodeEnum == null) {
                return null;
            }
            if (I18nConstant.SOUTHEAST_ASIA_CURRENCY_CODE.contains(currencyCode)) {
                //东南亚国家
                return PAY_CHANNEL_1;
               // return PAY_CHANNEL_2; 暂时都返回palpay
            }
            return PAY_CHANNEL_1;
        }
    }


}
