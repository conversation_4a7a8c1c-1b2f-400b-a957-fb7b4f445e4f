package com.linzi.pitpat.core.util.dto;

import com.linzi.pitpat.core.constants.enums.DingTalkMsgTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public class DingTalkRequestDto {
    private String accessToken;
    private String secret;
    private String message;
    private List<String> mobile;
    private DingTalkMsgTypeEnum msgType;

    public static DingTalkRequestDto of(String accessToken, String secret, String message) {
        return new DingTalkRequestDto().setAccessToken(accessToken).setSecret(secret).setMessage(message);
    }


    public static DingTalkRequestDto of(String accessToken, String secret, String message, String mobile) {
        DingTalkRequestDto requestDto = new DingTalkRequestDto().setAccessToken(accessToken).setSecret(secret).setMessage(message);
        if (StringUtils.hasText(mobile)) {
            requestDto.setMobile(List.of(mobile.split(",")));
        }
        return requestDto;
    }

    public static DingTalkRequestDto ofMarkdown(String accessToken, String secret, String message) {
        return new DingTalkRequestDto().setAccessToken(accessToken).setSecret(secret).setMessage(message).setMsgType(DingTalkMsgTypeEnum.MARKDOWN);
    }

    public static DingTalkRequestDto ofMarkdown(String accessToken, String secret, String message, String mobile) {
        DingTalkRequestDto requestDto = new DingTalkRequestDto().setAccessToken(accessToken).setSecret(secret).setMessage(message).setMsgType(DingTalkMsgTypeEnum.MARKDOWN);
        if (StringUtils.hasText(mobile)) {
            requestDto.setMobile(List.of(mobile.split(",")));
        }
        return requestDto;
    }
}
