package com.linzi.pitpat.core.util;

import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PatternUtils {
    /**
     * 邮箱正则表达式
     */
    @SuppressWarnings("all")
    private final static Pattern MAIL_PATTERN = Pattern.compile("^\\s*\\w+(?:\\.{0,1}[\\w-]+)*@[a-zA-Z0-9]+(?:[-.][a-zA-Z0-9]+)*\\.[a-zA-Z]+\\s*$");

    private static final List<String> EMAIL_DOMAIN_LIST = Arrays.asList("virtual.email", "pitpat.virtualemail", "linzikg.com");

    /**
     * 判断邮箱是否有效
     * @param email 邮箱
     * @return 检验结果（true：有效 false：无效）
     */
    public static boolean isEmail(String email) {
        if (!StringUtils.hasText(email)) {
            return false;
        }
        Matcher matcher = MAIL_PATTERN.matcher(email);
        return matcher.matches();
    }

    /**
     * 验证是否虚拟的邮箱
     * @param email
     * @return
     */
    public static boolean isVirtualEmail(String email) {
        String[] split = email.split("@");
        String emailSuffix = split[1].toLowerCase().trim();
        return EMAIL_DOMAIN_LIST.contains(emailSuffix);
    }
}
