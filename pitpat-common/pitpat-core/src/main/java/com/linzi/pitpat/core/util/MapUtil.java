package com.linzi.pitpat.core.util;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.Objects;

/**
 * 简化从 Json 转换为 Map后获取属性时转换成本
 */
public class MapUtil {

    public static Boolean getBoolean(Object value) {
        return TypeUtils.castToBoolean(value);
    }

    public static Byte getByte(Object value) {
        return TypeUtils.castToByte(value);
    }

    public static Short getShort(Object value) {
        return TypeUtils.castToShort(value);
    }

    public static Integer getInteger(Object value) {
        return TypeUtils.castToInt(value);
    }

    public static Integer getInteger(Object value, Integer defaultValue) {
        return Objects.isNull(value) ? defaultValue : getInteger(value);
    }

    public static Long getLong(Object value) {
        return TypeUtils.castToLong(value);
    }

    public static Long getLong(Object value, Long defaultValue) {
        return Objects.isNull(value) ? defaultValue : getLong(value);
    }

    public static Float getFloat(Object value) {
        return TypeUtils.castToFloat(value);
    }

    public static Double getDouble(Object value) {
        return TypeUtils.castToDouble(value);
    }

    public static Double getDouble(Object value, Double defaultValue) {
        return Objects.isNull(value)? defaultValue: TypeUtils.castToDouble(value);
    }

    public static BigDecimal getBigDecimal(Object value) {
        return TypeUtils.castToBigDecimal(value);
    }

    public static BigDecimal getBigDecimal(Object value, BigDecimal defaultValue) {
        return Objects.isNull(value)? defaultValue:  TypeUtils.castToBigDecimal(value);
    }

    public static BigInteger getBigInteger(Object value) {
        return TypeUtils.castToBigInteger(value);
    }

    public static String getString(Object value) {
        return value == null ? null : value.toString();
    }
    public static String getString(Object value,String defaultValue) {
        return Objects.isNull(value) ? defaultValue : getString(value);
    }

    private static class TypeUtils {

        public static Boolean castToBoolean(Object answer) {
            if (answer != null) {
                if (answer instanceof Boolean) {
                    return (Boolean) answer;
                }

                if (answer instanceof String) {
                    return Boolean.valueOf((String) answer);
                }

                if (answer instanceof Number n) {
                    return n.intValue() != 0 ? Boolean.TRUE : Boolean.FALSE;
                }
            }
            return null;
        }

        public static Byte castToByte(Object value) {
            Number answer = getNumber(value);
            if (answer == null) {
                return null;
            } else {
                return answer instanceof Byte ? (Byte) answer : answer.byteValue();
            }
        }

        public static Short castToShort(Object value) {
            Number answer = getNumber(value);
            if (answer == null) {
                return null;
            } else {
                return answer instanceof Short ? (Short) answer : answer.shortValue();
            }
        }

        public static Integer castToInt(Object value) {
            Number answer = getNumber(value);
            if (answer == null) {
                return null;
            } else {
                return answer instanceof Integer ? (Integer) answer : answer.intValue();
            }
        }

        public static Long castToLong(Object value) {
            Number answer = getNumber(value);
            if (answer == null) {
                return null;
            } else {
                return answer instanceof Long ? (Long) answer : answer.longValue();
            }
        }

        public static Float castToFloat(Object value) {
            Number answer = getNumber(value);
            if (answer == null) {
                return null;
            } else {
                return answer instanceof Float ? (Float) answer : answer.floatValue();
            }
        }

        public static Double castToDouble(Object value) {
            Number answer = getNumber(value);
            if (answer == null) {
                return null;
            } else {
                return answer instanceof Double ? (Double) answer : answer.doubleValue();
            }
        }

        public static BigDecimal castToBigDecimal(Object value) {
            if (value == null) {
                return null;
            } else if (value instanceof BigDecimal) {
                return (BigDecimal) value;
            } else if (value instanceof BigInteger) {
                return new BigDecimal((BigInteger) value);
            } else {
                String strVal = value.toString();
                if (strVal.isEmpty()) {
                    return null;
                } else {
                    return new BigDecimal(strVal);
                }
            }
        }

        public static BigInteger castToBigInteger(Object value) {
            if (value == null) {
                return null;
            } else if (value instanceof BigInteger) {
                return (BigInteger) value;
            } else if (!(value instanceof Float) && !(value instanceof Double)) {
                if (value instanceof BigDecimal decimal) {
                    int scale = decimal.scale();
                    if (scale > -1000 && scale < 1000) {
                        return ((BigDecimal) value).toBigInteger();
                    }
                }

                String strVal = value.toString();
                return !strVal.isEmpty() && !"null".equals(strVal) && !"NULL".equals(strVal) ? new BigInteger(strVal) : null;
            } else {
                return BigInteger.valueOf(((Number) value).longValue());
            }
        }

        //-------------------------
        public static <K> Number getNumber(Object answer) {
            if (answer != null) {
                if (answer instanceof Number) {
                    return (Number) answer;
                }
                if (answer instanceof String) {
                    try {
                        String text = (String) answer;
                        return NumberFormat.getInstance().parse(text);
                    } catch (ParseException ignored) {
                    }
                }
            }
            return null;
        }
    }
}
