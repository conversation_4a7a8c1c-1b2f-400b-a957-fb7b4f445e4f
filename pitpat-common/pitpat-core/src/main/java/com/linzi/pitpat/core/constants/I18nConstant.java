package com.linzi.pitpat.core.constants;

import com.linzi.pitpat.core.entity.Currency;
import com.linzi.pitpat.core.entity.CurrencyAmount;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 多语言常量类
 */

public class I18nConstant {

    /**
     * 语言head参数
     */
    public final static String LANGUAGE_HEAD_PARAM = "language";

    /**
     * 时区id head参数
     */
    public final static String ZONE_ID_HEAD_PARAM = "zoneId";

    /**
     * 时区偏移量head参数
     */
    public final static String ZONE_OFFSET_HEAD_PARAM = "zoneOffset";

    /**
     * 东南亚币种code
     */
    public final static List<String> SOUTHEAST_ASIA_CURRENCY_CODE = List.of(CurrencyCodeEnum.THB.getCode());

    public final static Map<String,String> COUNTRY_LANGUAGE_MAP = new HashMap();
    static {
        COUNTRY_LANGUAGE_MAP.put(CountryCodeEnum.CA.code,LanguageCodeEnum.en_US.code);
        COUNTRY_LANGUAGE_MAP.put(CountryCodeEnum.US.code,LanguageCodeEnum.en_US.code);
        COUNTRY_LANGUAGE_MAP.put(CountryCodeEnum.UK.code,LanguageCodeEnum.en_US.code);
        COUNTRY_LANGUAGE_MAP.put(CountryCodeEnum.DE.code,LanguageCodeEnum.de_DE.code);
        COUNTRY_LANGUAGE_MAP.put(CountryCodeEnum.IT.code,LanguageCodeEnum.it_IT.code);
        COUNTRY_LANGUAGE_MAP.put(CountryCodeEnum.ES.code,LanguageCodeEnum.es_ES.code);
        COUNTRY_LANGUAGE_MAP.put(CountryCodeEnum.PT.code,LanguageCodeEnum.pt_PT.code);
        COUNTRY_LANGUAGE_MAP.put(CountryCodeEnum.TH.code,LanguageCodeEnum.th_TH.code);
        COUNTRY_LANGUAGE_MAP.put(CountryCodeEnum.FR.code,LanguageCodeEnum.fr_CA.code);
    }

    public static Currency buildCurrency(String currencyCode, String defaultCode) {
        if (StringUtils.isEmpty(currencyCode)) {
            currencyCode = defaultCode;
        }
        return buildCurrency(currencyCode);
    }

    /**
     * 币种code USD：美元，CAD：加币，EUR：欧元，GBP：英镑
     */
    @Getter
    @AllArgsConstructor
    public enum CurrencyCodeEnum {
        USD("USD", "美元", "$"),
        CAD("CAD", "加币", "C$"),
        EUR("EUR", "欧元", "€"),
        GBP("GBP", "英镑", "£"),
        THB("THB","泰铢","฿"),
        ;

        private final String code;
        private final String name;
        private final String symbol;

        public static final CurrencyCodeEnum[] VALUES;

        static {
            VALUES = values();
        }

        public static CurrencyCodeEnum findByCode(String code) {
            if (StringUtils.isEmpty(code)) {
                return null;
            }
            return Arrays.stream(VALUES).filter(o -> code.equals(o.code)).findFirst().orElse(USD);
        }

        /**
         * 构建货币对象
         *
         * @return
         */
        public final Currency getCurrency() {
            return buildCurrency(code);
        }

        /**
         * 构建货币金额对象
         *
         * @param amount
         * @return
         */
        public final CurrencyAmount getCurrencyAmount(BigDecimal amount) {
            return new CurrencyAmount(buildCurrency(code), amount);
        }
    }


    /**
     * 语言code，en_US：英语，fr_CA ：法语，de_DE：德语，it_IT：意大利语，es_ES：西班牙语，pt_PT：葡萄牙语,th_TH：泰语
     */
    @Getter
    @AllArgsConstructor
    public enum LanguageCodeEnum {
        //通过Local 可以手动遍历某个 msg key 的所有翻译
        en_US(1,"en_US", "英语"),
        fr_CA(2,"fr_CA", "法语"),
        de_DE(3,"de_DE", "德语"),
        it_IT(4,"it_IT", "意大利语"),
        es_ES(5,"es_ES", "西班牙语"),
        pt_PT(6,"pt_PT", "葡萄牙语"),
        th_TH(7,"th_TH", "泰语"),
        ;

        private final Integer type;
        private final String code;
        private final String name;

        public static final LanguageCodeEnum[] VALUES;

        static {
            VALUES = values();
        }

        public static LanguageCodeEnum findByCode(String code) {
            if (StringUtils.isEmpty(code)) {
                return null;
            }
            return Arrays.stream(VALUES).filter(o -> code.equals(o.code)).findFirst().orElse(null);
        }
    }


    /**
     * 国家code，US：美国，CA ：加拿大
     */
    @Getter
    @AllArgsConstructor
    public enum CountryCodeEnum {
        CN("CN","中国","China","Beijing","CN_BTV"),
        US("US","美国","United States","Alabama","US_AFA"),
        CA("CA","加拿大","Canada","Alberta","CA_AYC"),
        UK("UK","英国","United Kingdom","Yorkshire & the Humber","UK_YWH"),
        KR("KR","韩国","Korea","Daegu","KR_DYW"),
        DE("DE","德国","Germany","Baden-Wuerttemberg","DE_BGM"),
        IT("IT","意大利","Italy","Abruzzo","IT_AXX"),
        FR("FR","法国","France","Auvergne-Rhône-Alpes","FR_AQY"),
        JP("JP","日本","Japan","Ehime","JP_EKI"),
        RU("RU","俄罗斯","Russia","Abakan","RU_ARL"),
        EG("EG","埃及","Egypt","aswan","EG_AWE"),
        GR("GR","希腊","Greece","piraeus","GR_PJD"),
        AU("AU","澳大利亚","Australia","northern region","AU_NQV"),
        BR("BR","巴西","Brazil","Acre","BR_AXN"),
        IN("IN","印度","India","Aizawl","IN_AEA"),
        IE("IE","爱尔兰","Ireland","Offaly","IE_OEO"),
        PL("PL","波兰","Poland","Elblag","PL_EEZ"),
        TH("TH","泰国","Thailand","Bangkok","TH_BVU"),
        AT("AT","奥地利","Austria","Burgenland","AT_BPW"),
        ES("ES","西班牙","Spain","Andalusia","ES_ARE"),
        PT("PT","葡萄牙","Portugal","Aveiro","PT_AVA"),
        AL("AL","阿尔巴尼亚","Albania","Elbasan","AL_ESH"),
        DZ("DZ","阿尔及利亚","Algeria","Adlar","DZ_ANW"),
        AF("AF","阿富汗","Afghanistan","Herat","AF_HFL"),
        AR("AR","阿根廷","Argentina","paraná","AR_PMN"),
        AE("AE","阿拉伯联合酋长国","United Arab Emirates","abu dhabi","AE_AKJ"),
        OM("OM","阿曼","Oman","batinay region","OM_BIU"),
        AZ("AZ","阿塞拜疆","Azerbaijan","Absheron","AZ_AOD"),
        ET("ET","埃塞俄比亚","Ethiopia","Afar","ET_AKW"),
        EE("EE","爱沙尼亚","Estonia","Belva","EE_BPR"),
        AD("AD","安道尔","Andorra","Andorra la Vella","AD_AGR"),
        AO("AO","安哥拉","Angola","Kwanza Norte","AO_KQW"),
        PG("PG","巴布亚新几内亚","Papua New Guinea","north","PG_NWA"),
        BS("BS","巴哈马","Bahamas","Abaco","BS_AMU"),
        PK("PK","巴基斯坦","Pakistan","Peshawar","PK_PZM"),
        PY("PY","巴拉圭","Paraguay","amanbai","PY_ALQ"),
        PS("PS","巴勒斯坦","Palestine","Gaza Strip","PS_GPC"),
        BH("BH","巴林","Bahrain","north","BH_NHK"),
        BY("BY","白俄罗斯","Belarus","Brest","BY_BVL"),
        BG("BG","保加利亚","Bulgaria","Burgas","BG_BCZ"),
        BJ("BJ","贝宁","Benin","aliboli","BJ_ACW"),
        BE("BE","比利时","Belgium","Hainaut","BE_HPF"),
        PR("PR","波多黎各","Puerto Rico","Aguada","PR_AAD"),
        BO("BO","玻利维亚","Bolivia","Alto","BO_ARG"),
        BA("BA","波斯尼亚和黑塞哥维那","Bosnia and Herzegovina","posavina","BA_PUK"),
        BW("BW","博茨瓦纳","Botswana","Southern District","BW_SXW"),
        BZ("BZ","伯利兹","Belize","Belize","BZ_BDK"),
        BT("BT","不丹","Bhutan","Paro","BT_PHO"),
        BF("BF","布基纳法索","Burkina Faso","barre","BF_BHF"),
        BI("BI","布隆迪","Burundi","Bubanza","BI_BZI"),
        KP("KP","朝鲜","North Korea","Haizhou","KP_HHZ"),
        DK("DK","丹麦","Denmark","Aarhus","DK_ARJ"),
        TL("TL","东帝汶","East Timor","Aileu","TL_ATC"),
        TG("TG","多哥","Togo","Binhai District","TG_BYM"),
        EC("EC","厄瓜多尔","Ecuador","azuai","EC_AAI"),
        ER("ER","厄立特里亚","Eritrea","anseba","ER_ANO"),
        PH("PH","菲律宾","the Philippines","Davao","PH_DRW"),
        FI("FI","芬兰","Finland","Espoo","FI_EGG"),
        CV("CV","佛得角","cape verde","Paul","CV_PYH"),
        CD("CD","刚果民主共和国","Democratic Republic of the Congo","Bandundu","CD_BXZ"),
        CO("CO","哥伦比亚","Colombia","Arauca","CO_AHB"),
        CR("CR","哥斯达黎加","costa rica","Alajuela","CR_AJR"),
        GL("GL","格陵兰","Greenland","Avannaata","GL_ATZ"),
        CU("CU","古巴","cuba","Holguin","CU_HSU"),
        GY("GY","圭亚那","Guyana","Essequibo Islands-West Demerara","GY_ECT"),
        KZ("KZ","哈萨克斯坦","Kazakhstan","Alkarek","KZ_ANX"),
        HT("HT","海地","Haiti","Artibonite","HT_AIJ"),
        NL("NL","荷兰","Netherlands","almere","NL_ART"),
        HN("HN","洪都拉斯","Honduras","atlantida","HN_AAB"),
        KI("KI","基里巴斯","Kiribati","phoenix islands","KI_PBP"),
        DJ("DJ","吉布提","Djibouti","Ali Sabih District","DJ_AAK"),
        KG("KG","吉尔吉斯斯坦","Kyrgyzstan","Osh","KG_OJI"),
        GN("GN","几内亚","Guinea","Bokai","GN_BEB"),
        GH("GH","加纳","Ghana","Ashanti","GH_AVE"),
        GA("GA","加蓬","Gabon","ogowe-lolo","GA_OXW"),
        KH("KH","柬埔寨","Cambodia","Oddar Meanchey","KH_OQD"),
        CZ("CZ","捷克共和国","Czech Republic","olomouc","CZ_OCO"),
        ZW("ZW","津巴布韦","Zimbabwe","matabeleland north","ZW_MVB"),
        CM("CM","喀麦隆","Cameroon","Adamawa","CM_ALP"),
        QA("QA","卡塔尔","Qatar","north","QA_NCJ"),
        CI("CI","科特迪瓦","Cote d'Ivoire","anyebi","CI_AQG"),
        HR("HR","克罗地亚","Croatia","Osijek-Baranja","HR_OYK"),
        KE("KE","肯尼亚","Kenya","Elgueyo-Maracquet","KE_EQJ"),
        CK("CK","库克群岛","Island","Rarotonga","CK_RWI"),
        LV("LV","拉脱维亚","Latvia","aluxne","LV_ASV"),
        LS("LS","莱索托","Lesotho","Berea","LS_BKG"),
        LA("LA","老挝","Laos","Attapeu","LA_ANB"),
        LB("LB","黎巴嫩","lebanon","north","LB_NZJ"),
        LR("LR","利比里亚","Liberia","Babolu","LR_BJJ"),
        LY("LY","利比亚","Libya","Barqa","LY_BPC"),
        LT("LT","立陶宛","Lithuania","Alitus","LT_ASO"),
        LU("LU","卢森堡","Luxembourg","Dikirch","LU_DNS"),
        RW("RW","卢旺达","Rwanda","biwemba","RW_BVF"),
        RO("RO","罗马尼亚","Romania","Alba Iulia","RO_AJC"),
        MG("MG","马达加斯加","madagascar","antsiranana","MG_ASP"),
        MV("MV","马尔代夫","Maldives","Adu","MV_ARA"),
        MT("MT","马耳他","malta","St. Julian's","MT_SZO"),
        MW("MW","马拉维","Malawi","Northern District","MW_NFD"),
        MY("MY","马来西亚","Malaysia","penang island","MY_PHU"),
        ML("ML","马里","Mali","Bamako Capital Region","ML_BMY"),
        MR("MR","毛里塔尼亚","mauritania","Adlar","MR_ANF"),
        AS("AS","美属萨摩亚","American Samoa","Ana","AS_AFS"),
        MN("MN","蒙古","Mongolia","bayanhonger","MN_BBM"),
        BD("BD","孟加拉","Bengal","Dhaka","BD_DOV"),
        FM("FM","密克罗尼西亚","Micronesia","Chuuk","FM_CNV"),
        PE("PE","秘鲁","Peru","Arequipa","PE_ACP"),
        MM("MM","缅甸","Myanmar","Ministry of Erection","MM_MQZ"),
        MD("MD","摩尔多瓦","moldova","Anenii Noi","MD_AXW"),
        MA("MA","摩洛哥","Morocco","Tangier","MA_TXT"),
        MX("MX","墨西哥","Mexico","aguascaliente","MX_AWU"),
        NA("NA","纳米比亚","Namibia","erongo","NA_EUD"),
        ZA("ZA","南非","South Africa","Upington","ZA_UFW"),
        NR("NR","瑙鲁","Nauru","Aiwo","NR_AUU"),
        NP("NP","尼泊尔","Nepal","Bagmati","NP_BGO"),
        NI("NI","尼加拉瓜","Nicaragua","Esteli","NI_EYB"),
        NE("NE","尼日尔","Niger","Agadez","NE_AQB"),
        NG("NG","尼日利亚","Nigeria","abia","NG_AXD"),
        NO("NO","挪威","Norway","akshus","NO_AKF"),
        SE("SE","瑞典","Sweden","norrbotten","SE_NOF"),
        CH("CH","瑞士","Switzerland","allgäu","CH_ABC"),
        SV("SV","萨尔瓦多","El Salvador","apopa","SV_AIK"),
        RS("RS","塞尔维亚,黑山","Serbia, Montenegro","belgrade","RS_BND"),
        SL("SL","塞拉利昂","Sierra Leone","north","SL_NGM"),
        SN("SN","塞内加尔","Senegal","dakar","SN_DKP"),
        CY("CY","塞浦路斯","Cyprus","Famagusta","CY_FJU"),
        SC("SC","塞舌尔","Seychelles","Anse Aux Pins","SC_ANV"),
        SA("SA","沙特阿拉伯","Saudi Arabia","Al Al","SA_ATW"),
        KN("KN","圣基茨和尼维斯","Saint Kitts and Nevis","Christ Church Nichola Town","KN_CYK"),
        LK("LK","斯里兰卡","Sri Lanka","Anuradhapura","LK_ALG"),
        SK("SK","斯洛伐克","Slovakia","banská bystrica","SK_BAI"),
        SI("SI","斯洛文尼亚","slovenia","Obarno-Kra","SI_OIJ"),
        SD("SD","苏丹","Sudan","north","SD_NOC"),
        SR("SR","苏里南","Surinam","broccopondo","SR_BZN"),
        SB("SB","所罗门群岛","solomon islands","Guadalcanal","SB_GCH"),
        TJ("TJ","塔吉克斯坦","Tajikistan","dushanbe","TJ_DIS"),
        TZ("TZ","坦桑尼亚","Tanzania","Arusha","TZ_AZQ"),
        TO("TO","汤加","Tonga","eva","TO_EKJ"),
        TN("TN","突尼斯","Tunisia","alyanai","TN_AAN"),
        TR("TR","土耳其","Türkiye","Adana","TR_AGQ"),
        TM("TM","土库曼斯坦","Turkmenistan","Ahal","TM_ADG"),
        VU("VU","瓦努阿图","Vanuatu","malampa","VU_MUE"),
        GT("GT","危地马拉","Guatemala","el progreso","GT_EIR"),
        VE("VE","委内瑞拉","Venezuela","aragua","VE_AHU"),
        BN("BN","文莱","Brunei","Belait","BN_BMH"),
        UG("UG","乌干达","Uganda","Arua","UG_AEG"),
        UA("UA","乌克兰","Ukraine","Odessa","UA_OOP"),
        UY("UY","乌拉圭","Uruguay","Artigas","UY_AOO"),
        UZ("UZ","乌兹别克斯坦","Uzbekistan","Andijan","UZ_AUD"),
        NZ("NZ","新西兰","New Zealand","Auckland","NZ_AKN"),
        HU("HU","匈牙利","Hungary","Baranya","HU_BUU"),
        SY("SY","叙利亚","Syria","Aleppo","SY_AAW"),
        JM("JM","牙买加","jamaica","portland","JM_PGE"),
        AM("AM","亚美尼亚","Armenia","almavir","AM_AAL"),
        YE("YE","也门","Yemen","Abyan","YE_ATV"),
        IQ("IQ","伊拉克","Iraq","Baghdad","IQ_BCL"),
        IL("IL","以色列","Israel","ashdod","IL_AWJ"),
        ID("ID","印度尼西亚","Indonesia","bali","ID_BZE"),
        JO("JO","约旦","Jordan","ajlon","JO_ABQ"),
        VN("VN","越南","Vietnam","Haiphong","VN_HFG"),
        ZM("ZM","赞比亚","Zambia","north","ZM_NRI"),
        TD("TD","乍得","Chad","Batha","TD_BZL"),
        CL("CL","智利","Chile","Araucanía Region","CL_ABI"),
        CF("CF","中非共和国","central african republic","Bamingi-Bangoran","CF_BAG"),
        AW("AW","阿鲁巴","Aruba","Aruba","AW_AYO"),
        AC("AC","阿森松岛","Ascension Island","Ascension Island","AC_AAA"),
        AI("AI","安圭拉","Anguilla","Anguilla","AI_API"),
        AG("AG","安提瓜岛和巴布达","Antigua and Barbuda","Antigua and Barbuda","AG_ARV"),
        AX("AX","奥兰群岛","Åland Islands","Åland Islands","AX_LLS"),
        BB("BB","巴巴多斯岛","Barbados","Barbados","BB_BDB"),
        PA("PA","巴拿马","Panama","Panama","PA_PNM"),
        BM("BM","百慕大","Bermuda","Bermuda","BM_BTT"),
        MP("MP","北马里亚纳群岛","Northern Mariana Islands","Northern Mariana Islands","MP_NEQ"),
        IS("IS","冰岛","Iceland","Iceland","IS_ILT"),
        BV("BV","布韦岛","Bouvet Island","Bouvet Island","BV_BPU"),
        DO("DO","多米尼加","Dominica","Dominica","DO_DRL"),
        FO("FO","法罗群岛","Faroe Islands","Faroe Islands","FO_FUA"),
        PF("PF","法属波利尼西亚","French Polynesia","French Polynesia","PF_FEF"),
        GF("GF","法属圭亚那","French Guiana","French Guiana","GF_FNG"),
        TF("TF","法属南部领地","french southern territories","french southern territories","TF_FYU"),
        VA("VA","梵蒂冈","Vatican","Vatican","VA_VYJ"),
        FJ("FJ","斐济","Fiji","Fiji","FJ_FXC"),
        FK("FK","弗兰克群岛","Frank Islands","Frank Islands","FK_FDX"),
        GM("GM","冈比亚","Gambia","Gambia","GM_GUG"),
        CG("CG","刚果","Congo","Congo","CG_CTG"),
        GG("GG","格恩西岛","Guernsey","Guernsey","GG_GJN"),
        GD("GD","格林纳达","Greneda","Greneda","GD_GVR"),
        GP("GP","瓜德罗普","Guadeloupe","Guadeloupe","GP_GIR"),
        GU("GU","关岛","Guam","Guam","GU_GGY"),
        AN("AN","荷属安地列斯","Netherlands Antilles","Netherlands Antilles","AN_NBG"),
        HM("HM","赫德和麦克唐纳群岛","heard and macdonald islands","heard and macdonald islands","HM_HBN"),
        GW("GW","几内亚比绍","Guinea-Bissau","Guinea-Bissau","GW_GIS"),
        KY("KY","开曼群岛","Cayman Islands","Cayman Islands","KY_CVY"),
        CC("CC","科科斯群岛","cocos islands","cocos islands","CC_CEX"),
        KM("KM","科摩罗","Comoros","Comoros","KM_CLP"),
        KW("KW","科威特","Kuwait","Kuwait","KW_KBP"),
        LI("LI","列支敦士登","Liechtenstein","Liechtenstein","LI_LJO"),
        RE("RE","留尼旺岛","Reunion","Reunion","RE_RVH"),
        MK("MK","马其顿","macedonia","macedonia","MK_MUK"),
        MH("MH","马绍尔群岛","Marshall Islands","Marshall Islands","MH_MSQ"),
        MQ("MQ","马提尼克","Martinique","Martinique","MQ_MOF"),
        YT("YT","马约特岛","mayotte","mayotte","YT_MHX"),
        IM("IM","曼岛","Isle of Man","Isle of Man","IM_IPV"),
        MU("MU","毛里求斯","Mauritius","Mauritius","MU_MNG"),
        UM("UM","美属外岛","U.S. Outlying Islands","U.S. Outlying Islands","UM_UZZ"),
        MS("MS","蒙特塞拉特","montserrat","montserrat","MS_MPC"),
        MC("MC","摩纳哥","Monaco","Monaco","MC_MEX"),
        MZ("MZ","莫桑比克","Mozambique","Mozambique","MZ_MUF"),
        GS("GS","南乔治亚和南桑德威奇群岛","South Georgia and South Sandwich","South Georgia and South Sandwich Islands","GS_SJM"),
        NU("NU","纽埃","Niue","Niue","NU_NOP"),
        NF("NF","诺福克","norfolk","norfolk","NF_NXQ"),
        PW("PW","帕劳群岛","palau islands","palau islands","PW_PQS"),
        PN("PN","皮特凯恩","Pitcairn","Pitcairn","PN_PKE"),
        GE("GE","乔治亚","georgia","georgia","GE_GBK"),
        WS("WS","萨摩亚","Samoa","Samoa","WS_SYC"),
        CX("CX","圣诞岛","christmas island","christmas island","CX_CRJ"),
        ST("ST","圣多美和普林西比","sao tome and principe","sao tome and principe","ST_SLS"),
        SH("SH","圣赫勒拿","saint helena","saint helena","SH_SPC"),
        LC("LC","圣卢西亚","saint lucia","saint lucia","LC_SPU"),
        SM("SM","圣马力诺","san marino","san marino","SM_SMO"),
        PM("PM","圣皮埃尔和米克隆群岛","Saint Pierre and Miquelon Island","Saint Pierre and Miquelon Islands","PM_SMG"),
        VC("VC","圣文森特和格林纳丁斯","Saint Vincent and the Grenadines","Saint Vincent and the Grenadines","VC_SFM"),
        SJ("SJ","斯瓦尔巴和扬马廷","Svalbard and Jan Martin","Svalbard and Jan Martin","SJ_SQQ"),
        SZ("SZ","斯威士兰","Swaziland","Swaziland","SZ_SKX"),
        SO("SO","索马里","somalia","somalia","SO_SSK"),
        TC("TC","特克斯和凯克特斯群岛","turks and kecates islands","turks and kecates islands","TC_TLL"),
        TT("TT","特立尼达和多巴哥","Trinidad and Tobago","Trinidad and Tobago","TT_TIP"),
        TV("TV","图瓦卢","Tuvalu","Tuvalu","TV_TBG"),
        TK("TK","托克劳","Tokelau","Tokelau","TK_TFX"),
        WF("WF","瓦利斯和福图纳","wallis and fortuna","wallis and fortuna","WF_WFE"),
        SG("SG","新加坡","Singapore","Singapore","SG_SGA"),
        NC("NC","新喀里多尼亚","new caledonia","new caledonia","NC_NFQ"),
        IR("IR","伊朗","Iran","Iran","IR_INB"),
        JE("JE","泽西岛","Jersey","Jersey","JE_JZI"),
        GI("GI","直布罗陀","Gibraltar","Gibraltar","GI_GDD"),
        DEFAULT("","","","",""),

        ;

        public final String code;
        public final String name;
        public final String enName;
        public final String defaultState;
        public final String defaultStateCode;

        public static final CountryCodeEnum[] VALUES;

        static {
            VALUES = values();
        }

        public static CountryCodeEnum findByCode(String code) {
            if (StringUtils.isEmpty(code)) {
                return null;
            }
            return Arrays.stream(VALUES).filter(o -> code.equals(o.code)).findFirst().orElse(null);
        }

        public static CountryCodeEnum findByEnName(String enName) {
            if (StringUtils.isEmpty(enName)) {
                return null;
            }
            return Arrays.stream(VALUES).filter(o -> enName.equals(o.enName)).findFirst().orElse(null);
        }
    }

    public static Currency buildDefaultCurrency() {
        return buildCurrency(CurrencyCodeEnum.USD);
    }


    /**
     * 通过币种 构建币种对象
     *
     * @param currencyCode
     * @return
     */
    public static Currency buildCurrency(String currencyCode) {
        return buildCurrency(CurrencyCodeEnum.findByCode(currencyCode));
    }

    /**
     * 通过币种 构建币种金额对象
     *
     * @param currencyCode
     * @param amount
     * @return
     */
    public static CurrencyAmount buildCurrencyAmount(String currencyCode, BigDecimal amount) {
        return buildCurrencyAmount(CurrencyCodeEnum.findByCode(currencyCode), amount);
    }

    /**
     * 通过币种枚举 构建币种对象
     *
     * @param currencyCodeEnum
     * @return
     */
    public static Currency buildCurrency(CurrencyCodeEnum currencyCodeEnum) {
        if (currencyCodeEnum == null) {
            return null;
        }
        return new Currency(currencyCodeEnum.getName(), currencyCodeEnum.getCode(), currencyCodeEnum.getSymbol());
    }

    /**
     * 通过币种枚举 构建币种金额对象
     *
     * @param currencyCodeEnum
     * @param amount
     * @return
     */
    public static CurrencyAmount buildCurrencyAmount(CurrencyCodeEnum currencyCodeEnum, BigDecimal amount) {
        if (currencyCodeEnum == null) {
            return null;
        }
        return new CurrencyAmount(buildCurrency(currencyCodeEnum.getCode()), amount);
    }


    /**
     * 币种格式化
     * @param currencyCode 币种
     * @param amount 金额
     * @return
     */
    public static BigDecimal currencyFormat (String currencyCode, BigDecimal amount) {
        if (Objects.isNull(amount)){
            return null;
        }
        int newScale = 2;
        RoundingMode roundingMode = RoundingMode.HALF_UP;
        if (CurrencyCodeEnum.THB.getCode().equals(currencyCode)) {
            //泰铢保留整数并向上取整
            newScale = 0;
            roundingMode = RoundingMode.CEILING ;
        }
        return amount.setScale(newScale,roundingMode);
    }
    /**
     * 币种格式化
     * @param currencyCode 币种
     * @param amount 金额
     * @return
     */
    public static BigDecimal currencyFormat (String currencyCode, BigDecimal amount,RoundingMode roundingMode) {
        if (Objects.isNull(amount)){
            return null;
        }
        int newScale = 2;
        if (CurrencyCodeEnum.THB.getCode().equals(currencyCode)) {
            //泰铢保留整数并向上取整
            newScale = 0;
            roundingMode = RoundingMode.CEILING ;
        }
        return amount.setScale(newScale,roundingMode);
    }


    /**
     * 版本控制
     * @param appVersion
     * @return
     */
    public static List<String> appVersion(Integer appVersion){
        if (appVersion< 3080){
            List<String> list= new ArrayList<>();
            list.add(CountryCodeEnum.US.code);
            list.add(CountryCodeEnum.CA.code);
            list.add(CountryCodeEnum.UK.code);
            list.add(CountryCodeEnum.FR.code);
            list.add(CountryCodeEnum.DE.code);
            list.add(CountryCodeEnum.IT.code);
            list.add(CountryCodeEnum.ES.code);
            list.add(CountryCodeEnum.PT.code);
            list.add(CountryCodeEnum.TH.code);
            return list;
        }
        return null;

    }
}
