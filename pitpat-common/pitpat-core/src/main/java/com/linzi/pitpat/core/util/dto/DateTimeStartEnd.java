package com.linzi.pitpat.core.util.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.Date;

@Data
@NoArgsConstructor
public class DateTimeStartEnd {


    private ZonedDateTime startTime;

    private ZonedDateTime endTime;

    public DateTimeStartEnd(ZonedDateTime startTime, ZonedDateTime endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
    }
}
