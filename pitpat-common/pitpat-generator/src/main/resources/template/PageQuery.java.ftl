package ${package}.model.query;

import com.linzi.pitpat.lang.PageQuery;
import ${package}.model.entity.${table.spaceName}Do;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.io.Serial;

<#assign now = .now>
<#assign date = now?date>

/**
 * ${table.comment}分页查询对象
 *
 * @since ${date}
 */
@Getter
@Accessors(chain = true)
public class ${table.spaceName}PageQuery extends PageQuery {

    private Long id ;
}
