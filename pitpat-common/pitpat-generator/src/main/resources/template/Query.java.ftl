package ${package}.model.query;

import com.linzi.pitpat.lang.Query;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serial;

<#assign now = .now>
<#assign date = now?date>

/**
 * ${table.comment}列表查询对象
 *
 * @since ${date}
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class ${table.spaceName}Query extends Query {

    private Long id;
}
