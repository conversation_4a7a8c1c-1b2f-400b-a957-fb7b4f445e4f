package ${package}.console.api.fallback;

import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.lang.Result;
import ${package}.console.api.dto.request.${table.spaceName}QueryDto;
import ${package}.console.api.dto.response.${table.spaceName}ResponseDto;
import ${package}.console.api.interfaces.${table.spaceName}ConsoleApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;

 <#assign now = .now>
 <#assign date = now?date> <#--?iso_utc-->

 /**
  *
  * @since ${date}
  */
@Slf4j
public class ${table.spaceName}ConsoleFallbackFactory implements FallbackFactory<${table.spaceName}ConsoleApi> {
    @Override
    public ${table.spaceName}ConsoleApi create(Throwable cause) {
        //TODO接口实现
        return new ${table.spaceName}ConsoleApi() {
            @Override
            public Result<${table.spaceName}ResponseDto> get(${table.spaceName}QueryDto queryDto) {
                log.error("fallback cause, msg={}", cause.getMessage(), cause);
                return CommonResult.fail(CommonError.BUSINESS_ERROR);
            }
        };
    }
}
