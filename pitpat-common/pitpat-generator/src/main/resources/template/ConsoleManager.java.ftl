package ${package}.manager.console;

import ${package}.service.${table.spaceName}Service;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

<#assign now = .now>
<#assign date = now?date> <#--?iso_utc-->
/**
 * 不是所有的业务都需要通过 manager 层去透传给 service ,如果是service 提供的能力已经满足 controller 的需求，可以直接通过 service 提供能力，
 * 这样尽在需要何必要的情况下 使用 manager 确保 合理的分层和代码维护
 *
 *  manager 类里面的依赖全部使用构造方法，为什么？ 因为官方推荐这么做，同时也可以避免依赖循环和 依赖膨胀，当该类的依赖太多，你可能需要考虑重构了
 *  @since ${date}
 */
 @Slf4j
@Component
@RequiredArgsConstructor
public class ${table.spaceName}ConsoleManager {
    //强制使用构造器传参
    private final ${table.spaceName}Service ${table.javaName}Service;


//    //   TODO 手动删掉一下代码，一下代码支持给出一些Manager使用场景以及命名建议
//    /**
//     * 冻结用户
//     * @param ${table.javaName}
//     */
//    public void register(${table.spaceName} ${table.javaName}) {
//        //DOTO do something
//        ${table.javaName}Service.create(${table.javaName});
//    }
//
//    /**
//     * 用户注销
//     * @param ${table.javaName}Id
//     */
//    public void deregister(Long ${table.javaName}Id) {
//        // TODO 开启 Mybatis plus 全局配置，使用软删除
//        ${table.javaName}Service.deleteById(${table.javaName}Id);
//    }
//
//    /**
//     * 第三方绑定
//     * @return
//     */
//    public void oathBind${table.spaceName}(){}
//
//    /**
//     * 报名
//     */
//    public void enroll(){}
//
//    /**
//     * 退款
//     */
//    public void refund(){}


}
