package ${package}.console.api.interfaces;

import com.linzi.pitpat.lang.Result;
import ${package}.console.api.dto.request.${table.spaceName}QueryDto;
import ${package}.console.api.dto.response.${table.spaceName}ResponseDto;
import ${package}.console.api.fallback.${table.spaceName}ConsoleFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

 <#assign now = .now>
 <#assign date = now?date> <#--?iso_utc-->

 /**
  * value 名称与 spring.application.name 一致
  * 根据实际需要暴露需要的 feign api
  *
  * @since ${date}
  */
@FeignClient(value = "pitpat-user", path = "/${table.javaName}s", fallbackFactory = ${table.spaceName}ConsoleFallbackFactory.class)
public interface ${table.spaceName}ConsoleApi {

    /**
     * 按照ID 查询${table.comment}
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/get")
    Result<${table.spaceName}ResponseDto> get(@RequestBody ${table.spaceName}QueryDto queryDto);
}
