<#if microservices == false>
    <#if console == true>
package ${package}.dto.console.request;
    <#else>
package ${package}.dto.api.request;
    </#if>
<#else>
    <#if console == true>
package ${package}.console.api.dto.request;
    <#else>
package ${package}.api.dto.request;
    </#if>
</#if>

import java.io.Serializable;
import lombok.Data;

<#assign now = .now>
<#assign date = now?date> <#--?iso_utc-->

/**
* Request 不要简写
* ${table.comment} 更新 DTO
*
* @since ${date}
*/
@Data
public class ${table.spaceName}UpdateRequestDto implements Serializable {

}
