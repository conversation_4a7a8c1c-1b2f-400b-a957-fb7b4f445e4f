package ${package}.controller.console;

import com.linzi.pitpat.core.web.CommonResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.lang.Result;
import ${package}.converter.console.${table.spaceName}ConsoleConverter;
import ${package}.manager.console.${table.spaceName}ConsoleManager;
import ${package}.model.entity.${table.spaceName}Do;
import ${package}.model.query.${table.spaceName}PageQuery;
import ${package}.model.query.${table.spaceName}Query;
import ${package}.service.${table.spaceName}Service;

<#if microservices == false>
import ${package}.dto.console.request.${table.spaceName}CreateRequestDto;
import ${package}.dto.console.request.${table.spaceName}PageQueryDto;
import ${package}.dto.console.request.${table.spaceName}QueryDto;
import ${package}.dto.console.request.${table.spaceName}UpdateRequestDto;
import ${package}.dto.console.response.${table.spaceName}ResponseDto;
<#else>
import ${package}.console.dto.request.${table.spaceName}CreateRequestDto;
import ${package}.console.dto.request.${table.spaceName}PageQueryDto;
import ${package}.console.dto.request.${table.spaceName}QueryDto;
import ${package}.console.dto.request.${table.spaceName}UpdateRequestDto;
import ${package}.console.dto.response.${table.spaceName}ResponseDto;
</#if>

import java.util.List;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

<#assign now = .now>
<#assign date = now?date>

/**
 * ${table.comment} 服务类
 *
 * @since ${date}
 */
@Slf4j
@RestController
@RequestMapping("/${table.javaName}s")
@RequiredArgsConstructor
public class ${table.spaceName}ConsoleController {

    private final ${table.spaceName}Service ${table.javaName}Service;
    private final ${table.spaceName}ConsoleManager ${table.javaName}ConsoleManager;
    private final ${table.spaceName}ConsoleConverter ${table.javaName}ConsoleConverter;

    /**
     * 创建${table.comment}
     *
     * @param requestDto ${table.comment}表单
     * @return ${table.spaceName}
     */
    @PostMapping("/create")
    public Result<Long> create(@RequestBody @Validated ${table.spaceName}CreateRequestDto requestDto) {
        ${table.spaceName}Do ${table.javaName} =${table.javaName}ConsoleConverter.toDo(requestDto);
        return CommonResult.success(${table.javaName}Service.create(${table.javaName}));
    }

    /**
     * 更新${table.comment}
     *
     * @param requestDto ${table.comment}表单
     * @return ${table.spaceName}
     */
    @PostMapping("/update")
    public Result<Long> update(@RequestBody @Validated ${table.spaceName}UpdateRequestDto requestDto) {
        ${table.spaceName}Do ${table.javaName} =${table.javaName}ConsoleConverter.toDo(requestDto);
        return CommonResult.success(${table.javaName}Service.update(${table.javaName}));
    }

    /**
     * 按照ID 删除${table.comment}
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/delete")
    public Result<Boolean> delete(@RequestBody ${table.spaceName}QueryDto queryDto) {
        boolean affected = ${table.javaName}Service.deleteById(queryDto.getId());
        return CommonResult.success(affected);
    }

    /**
     * 按照ID 查询${table.comment}
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/get")
    public Result<${table.spaceName}ResponseDto> get(@RequestBody ${table.spaceName}QueryDto queryDto) {
        ${table.spaceName}Do ${table.javaName} = ${table.javaName}Service.findById(queryDto.getId());
        return CommonResult.success(${table.javaName}ConsoleConverter.toDto(${table.javaName}));
    }

    /**
     * 查询${table.comment}用列表
     *
     * @param queryDto 查询对象
     * @return List<${table.spaceName}>
     */
    @PostMapping("/list")
    public Result<List<${table.spaceName}ResponseDto>> findList(@RequestBody ${table.spaceName}QueryDto queryDto) {
        ${table.spaceName}Query query =${table.javaName}ConsoleConverter.toQuery(queryDto);
        List<${table.spaceName}Do> ${table.javaName}List = ${table.javaName}Service.findList(query);
        return CommonResult.success(${table.javaName}ConsoleConverter.toDtoList(${table.javaName}List));
    }

    /**
     * 分页查询${table.comment}列表
     *
     * @param pageQueryDto 分查询对象
     * @return List<${table.spaceName}>
     */
    @PostMapping("/page")
    public Result<Page<${table.spaceName}ResponseDto>> findPage(@RequestBody ${table.spaceName}PageQueryDto pageQueryDto) {
        ${table.spaceName}PageQuery pageQuery =${table.javaName}ConsoleConverter.toPageQuery(pageQueryDto);
        Page<${table.spaceName}Do> page = ${table.javaName}Service.findPage(pageQuery);
        return CommonResult.success(${table.javaName}ConsoleConverter.toDtoPage(page));
    }

    /**
     * 批量导入${table.comment}
     *
     * @param requestDtoList ${table.comment}数据
     * @return ${table.spaceName}
     */
    @PostMapping("/batch/create")
    public Result<Boolean> batchCreate(@RequestBody @Validated List<${table.spaceName}CreateRequestDto> requestDtoList) {
        List<${table.spaceName}Do> ${table.javaName}List =${table.javaName}ConsoleConverter.toDoList(requestDtoList);
        boolean result = ${table.javaName}Service.batchCreate(${table.javaName}List);
        return CommonResult.success(result);
    }


}
