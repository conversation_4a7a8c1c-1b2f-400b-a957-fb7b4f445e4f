package ${package}.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.util.PageHelper;
import ${package}.mapper.${table.spaceName}Mapper;
import ${package}.model.entity.${table.spaceName}Do;
import ${package}.model.query.${table.spaceName}PageQuery;
import ${package}.model.query.${table.spaceName}Query;
import ${package}.service.${table.spaceName}Service;
import java.util.List;
import java.util.Objects;

import com.linzi.pitpat.lang.Query;
import com.linzi.pitpat.framework.db.mybatis.wrapper.QueryWrapperBuilder;
import com.linzi.pitpat.framework.db.mybatis.wrapper.UpdateWrapperBuilder;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.executor.BatchResult;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 *
 * ${table.comment} 服务实现类
 *
 * @since 2024-04-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ${table.spaceName}ServiceImpl  implements ${table.spaceName}Service {
    //使用构造器初始化依赖
    private final ${table.spaceName}Mapper ${table.javaName}Mapper;

    @Override
    public Long create(${table.spaceName}Do ${table.javaName}) {
        //必要的业务检查
        int affectedRow = ${table.javaName}Mapper.insert(${table.javaName});
        log.info("创建${table.comment},user={}, affected row={}", ${table.javaName}, affectedRow);
        return ${table.javaName}.getId();
    }

    @Override
    //fixme 如果要启用缓存，这里需要更改默认缓存key
    @Caching(evict = {
            //@CacheEvict(value = UserCacheName.USER_KEY, key = "#${table.javaName}.id"),
    })
    public Long update(${table.spaceName}Do ${table.javaName}) {
        ${table.spaceName}Do existed = findById(${table.javaName}.getId());
        if (Objects.isNull(existed)) {
            throw new RuntimeException("${table.comment}不存在");
        }
        int affectedRow = ${table.javaName}Mapper.updateById(${table.javaName});
        log.info("更新${table.comment},user={}, affected row={}", ${table.javaName}, affectedRow);
        return ${table.javaName}.getId();
    }

    /**
     * @see  com.linzi.pitpat.framework.db.mybatis.wrapper.annotion.UpdateColumn
     */
    @Override
    public Long updateSelective(${table.spaceName}Do ${table.javaName}){
        UpdateWrapper<${table.spaceName}Do> wrapper = UpdateWrapperBuilder.build(${table.javaName});

        int affectedRow = ${table.javaName}Mapper.update(wrapper);
        log.info("部分更新${table.comment},user={}, affected row={}", ${table.javaName}, affectedRow);
        return ${table.javaName}.getId();
    }

    @Override
    //fixme 如果要启用缓存，这里需要更改默认缓存key
    //@CacheEvict(value = UserCacheName.USER_KEY, key = "#id")
    public boolean deleteById(Long id) {
        ${table.spaceName}Do existed = findById(id);
        if (Objects.isNull(existed)) {
            throw new RuntimeException("${table.comment}不存在");
        }
        //${table.javaName}Mapper.deleteById(id);

        LambdaUpdateWrapper<${table.spaceName}Do> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq( ${table.spaceName}Do::getId, id);
        updateWrapper.set( ${table.spaceName}Do::getIsDelete,1L);
        ${table.javaName}Mapper.update(updateWrapper);

        log.info("删除${table.comment}，id={}", id);
        return true;
    }

    @Override
    //fixme 如果要启用缓存，这里需要更改默认缓存key
    //@Cacheable(value = UserCacheName.USER_KEY, key = "#id")
    public ${table.spaceName}Do findById(Long id) {
        return ${table.javaName}Mapper.selectById(id);
    }

    @Override
    public ${table.spaceName}Do findByQuery(${table.spaceName}Query query) {
        //TODO 不建议第二个参数设置为 false, 否则如果数据异常，难以发现
        return ${table.javaName}Mapper.selectOne(buildQueryWrapper(query), false);
    }

    @Override
    public List<${table.spaceName}Do> findList(${table.spaceName}Query query) {
        Wrapper<${table.spaceName}Do> wrapper = buildQueryWrapper(query);
        List<${table.spaceName}Do> ${table.spaceName}s = ${table.javaName}Mapper.selectList(wrapper);
        log.info("查询${table.comment}列表， query={}", query);
        return ${table.spaceName}s;
    }

    @Override
    public Page<${table.spaceName}Do> findPage(${table.spaceName}PageQuery pageQuery) {
        Wrapper<${table.spaceName}Do> queryWrapper = buildQueryWrapper(pageQuery);
        Page<${table.spaceName}Do> result = ${table.javaName}Mapper.selectPage(PageHelper.ofPage(pageQuery), queryWrapper);
        log.info("查询${table.comment}列表， pageQuery={}", pageQuery);
        return result;
    }

    @Override
    public boolean batchCreate(List<${table.spaceName}Do> ${table.javaName}List) {
        //TODO 不用删掉此方法
        List<BatchResult> affectList = ${table.javaName}Mapper.insert(${table.javaName}List);
        log.info("批量新增${table.comment}列表， pageQuery={}， affect Row={}", ${table.javaName}List, affectList.size());
        return true;
    }

    @Override
    public boolean batchUpdate(List<${table.spaceName}Do> ${table.javaName}List) {
        //TODO 不用删掉此方法
         List<BatchResult> affectList = ${table.javaName}Mapper.updateById(${table.javaName}List);
        log.info("批量更新${table.comment}列表， pageQuery={}， affect Row={}", ${table.javaName}List, affectList.size());
        return true;
    }

    /**
     * 通用的条件构造器 @see https://kjdoc.yijiesudai.com/x/VAGNBw
     * if using this method ,you should delete  buildQueryWrapper(${table.spaceName}Query query) and buildQueryWrapper(${table.spaceName}PageQuery query)
     * @param query query condition
     * @return Wrapper
     * @param <Q> Q extends Query
     */
    private static <Q extends Query> Wrapper<${table.spaceName}Do> buildQueryWrapper(Q query) {
        QueryWrapper<${table.spaceName}Do> wrapper = QueryWrapperBuilder.build(query, ${table.spaceName}Do.class);
        //wrapper.last("");
        //wrapper.groupBy("");
        return wrapper;
    }

    @Deprecated
    private static Wrapper<${table.spaceName}Do> buildQueryWrapper(${table.spaceName}Query query) {
        return Wrappers.<${table.spaceName}Do>lambdaQuery()
                .eq(Objects.nonNull(query.getId()), ${table.spaceName}Do::getId, query.getId())
                .last(!CollectionUtils.isEmpty(query.getOrders()), PageHelper.ofOrderSql(query)); //排序
    }

    @Deprecated
    private static Wrapper<${table.spaceName}Do> buildQueryWrapper(${table.spaceName}PageQuery query) {
        return Wrappers.<${table.spaceName}Do>lambdaQuery()
                .eq(Objects.nonNull(query.getId()), ${table.spaceName}Do::getId, query.getId())
                .last(!CollectionUtils.isEmpty(query.getOrders()), PageHelper.ofOrderSql(query)); //排序
    }
}
