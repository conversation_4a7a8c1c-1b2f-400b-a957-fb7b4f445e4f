package ${package}.api.interfaces;

import com.linzi.pitpat.lang.Result;
import ${package}.api.dto.request.${table.spaceName}QueryDto;
import ${package}.api.dto.response.${table.spaceName}ResponseDto;
import ${package}.api.fallback.${table.spaceName}FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

 <#assign now = .now>
 <#assign date = now?date> <#--?iso_utc-->

 /**
  * value 名称与 spring.application.name 一致
  * 根据实际需要暴露需要的 feign api
  *
  * @since ${date}
  */
@FeignClient(value = "pitpat-user", path = "/api/${table.javaName}s", fallbackFactory = ${table.spaceName}FallbackFactory.class)
public interface ${table.spaceName}Api {

    /**
     * 按照 ID 查询${table.comment}
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/get")
    Result<${table.spaceName}ResponseDto> get(@RequestBody ${table.spaceName}QueryDto queryDto);
}
