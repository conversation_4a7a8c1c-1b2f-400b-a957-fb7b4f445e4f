package ${package}.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import ${package}.model.entity.${table.spaceName}Do;
import ${package}.model.query.${table.spaceName}PageQuery;
import ${package}.model.query.${table.spaceName}Query;

import java.util.List;

<#assign now = .now>
<#assign date = now?date>

/**
 * ${table.comment} 服务类
 *
 * @since ${date}
 */
public interface ${table.spaceName}Service  {

    /**
     * 新增${table.comment}
     * @param ${table.javaName}
     * @return 新增数量
     */
    Long create(${table.spaceName}Do ${table.javaName});

    /**
     * 更新${table.comment}
     * @param ${table.javaName}
     * @return 更新数量
     */
    Long update(${table.spaceName}Do ${table.javaName});

    /**
     * 可更新空值
     * @param ${table.javaName} ${table.spaceName}
     * @return 更新数量
     */
    Long updateSelective(${table.spaceName}Do ${table.javaName});

    /**
     * 删除${table.comment}
     * @param id
     * @return 影响数量
     */
    boolean deleteById(Long id);

    /**
     * 根据ID 查询${table.comment}，返回单条数据
     * @param id
     * @return
     */
    ${table.spaceName}Do findById(Long id);

    /**
     * 根据条件查询聚合活动，返回单条数据
     * @param query
     * @return
     */
    ${table.spaceName}Do findByQuery(${table.spaceName}Query query);

    /**
     * 查询${table.comment}列表
     * @param query
     * @return
     */
    List<${table.spaceName}Do> findList(${table.spaceName}Query query);

    /**
     * 分页查询${table.comment}
     * @param query
     * @return
     */
    Page<${table.spaceName}Do> findPage(${table.spaceName}PageQuery query);

    /**
     * 批量新增
     * @param ${table.javaName}List
     * @return
     */
    boolean batchCreate(List<${table.spaceName}Do> ${table.javaName}List);

    /**
     * 批量更新
     * @param ${table.javaName}List
     * @return
     */
    public boolean batchUpdate(List<${table.spaceName}Do> ${table.javaName}List);
}