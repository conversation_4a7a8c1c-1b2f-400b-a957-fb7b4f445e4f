<#if microservices == false>
    <#if console == true>
package ${package}.dto.console.request;
    <#else>
package ${package}.dto.api.request;
    </#if>
<#else>
    <#if console == true>
package ${package}.console.api.dto.request;
    <#else>
package ${package}.api.dto.request;
    </#if>
</#if>

import com.linzi.pitpat.lang.PageQuery;
import lombok.Data;

<#assign now = .now>
<#assign date = now?date> <#--?iso_utc-->
/**
 * 用户
 * Dto 转换成 query 或者 Do 的意义在于限制 api的功能，同时与 service 提功能的能力解耦，
 * 比如一个findByQuery 在 service 支持更多的业务查询，但是 api 和 console api 提供的能力不一样就可以 通过 Dto 来限制
 *
 * Request 不要简写
 * ${table.comment} 分页查询 Dto
 *
 * @since ${date}
 */
@Data
public class ${table.spaceName}PageQueryDto extends PageQuery {

    private Long id;
}
