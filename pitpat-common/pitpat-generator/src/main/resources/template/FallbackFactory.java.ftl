package ${package}.api.fallback;

import com.linzi.pitpat.core.web.CommonError;
import com.linzi.pitpat.core.web.CommonResult;
import com.linzi.pitpat.lang.Result;
import ${package}.api.dto.request.${table.spaceName}QueryDto;
import ${package}.api.dto.response.${table.spaceName}ResponseDto;
import ${package}.api.interfaces.${table.spaceName}Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;

 <#assign now = .now>
 <#assign date = now?date> <#--?iso_utc-->

 /**
  *
  * @since ${date}
  */
@Slf4j
public class ${table.spaceName}FallbackFactory implements FallbackFactory<${table.spaceName}Api> {
    @Override
    public ${table.spaceName}Api create(Throwable cause) {
        //TODO接口实现
        return new ${table.spaceName}Api() {
            @Override
            public Result<${table.spaceName}ResponseDto> get(${table.spaceName}QueryDto queryDto) {
                log.error("fallback cause, msg={}", cause.getMessage(), cause);
                return CommonResult.fail(CommonError.BUSINESS_ERROR);
            }
        };
    }
}
