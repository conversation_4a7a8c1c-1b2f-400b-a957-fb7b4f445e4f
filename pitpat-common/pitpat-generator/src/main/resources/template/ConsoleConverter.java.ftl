package ${package}.converter.console;

import com.linzi.pitpat.core.converter.BigDecimalConverter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import ${package}.model.entity.${table.spaceName}Do;
import ${package}.model.query.${table.spaceName}PageQuery;
import ${package}.model.query.${table.spaceName}Query;

<#if microservices == false>
import ${package}.dto.console.request.${table.spaceName}CreateRequestDto;
import ${package}.dto.console.request.${table.spaceName}PageQueryDto;
import ${package}.dto.console.request.${table.spaceName}QueryDto;
import ${package}.dto.console.request.${table.spaceName}UpdateRequestDto;
import ${package}.dto.console.response.${table.spaceName}ResponseDto;
<#else>
import ${package}.console.dto.request.${table.spaceName}CreateRequestDto;
import ${package}.console.dto.request.${table.spaceName}PageQueryDto;
import ${package}.console.dto.request.${table.spaceName}QueryDto;
import ${package}.console.dto.request.${table.spaceName}UpdateRequestDto;
import ${package}.console.dto.response.${table.spaceName}ResponseDto;
</#if>


import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

<#assign now = .now>
<#assign date = now?date> <#--?iso_utc-->

/**
 * ${table.comment} 管理后台 DO 转换器
 *
 * @since ${date}
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy= ReportingPolicy.IGNORE, uses = {BigDecimalConverter.class})
public interface ${table.spaceName}ConsoleConverter {

    ${table.spaceName}Do toDo(${table.spaceName}CreateRequestDto dto);

    ${table.spaceName}Do toDo(${table.spaceName}UpdateRequestDto dto);

    List<${table.spaceName}Do> toDoList(List<${table.spaceName}CreateRequestDto> dtoList);

    ${table.spaceName}ResponseDto toDto(${table.spaceName}Do ${table.javaName});

    List<${table.spaceName}ResponseDto> toDtoList(List<${table.spaceName}Do>${table.javaName}List);

    Page<${table.spaceName}ResponseDto> toDtoPage(Page<${table.spaceName}Do> page);

    ${table.spaceName}Query toQuery(${table.spaceName}QueryDto dto);

    ${table.spaceName}PageQuery toPageQuery(${table.spaceName}PageQueryDto dto);
}
