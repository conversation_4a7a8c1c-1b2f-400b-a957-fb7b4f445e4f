package ${package}.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import ${package}.model.entity.${table.spaceName}Do;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

<#assign now = .now>
<#assign date = now?date>

/**
 * ${table.comment} 数据访问对象
 *
 * @since ${date}
 */
@Mapper
public interface ${table.spaceName}Mapper extends BaseMapper<${table.spaceName}Do> {

}
