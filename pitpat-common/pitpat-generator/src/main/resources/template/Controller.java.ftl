package ${package}.controller.api;

import com.linzi.pitpat.core.web.CommonResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.lang.Result;
import ${package}.converter.api.${table.spaceName}Converter;
import ${package}.manager.api.${table.spaceName}Manager;
import ${package}.model.entity.${table.spaceName}Do;
import ${package}.model.query.${table.spaceName}PageQuery;
import ${package}.model.query.${table.spaceName}Query;
import ${package}.service.${table.spaceName}Service;

<#if microservices == false>
import ${package}.dto.api.request.${table.spaceName}CreateRequestDto;
import ${package}.dto.api.request.${table.spaceName}UpdateRequestDto;
import ${package}.dto.api.request.${table.spaceName}PageQueryDto;
import ${package}.dto.api.request.${table.spaceName}QueryDto;
import ${package}.dto.api.response.${table.spaceName}ResponseDto;
<#else>
import ${package}.api.dto.request.${table.spaceName}CreateRequestDto;
import ${package}.api.dto.request.${table.spaceName}UpdateRequestDto;
import ${package}.api.dto.request.${table.spaceName}PageQueryDto;
import ${package}.api.dto.request.${table.spaceName}QueryDto;
import ${package}.api.dto.response.${table.spaceName}ResponseDto;
</#if>

import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

<#assign now = .now>
<#assign date = now?date>

/**
 * ${table.comment} 服务类
 *
 * @since ${date}
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/${table.javaName}s")
public class ${table.spaceName}Controller {

    private final ${table.spaceName}Service ${table.javaName}Service;
    private final ${table.spaceName}Manager ${table.javaName}Manager;
    private final ${table.spaceName}Converter ${table.javaName}Converter;

    /**
     * 创建${table.comment}
     *
     * @param requestDto ${table.comment}表单
     * @return ${table.spaceName}
     */
    @PostMapping("/create")
    public Result<Long> create(@RequestBody @Validated ${table.spaceName}CreateRequestDto requestDto) {
       ${table.spaceName}Do ${table.javaName} =${table.javaName}Converter.toDo(requestDto);
        return CommonResult.success(${table.javaName}Service.create(${table.javaName}));
    }

    /**
     * 更新${table.comment}
     *
     * @param requestDto ${table.comment}表单
     * @return ${table.spaceName}
     */
    @PostMapping("/update")
    public Result<Long> update(@RequestBody @Validated ${table.spaceName}UpdateRequestDto requestDto) {
       ${table.spaceName}Do ${table.javaName} =${table.javaName}Converter.toDo(requestDto);
        return CommonResult.success(${table.javaName}Service.update(${table.javaName}));
    }

    /**
     * 按照ID 删除${table.comment}
     *
     * @param queryDto
     * @return
     */
    @PostMapping("/delete")
    public Result<Boolean> delete(@RequestBody ${table.spaceName}QueryDto queryDto) {
        boolean affected = ${table.javaName}Service.deleteById(queryDto.getId());
        return CommonResult.success(affected);
    }
    
    /**
     * 按照ID 查询${table.comment}
     *
     * @param queryDto 查询对象
     * @return
     */
    @PostMapping("/get")
    public Result<${table.spaceName}ResponseDto> findById(@RequestBody @Validated ${table.spaceName}QueryDto queryDto) {
//        ${table.spaceName}Query query = ${table.javaName}Converter.toQuery(queryDto);
        ${table.spaceName}Do ${table.javaName} = ${table.javaName}Service.findById(queryDto.getId());
        return CommonResult.success(${table.javaName}Converter.toDto(${table.javaName}));
    }

    /**
     * 查询${table.comment}用列表
     *
     * @param queryDto 查询对象
     * @return List<${table.spaceName}>
     */
    @PostMapping("/list")
    public Result<List<${table.spaceName}ResponseDto>> findList(@RequestBody ${table.spaceName}QueryDto queryDto) {
        ${table.spaceName}Query query = ${table.javaName}Converter.toQuery(queryDto);
        List<${table.spaceName}Do> ${table.javaName}List = ${table.javaName}Service.findList(query);
        log.info("${table.javaName} find list {}", ${table.javaName}List);
        return CommonResult.success(${table.javaName}Converter.toDtoList(${table.javaName}List));
    }

    /**
     * 分页查询${table.comment}列表
     *
     * @param pageQueryDto 分查询对象
     * @return List<${table.spaceName}>
     */
    @PostMapping("/page")
    public Result<Page<${table.spaceName}ResponseDto>> findPage(@RequestBody ${table.spaceName}PageQueryDto pageQueryDto) {
        ${table.spaceName}PageQuery pageQuery = ${table.javaName}Converter.toPageQuery(pageQueryDto);
        Page<${table.spaceName}Do> page = ${table.javaName}Service.findPage(pageQuery);
        log.info("${table.javaName} find page {}", page);
        return CommonResult.success(${table.javaName}Converter.toDtoPage(page));
    }
}
