package ${package}.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;
import com.linzi.pitpat.lang.BaseDo;

<#assign hasDate = 0>
<#assign hasDateTime = 0>
<#assign hasBigDecimal = 0>

<#list table.fieldList as field>
    <#if hasDate ==0 && field.javaType == 'Date'>
import java.time.ZonedDateTime;<#assign hasDate = 1>
    </#if>
    <#if hasBigDecimal ==0 && (field.javaType == 'DOUBLE' || field.javaType == 'DECIMAL' || field.javaType == 'BigDecimal')>
import java.math.BigDecimal;<#assign hasBigDecimal = 1>
    </#if>
</#list>

<#assign now = .now>
<#assign date = now?date> <#--?iso_utc-->

/**
 * ${table.comment} DO对象
 *
 * @since ${date}
 */
@Data
@Accessors(chain = true)
@TableName("${table.tableName}")
public class ${table.spaceName}Do extends BaseDo   {

<#assign superFields = ["gmtCreate", "gmtModified", "isDelete"]>
<#list table.fieldList as field>
     <#if superFields?seq_index_of(field.javaCode) == -1>
     // ${field.comment}
     <#if field.columnKey == 'PRI'>
     @TableId(value ="${field.field}", type = IdType.AUTO)
     </#if>
     private ${field.javaType} ${field.javaCode};
     </#if>
</#list>
}