package ${package}.converter.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linzi.pitpat.core.converter.BigDecimalConverter;

import ${package}.model.entity.${table.spaceName}Do;
import ${package}.model.query.${table.spaceName}PageQuery;
import ${package}.model.query.${table.spaceName}Query;

<#if microservices == false>
import ${package}.dto.api.request.${table.spaceName}CreateRequestDto;
import ${package}.dto.api.request.${table.spaceName}PageQueryDto;
import ${package}.dto.api.request.${table.spaceName}QueryDto;
import ${package}.dto.api.request.${table.spaceName}UpdateRequestDto;
import ${package}.dto.api.response.${table.spaceName}ResponseDto;
<#else>
import ${package}.api.dto.request.${table.spaceName}CreateRequestDto;
import ${package}.api.dto.request.${table.spaceName}PageQueryDto;
import ${package}.api.dto.request.${table.spaceName}QueryDto;
import ${package}.api.dto.request.${table.spaceName}UpdateRequestDto;
import ${package}.api.dto.response.${table.spaceName}ResponseDto;
</#if>


import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

<#assign now = .now>
<#assign date = now?date> <#--?iso_utc-->

/**
 * ${table.comment} DO转换器
 *
 * @since ${date}
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = {BigDecimalConverter.class})
public  interface ${table.spaceName}Converter  {

    ${table.spaceName}Do toDo(${table.spaceName}CreateRequestDto dto);

    ${table.spaceName}Do toDo(${table.spaceName}UpdateRequestDto dto);

    List<${table.spaceName}Do> toDoList(List<${table.spaceName}CreateRequestDto> dtoList);

    ${table.spaceName}ResponseDto toDto(${table.spaceName}Do ${table.javaName});

    List<${table.spaceName}ResponseDto> toDtoList(List<${table.spaceName}Do>${table.javaName}List);

    Page<${table.spaceName}ResponseDto> toDtoPage(Page<${table.spaceName}Do> page);

    ${table.spaceName}Query toQuery(${table.spaceName}QueryDto queryDto);

    ${table.spaceName}PageQuery toPageQuery(${table.spaceName}PageQueryDto pageQueryDto);
}
