<#if microservices == false>
    <#if console == true>
package ${package}.dto.console.response;
    <#else>
package ${package}.dto.api.response;
    </#if>
<#else>
    <#if console == true>
package ${package}.console.api.dto.response;
    <#else>
package ${package}.api.dto.response;
    </#if>
</#if>

import java.io.Serializable;

import lombok.Data;

/**
 * 返回对象有各种定义，BO,VO,DTO 等等
 * 用户响应对象，Response 不要简写
 */
@Data
public class ${table.spaceName}ResponseDto implements Serializable {

    private Long id;
}
