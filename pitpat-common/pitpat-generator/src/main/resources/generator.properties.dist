#分包,true;不分包,false
generate.module.multilayer=false
# 是否微服务，兼容 pitpat-data
generate.module.microservices=false
#不分包，直接指定完整报名
generate.package=com.linzi.pitpat.user
# --
#分包，包名不包含module 名称 比如 com.linzi.pitpat.user, com.linzi.pitpat.payment,设置为 com.linzi.pitpat既可以
#generate.package=com.linzi.pitpat

# -- api dto
generate.api-module=user-api
generate.api-module.java-path=src.main.java.%s.api
# -- console dto
generate.console-api-module=user-console-api
generate.console-api-module.java-path=src.main.java.%s.console.api
# -- service
generate.service-module=user-service
generate.service-module.java-path=src.main.java.%s
generate.service-module.resource-path=src.main.resources


