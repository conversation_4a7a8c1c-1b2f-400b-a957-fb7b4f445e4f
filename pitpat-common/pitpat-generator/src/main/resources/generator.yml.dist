generate:
  multilayer: false   #分包,true;不分包,false
  microservices: false # 是否微服务，否的话 忽略 generate.api-module， generate.console-api-module 属性
  package: com.linzi.pitpat.user #不分包，直接指定完整报名
  #packagePath=com.linzi.pitpat   #分包，包名不包含module 名称 比如 com.linzi.pitpat.user, com.linzi.pitpat.payment,设置为 com.linzi.pitpat既可以

  api-module: # api feign
    name: user-api
    java-path: src/main/java/%s/api

  console-api-module: # console-api feign
    name: user-console-api
    java-path: src/main/java/%s/console/api

  service-module:  # service
    name: user-service
    java-path: src/main/java/%s
    resource-path: src/main/resources

dataSource: # 数据库连接信息
  url: *************************************************************
  database: pitpat
  username: pitpat
  password: Er4f@0*G
