package com.linzi.pitpat.entity;

import java.util.Objects;

public enum FileType {
    SERVICE_FILE(1),
    SERVICE_RESOURCE_FILE(2),
    DTO_FILE(3),
    API_DTO_FILE(4),
    CONSOLE_DTO_FILE(5);

    private static final FileType[] VALUES;

    static {
        VALUES = values();
    }

    public void setFileType(Integer fileType) {
        this.fileType = fileType;
    }

    public Integer getFileType() {
        return fileType;
    }

    private Integer fileType;


    FileType(int fileType) {
        this.fileType = fileType;
    }

    public static FileType resolve(int fileType) {
        for (FileType item : VALUES) {
            if (Objects.equals(item.getFileType(), fileType)) {
                return item;
            }
        }
        return null;
    }
}
