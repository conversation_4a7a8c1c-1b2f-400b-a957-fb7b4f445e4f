package com.linzi.pitpat.generator;

import com.linzi.pitpat.entity.CodeModule;
import com.linzi.pitpat.entity.TablesBean;
import com.linzi.pitpat.util.PathResolver;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 根据数据表表信息生成 Model对象
 */
@Slf4j
public class ModelGenerator extends BaseGenerator {

    public List<CodeModule> codeModules = List.of(CodeModule.DO_MODEL, CodeModule.QUERY_MODEL, CodeModule.PAGE_QUERY_MODEL);

    /**
     * 重新生成Do
     *
     * @param table
     * @param packageName
     */
    public void patch(TablesBean table, String packageName) {
        parseTpl(packageName, CodeModule.DO_MODEL, table);
    }

    // v2 接口
    public void generate(TablesBean table, String packageName) {
        codeModules.forEach(module -> parseTpl(packageName, module, table));
    }

    @Override
    protected String getFilePath(String modelName, CodeModule codeModule, TablesBean table) {
        return PathResolver.getServiceFilePath(table, codeModule, modelName);
    }
}