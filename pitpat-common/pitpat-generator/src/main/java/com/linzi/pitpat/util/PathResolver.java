package com.linzi.pitpat.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.linzi.pitpat.config.Configuration;
import com.linzi.pitpat.entity.CodeModule;
import com.linzi.pitpat.entity.TablesBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.util.ClassUtils;
import org.springframework.util.StringUtils;
import org.yaml.snakeyaml.Yaml;

import java.io.InputStream;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Properties;

@Slf4j
public class PathResolver {

    private static Properties properties;

    /**
     * read generator.properties
     *
     * @return Properties
     */
    static {
        //Properties properties = null;
        //try {
        //    properties = PropertiesLoaderUtils.loadAllProperties("generator.properties");
        //} catch (IOException e) {
        //    log.info("Error when read properties: {}", e.getMessage(), e);
        //}
        //Configuration result = parseYml();
        //log.info("yml={}", result);
        //return properties;
    }

    //public static Properties readProperty() {
    //    if (Objects.nonNull(properties)) {
    //        return properties;
    //    }
    //    return null;
    //}


    public static Configuration parseYml() {
        Yaml yaml = new Yaml();

        ClassLoader classLoaderToUse = ClassUtils.getDefaultClassLoader();

        Assert.notNull(classLoaderToUse, "ClassLoader name must not be null");
        InputStream inputStream = classLoaderToUse.getClass().getResourceAsStream("generator.yml");
        if (Objects.isNull(inputStream)) {
            inputStream = ClassLoader.getSystemResourceAsStream("generator.yml");
        }
        if (Objects.isNull(inputStream)) {
            return null;
        }
        Map<String, Object> obj = yaml.load(inputStream);
        return new ObjectMapper().convertValue(obj, Configuration.class);
    }


    /**
     * service 模块的 java 文件路径
     *
     * @param table      mysql 表配置
     * @param codeModule java 文件配置
     * @param moduleName 模块名称
     * @return 文件路径
     */
    public static String getServiceFilePath(TablesBean table, CodeModule codeModule, String moduleName) {
        Configuration configuration = parseYml();
        Configuration.GenerateConfig generate = configuration.getGenerate();
        Configuration.ModuleConfig serviceModule = generate.getServiceModule();

        String javaPath = serviceModule.getJavaPath();
        String name = serviceModule.getName();
        //String modulePath = readProperty().getProperty("generate.service-module");
        String packagePath = String.format(javaPath, getPackageName(moduleName));
        return String.format("%s%s%s%s/%s%s", getContextPath(), name, packageConvertPath(packagePath),
                codeModule.getPathName(), table.getSpaceName(), codeModule.getTplName().substring(0, codeModule.getTplName().length() - 4));
    }


    /**
     * resource 模块的 resource 文件路径
     *
     * @param table      mysql 表配置
     * @param codeModule java 文件配置
     * @param moduleName 模块名称
     * @return 文件路径
     */
    public static String getResourceFilePath(TablesBean table, CodeModule codeModule, String moduleName) {
        Configuration configuration = parseYml();
        Configuration.GenerateConfig generate = configuration.getGenerate();
        Configuration.ModuleConfig serviceModule = generate.getServiceModule();

        return String.format("%s%s%s%s/%s%s", getContextPath(), serviceModule.getName(),
                packageConvertPath(serviceModule.getResourcePath()), codeModule.getPathName(), table.getSpaceName(),
                codeModule.getTplName().substring(0, codeModule.getTplName().length() - 4));
    }

    /**
     * api 模块的 resource 文件路径
     *
     * @param table      mysql 表配置
     * @param codeModule java 文件配置
     * @param moduleName 模块名称
     * @return 文件路径
     */
    public static String getApiFilePath(TablesBean table, CodeModule codeModule, String moduleName) {
        Configuration configuration = parseYml();
        Configuration.GenerateConfig generate = configuration.getGenerate();
        Configuration.ModuleConfig apiModule = generate.getApiModule();

        //String modulePath = readProperty().getProperty("generate.api-module");
        String packagePath = String.format(apiModule.getJavaPath(), getPackageName(apiModule.getName()));

        return String.format("%s%s%s%s/%s%s", getContextPath(), apiModule.getName(), packageConvertPath(packagePath),
                codeModule.getPathName(), table.getSpaceName(), codeModule.getTplName().substring(0, codeModule.getTplName().length() - 4));
    }

    /**
     * console 模块的 resource 文件路径
     *
     * @param table      mysql 表配置
     * @param codeModule java 文件配置
     * @param moduleName 模块名称
     * @return 文件路径
     */
    public static String getConsoleFilePath(TablesBean table, CodeModule codeModule, String moduleName) {
        Configuration configuration = parseYml();
        Configuration.GenerateConfig generate = configuration.getGenerate();
        Configuration.ModuleConfig consoleModule = generate.getApiModule();

        //String modulePath = readProperty().getProperty("generate.console-api-module");
        String modulePath = consoleModule.getName();
        String packagePath = String.format(consoleModule.getJavaPath(), getPackageName(moduleName));

        return String.format("%s%s%s%s/%s%s", getContextPath(), modulePath, packageConvertPath(packagePath),
                codeModule.getPathName(), table.getSpaceName(), codeModule.getTplName().substring(0, codeModule.getTplName().length() - 4));
    }


    /**
     * 根据配置获取包名
     *
     * @param moduleName 模块名，如果分包则需要指定
     * @return packageName
     */
    public static String getPackageName(String moduleName) {
        Configuration configuration = parseYml();
        Configuration.GenerateConfig generate = configuration.getGenerate();
        String packageName = generate.getPackagePath();//readProperty().getProperty("generate.package");
        //分包
        if (isMultiLayer()) {
            if (!StringUtils.hasText(moduleName)) {
                throw new RuntimeException("当前生成模式：【分包】,请指定 【moduleName】");
            }
            packageName += String.format(".%s", moduleName);
        }
        return packageName;
    }

    /**
     * 包是否分层
     *
     * @return
     */
    public static boolean isMultiLayer() {
        Configuration configuration = parseYml();
        Configuration.GenerateConfig generate = configuration.getGenerate();

        return Objects.equals(generate.getMultilayer(), true);
    }

    /**
     * 是否微服务，否的话不生成 api 和 console-api 模块
     *
     * @return
     */
    public static boolean isMicroservices() {
        Configuration configuration = parseYml();
        Configuration.GenerateConfig generate = configuration.getGenerate();

        return Objects.equals(generate.getMicroservices(), true);

    }
    //---------------------------------------------------------------------------------------------------------------------------

    /**
     * 将包名转化为文件路径
     *
     * @param packageName 包引用路径
     * @return 包文件路径
     */
    private static String packageConvertPath(String packageName) {
        return String.format("/%s/", packageName.contains(".") ? packageName.replaceAll("\\.", "/") : packageName);
    }

    /**
     * 获取当前模块的绝对路径
     *
     * @return 文件绝对路径
     */
    private static String getContextPath() {
        String projectPath = System.getProperty("user.dir");

        Configuration configuration = parseYml();
        Configuration.GenerateConfig generate = configuration.getGenerate();
        Configuration.ModuleConfig serviceModule = generate.getServiceModule();

        Optional<String> optional = Arrays.stream(projectPath.split(serviceModule.getName())).findFirst();
        if (optional.isEmpty()) {
            log.error("fail to find parent module path");
            throw new RuntimeException("无法获取模块路径");
        }
        log.info("contextPah = {}", Objects.equals(projectPath, optional.get()));
        return optional.get();
    }


}
