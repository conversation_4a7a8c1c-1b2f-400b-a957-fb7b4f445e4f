package com.linzi.pitpat.generator;


import com.linzi.pitpat.entity.CodeModule;
import com.linzi.pitpat.entity.TablesBean;
import com.linzi.pitpat.util.PathResolver;

import java.util.List;

/**
 * service 聚合层器生成对象
 */
public class ManagerGenerator extends BaseGenerator {

    public List<CodeModule> codeModules = List.of(CodeModule.API_MANAGER, CodeModule.CONSOLE_MANAGER);

    // v2 接口
    public void generate(TablesBean table, String packageName) {
        //if(PathResolver.isMicroservices()){
            codeModules.forEach(module -> parseTpl(packageName, module, table));
        //}else {
        //    parseTpl(packageName, CodeModule.MANAGER, table);
        //}
    }

    @Override
    protected String getFilePath(String modelName, CodeModule codeModule, TablesBean table) {
        return PathResolver.getServiceFilePath(table, codeModule, modelName);
    }

}
