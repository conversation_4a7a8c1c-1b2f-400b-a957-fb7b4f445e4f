package com.linzi.pitpat.generator;


import com.linzi.pitpat.entity.CodeModule;
import com.linzi.pitpat.entity.FileType;
import com.linzi.pitpat.entity.TablesBean;
import com.linzi.pitpat.util.PathResolver;

import java.util.List;
import java.util.Objects;

/**
 * 生成 feign 接口以及fallback默认是嫌累
 */
public class ApiGenerator extends BaseGenerator {

    public List<CodeModule> codeModules = List.of(CodeModule.API, CodeModule.CONSOLE_API, CodeModule.API_FALLBACK,
            CodeModule.CONSOLE_API_FALLBACK);

    public void generate(TablesBean table, String packageName) {
        if(PathResolver.isMicroservices()){
            codeModules.forEach(module -> parseTpl(packageName, module, table));
        }
    }

    //这里需要重新里文件类型
    @Override
    protected String getFilePath(String modelName, CodeModule codeModule, TablesBean table) {
        if (Objects.equals(codeModule.getFileType(), FileType.CONSOLE_DTO_FILE.getFileType())) {
            return PathResolver.getConsoleFilePath(table, codeModule, modelName);
        }
        return PathResolver.getApiFilePath(table, codeModule, modelName);
    }
}
