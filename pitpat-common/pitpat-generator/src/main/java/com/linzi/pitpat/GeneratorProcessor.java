package com.linzi.pitpat;

import com.linzi.pitpat.entity.FieldBean;
import com.linzi.pitpat.entity.TablesBean;
import com.linzi.pitpat.generator.ApiGenerator;
import com.linzi.pitpat.generator.BaseGenerator;
import com.linzi.pitpat.generator.ControllerGenerator;
import com.linzi.pitpat.generator.ConverterGenerator;
import com.linzi.pitpat.generator.DtoGenerator;
import com.linzi.pitpat.generator.ManagerGenerator;
import com.linzi.pitpat.generator.MapperGenerator;
import com.linzi.pitpat.generator.ModelGenerator;
import com.linzi.pitpat.generator.ServiceGenerator;
import com.linzi.pitpat.util.MysqlTableUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/***
 * 预发布环境回调接口地址：
 https://pre-callback.sinawallent.com/platform/callback
 https://pre-callback.sinawallent.com/platformweb/query
 */
@Slf4j
public class GeneratorProcessor {

    private List<TablesBean> readTables(List<String> tableNameList) {
        Map<String, String> map = MysqlTableUtil.getComments();
        return tableNameList.stream().map(item -> {
            TablesBean table = new TablesBean(item);
            String tableName = table.getTableName();
            List<FieldBean> itemList = MysqlTableUtil.readTableDetail(tableName);
            table.setFieldList(itemList);
            table.setComment(map.get(tableName));
            return table;
        }).toList();
    }

    public void generate(String packageName, List<String> tableNameList) {
        //默认值
        generate(packageName, tableNameList, List.of("model", "service", "mapper"));
    }

    /**
     * @param packageName
     * @param tableNameList
     * @param modelList
     */
    public void generate(String packageName, List<String> tableNameList, List<String> modelList) {
        //所有模型生成配置
        //List<String> fullModelList = List.of("model", "service", "mapper", "converter", "controller", "manager", "dto", "api");

        List<TablesBean> tables = readTables(tableNameList);

        ControllerGenerator controller = new ControllerGenerator();
        ConverterGenerator converter = new ConverterGenerator();
        ManagerGenerator manager = new ManagerGenerator();
        ServiceGenerator service = new ServiceGenerator();
        ModelGenerator model = new ModelGenerator();
        MapperGenerator mapper = new MapperGenerator();
        DtoGenerator dto = new DtoGenerator();
        ApiGenerator api = new ApiGenerator();

        if (CollectionUtils.isEmpty(modelList)) {
            modelList = List.of("model", "service", "mapper");
        }
        Map<String, BaseGenerator> modelMap = Map.of("model", model, "service", service, "mapper", mapper, "converter", converter, "controller", controller, "manager", manager, "dto", dto, "api", api);

        List<BaseGenerator> enableList = modelList.stream().map(modelMap::get).toList();
        enableList.forEach(generator -> tables
                .forEach(table -> generator.generate(table, packageName)));
    }

    public void patchDo(String packageName, List<String> tableNameList) {
        List<TablesBean> tables = readTables(tableNameList);

        ModelGenerator modelGenerator = new ModelGenerator();
        tables.forEach(table -> modelGenerator.patch(table, packageName));
    }

}
