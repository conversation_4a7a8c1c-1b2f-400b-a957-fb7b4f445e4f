package com.linzi.pitpat.config;

import lombok.Data;

@Data
public class Configuration {

    private GenerateConfig generate;
    private DataSourceConfig dataSource;

    @Data
    public static class GenerateConfig {

        private Boolean multilayer;
        private Boolean microservices;
        private String packagePath;
        private ModuleConfig apiModule;
        private ModuleConfig consoleApiModule;
        private ModuleConfig serviceModule;
    }

    @Data
    public static class ModuleConfig {

        private String name;
        private String javaPath;
        private String resourcePath;
    }

    @Data
    public static class DataSourceConfig {

        private String url;
        private String database;
        private String username;
        private String password;
    }
}
