package com.linzi.pitpat.generator;


import com.linzi.pitpat.entity.CodeModule;
import com.linzi.pitpat.entity.FileType;
import com.linzi.pitpat.entity.TablesBean;
import com.linzi.pitpat.util.PathResolver;

import java.util.List;
import java.util.Objects;

/**
 * 转换器生成对象, 将 dto 拆分为 api 与 console 两个。好处是，更加灵活，baseGenerator 逻辑更加通用，不用为了兼容导致业务处理兼容处理非通用逻辑
 */
public class DtoGenerator extends BaseGenerator {

    public List<CodeModule> codeModules = List.of(
            CodeModule.CREATE_REQUEST_DTO_REQUEST, CodeModule.UPDATE_REQUEST_DTO_REQUEST, CodeModule.DELETE_REQUEST_DTO_REQUEST,
            CodeModule.QUERY_DTO_REQUEST, CodeModule.PAGE_QUERY_DTO_REQUEST, CodeModule.RESPONSE_DTO);

    public List<CodeModule> apiCodeModules = List.of(
            CodeModule.API_CREATE_REQUEST_DTO_REQUEST, CodeModule.API_UPDATE_REQUEST_DTO_REQUEST, CodeModule.API_DELETE_REQUEST_DTO_REQUEST,
            CodeModule.API_QUERY_DTO_REQUEST, CodeModule.API_PAGE_QUERY_DTO_REQUEST, CodeModule.API_RESPONSE_DTO);

    public List<CodeModule> consoleCodeModules = List.of(
            CodeModule.CONSOLE_CREATE_REQUEST_DTO_REQUEST, CodeModule.CONSOLE_UPDATE_REQUEST_DTO_REQUEST, CodeModule.CONSOLE_DELETE_REQUEST_DTO_REQUEST,
            CodeModule.CONSOLE_QUERY_DTO_REQUEST, CodeModule.CONSOLE_PAGE_QUERY_DTO_REQUEST, CodeModule.CONSOLE_RESPONSE_DTO);

    public void generate(TablesBean table, String packageName) {
        if (PathResolver.isMicroservices()) {
            generateApiDto(table, packageName);
            generateConsoleApiDto(table, packageName);
        } else {
            apiCodeModules.forEach(module -> {
                module.setFileType(FileType.API_DTO_FILE.getFileType());
                parseTpl(packageName, module, table);
            });
            consoleCodeModules.forEach(module -> {
                module.setFileType(FileType.CONSOLE_DTO_FILE.getFileType());
                parseTpl(packageName, module, table);
            });
        }
    }

    private void generateApiDto(TablesBean table, String packageName) {
        codeModules.forEach(module -> {
            module.setFileType(FileType.API_DTO_FILE.getFileType());
            parseTpl(packageName, module, table);
        });
    }

    private void generateConsoleApiDto(TablesBean table, String packageName) {
        codeModules.forEach(module -> {
            module.setFileType(FileType.CONSOLE_DTO_FILE.getFileType());
            parseTpl(packageName, module, table);
        });
    }

    //这里需要重新里文件类型
    @Override
    protected String getFilePath(String modelName, CodeModule codeModule, TablesBean table) {
        if (PathResolver.isMicroservices()) {
            if (Objects.equals(codeModule.getFileType(), FileType.API_DTO_FILE.getFileType())) {
                return PathResolver.getApiFilePath(table, codeModule, modelName);
            } else  {
                return PathResolver.getConsoleFilePath(table, codeModule, modelName);
            }
        } else {
            if (Objects.equals(codeModule.getFileType(), FileType.API_DTO_FILE.getFileType())) {
                return PathResolver.getServiceFilePath(table, codeModule, modelName);
            } else  {
                return PathResolver.getServiceFilePath(table, codeModule, modelName);
            }
        }
    }
}
