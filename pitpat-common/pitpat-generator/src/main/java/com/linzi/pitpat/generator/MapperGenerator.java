package com.linzi.pitpat.generator;

import com.linzi.pitpat.entity.CodeModule;
import com.linzi.pitpat.entity.FileType;
import com.linzi.pitpat.entity.TablesBean;
import com.linzi.pitpat.util.PathResolver;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

/**
 * 根据数据表表信息生成Mapper对象
 */
@Slf4j
public class MapperGenerator extends BaseGenerator {

    public List<CodeModule> codeModules = List.of(CodeModule.MAPPER, CodeModule.MAPPER_XML);

    // v2 接口
    public void generate(TablesBean table, String packageName) {
        codeModules.forEach(module -> parseTpl(packageName, module, table));
    }

    @Override
    protected String getFilePath(String modelName, CodeModule codeModule, TablesBean table) {
        if (Objects.equals(FileType.SERVICE_RESOURCE_FILE.getFileType(), codeModule.getFileType())) {
            return PathResolver.getResourceFilePath(table, codeModule, modelName);
        }
        return PathResolver.getServiceFilePath(table, codeModule, modelName);
    }


}
