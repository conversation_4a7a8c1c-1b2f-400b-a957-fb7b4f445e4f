package com.linzi.pitpat.generator;


import com.linzi.pitpat.entity.CodeModule;
import com.linzi.pitpat.entity.TablesBean;
import com.linzi.pitpat.util.PathResolver;

import java.util.List;

/**
 * 转换器生成对象
 */
public class ConverterGenerator extends BaseGenerator {

    public List<CodeModule> codeModules = List.of(CodeModule.API_CONVERTER, CodeModule.CONSOLE_CONVERTER);

    public void generate(TablesBean table, String packageName) {
        //if (PathResolver.isMicroservices()) {
            codeModules.forEach(module -> parseTpl(packageName, module, table));
        //} else {
        //    parseTpl(packageName, CodeModule.API_CONVERTER, table);
        //}
    }

    @Override
    protected String getFilePath(String modelName, CodeModule codeModule, TablesBean table) {
        return PathResolver.getServiceFilePath(table, codeModule, modelName);
    }
}
