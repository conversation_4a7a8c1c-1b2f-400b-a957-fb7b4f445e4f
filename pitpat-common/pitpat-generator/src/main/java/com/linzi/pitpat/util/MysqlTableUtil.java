package com.linzi.pitpat.util;

import com.linzi.pitpat.config.Configuration;
import com.linzi.pitpat.entity.FieldBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class MysqlTableUtil {

    public static List<FieldBean> readTableDetail(String tableName) {
        Configuration configuration = PathResolver.parseYml();

        Assert.notNull(configuration, "代码生成器配置为空");
        Configuration.DataSourceConfig dataSource = configuration.getDataSource();
        //Map<String, Object> config = PathResolver.parseYml();
        // 驱动程序名
        String driver = "com.mysql.cj.jdbc.Driver";
        String url = dataSource.getUrl();// properties.getProperty("datasource.url");
        // MySQL配置时的用户名
        String user = dataSource.getUsername();// properties.getProperty("datasource.username");
        // Java连接MySQL配置时的密码
        String password = dataSource.getPassword();//  properties.getProperty("datasource.password");
        //数据库名称
        String database = dataSource.getDatabase();//properties.getProperty("datasource.database");

        List<FieldBean> list = new ArrayList<FieldBean>();

        try {

            // 加载驱动程序
            Class.forName(driver);

            // 连续数据库
            Connection conn = DriverManager.getConnection(url, user, password);

            if (!conn.isClosed()) {

            }

            // statement用来执行SQL语句
            Statement statement = conn.createStatement();

            // 要执行的SQL语句
            String sql = "SELECT COLUMN_NAME columnName, DATA_TYPE dataType, COLUMN_COMMENT columnComment, column_key FROM INFORMATION_SCHEMA.COLUMNS " + "WHERE table_name = '" + tableName
                    + "' AND table_schema = '" + database + "' order by ORDINAL_POSITION;";

            ResultSet rs = statement.executeQuery(sql);

            while (rs.next()) {

                // 选择sname这列数据
                String columnName = rs.getString("columnName");
                String dataType = rs.getString("dataType");
                String columnComment = rs.getString("columnComment");
                String columnKey = rs.getString("column_key");
                FieldBean bean = new FieldBean(columnName, dataType, columnComment, columnKey);
                list.add(bean);
            }
            rs.close();
            conn.close();
        } catch (ClassNotFoundException e) {
            log.error("Sorry,can`t find the Driver!", e);
        } catch (Exception e) {
            log.error("连接数据库报错", e);
        }

        return list;
    }

    public static Map<String, String> getComments() {
        Configuration configuration = PathResolver.parseYml();

        Assert.notNull(configuration, "代码生成器配置为空");
        Configuration.DataSourceConfig dataSource = configuration.getDataSource();


        //Properties properties = PathResolver.readProperty();
        Map<String, String> map = new HashMap<>();
        // 驱动程序名
        String driver = "com.mysql.cj.jdbc.Driver";
        String url = dataSource.getUrl();//properties.getProperty("datasource.url");
        // MySQL配置时的用户名
        String user = dataSource.getUsername();// properties.getProperty("datasource.username");
        // Java连接MySQL配置时的密码
        String password = dataSource.getPassword();//properties.getProperty("datasource.password");

        try {
            // 加载驱动程序
            Class.forName(driver);
            // 连续数据库
            Connection conn = DriverManager.getConnection(url, user, password);

            if (!conn.isClosed()) {

            }
            // statement用来执行SQL语句
            Statement statement = conn.createStatement();
            // 要执行的SQL语句
            String sql = "show table status from  " + dataSource.getDatabase();//properties.getProperty("datasource.database");
            ResultSet rs = statement.executeQuery(sql);
            while (rs.next()) {
                // 选择sname这列数据
                String key = rs.getString("Name");
                String value = rs.getString("Comment");
                map.put(key, value);
            }
            rs.close();
            conn.close();
        } catch (SQLException | ClassNotFoundException e) {
            log.info("获取数据库信息失败，{}", e.getMessage());
        }
        return map;
    }

}
