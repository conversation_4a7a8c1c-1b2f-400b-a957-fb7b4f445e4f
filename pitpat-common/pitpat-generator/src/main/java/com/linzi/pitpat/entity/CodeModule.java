package com.linzi.pitpat.entity;

import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

@Getter
public enum CodeModule {
    CONTROLLER("Controller.java.ftl", "controller", "", 1),
    API_CONTROLLER("Controller.java.ftl", "controller/api", "", 1),
    CONSOLE_CONTROLLER("ConsoleController.java.ftl", "controller/console", "", 1),

    CONVERTER("Converter.java.ftl", "converter", "", 1),
    API_CONVERTER("Converter.java.ftl", "converter/api", "", 1),
    CONSOLE_CONVERTER("ConsoleConverter.java.ftl", "converter/console", "", 1),

    MANAGER("Manager.java.ftl", "manager", "", 1),
    API_MANAGER("Manager.java.ftl", "manager/api", "", 1),
    CONSOLE_MANAGER("ConsoleManager.java.ftl", "manager/console", "", 1),

    DO_MODEL("Do.java.ftl", "model/entity", "", 1),
    QUERY_MODEL("Query.java.ftl", "model/query", "", 1),
    PAGE_QUERY_MODEL("PageQuery.java.ftl", "model/query", "", 1),

    MAPPER("Mapper.java.ftl", "mapper", "", 1),
    MAPPER_XML("Mapper.xml.ftl", "mapper", "", 2),

    SERVICE("Service.java.ftl", "service", "", 1),
    SERVICE_IMP("ServiceImpl.java.ftl", "service/impl", "", 1),

    API("Api.java.ftl", "interfaces", "", 4),
    CONSOLE_API("ConsoleApi.java.ftl", "interfaces", "", 5),

    API_FALLBACK("FallbackFactory.java.ftl", "fallback", "", 4),
    CONSOLE_API_FALLBACK("ConsoleFallbackFactory.java.ftl", "fallback", "", 5),

    QUERY_DTO_REQUEST("QueryDto.java.ftl", "dto/request", "", 3),
    PAGE_QUERY_DTO_REQUEST("PageQueryDto.java.ftl", "dto/request", "", 3),
    CREATE_REQUEST_DTO_REQUEST("CreateRequestDto.java.ftl", "dto/request", "", 3),
    UPDATE_REQUEST_DTO_REQUEST("UpdateRequestDto.java.ftl", "dto/request", "", 3),
    DELETE_REQUEST_DTO_REQUEST("DeleteRequestDto.java.ftl", "dto/request", "", 3),
    RESPONSE_DTO("ResponseDto.java.ftl", "dto/response", "", 3),

    API_QUERY_DTO_REQUEST("QueryDto.java.ftl", "dto/api/request", "", 3),
    API_PAGE_QUERY_DTO_REQUEST("PageQueryDto.java.ftl", "dto/api/request", "", 3),
    API_CREATE_REQUEST_DTO_REQUEST("CreateRequestDto.java.ftl", "dto/api/request", "", 3),
    API_UPDATE_REQUEST_DTO_REQUEST("UpdateRequestDto.java.ftl", "dto/api/request", "", 3),
    API_DELETE_REQUEST_DTO_REQUEST("DeleteRequestDto.java.ftl", "dto/api/request", "", 3),
    API_RESPONSE_DTO("ResponseDto.java.ftl", "dto/api/response", "", 3),

    CONSOLE_QUERY_DTO_REQUEST("QueryDto.java.ftl", "dto/console/request", "", 3),
    CONSOLE_PAGE_QUERY_DTO_REQUEST("PageQueryDto.java.ftl", "dto/console/request", "", 3),
    CONSOLE_CREATE_REQUEST_DTO_REQUEST("CreateRequestDto.java.ftl", "dto/console/request", "", 3),
    CONSOLE_UPDATE_REQUEST_DTO_REQUEST("UpdateRequestDto.java.ftl", "dto/console/request", "", 3),
    CONSOLE_DELETE_REQUEST_DTO_REQUEST("DeleteRequestDto.java.ftl", "dto/console/request", "", 3),
    CONSOLE_RESPONSE_DTO("ResponseDto.java.ftl", "dto/console/response", "", 3);

    private static final CodeModule[] VALUES;

    static {
        VALUES = values();
    }

    /**
     * 文件全路径，需要根据配置才能组装成完整路径
     */
    @Setter
    private String path;
    /**
     * 文件前缀路径
     */
    private String pathName;
    /**
     * 模板名称
     */
    private String tplName;
    /**
     * 文件类型，主要区分 java 文件、资源文件，以及相同名字不同路径的 feign 接口相关的java 文件
     */
    @Setter
    private Integer fileType;

    CodeModule(String tplName, String pathName, String path, int fileType) {
        this.path = path;
        this.tplName = tplName;
        this.pathName = pathName;
        this.fileType = fileType;
    }

    public static CodeModule resolve(String tplName) {
        for (CodeModule item : VALUES) {
            if (Objects.equals(item.getTplName(), tplName) || Objects.equals(item.getPathName(), tplName)) {
                return item;
            }
        }
        return null;
    }
}
