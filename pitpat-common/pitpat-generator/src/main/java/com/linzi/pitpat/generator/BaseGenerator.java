package com.linzi.pitpat.generator;

import com.linzi.pitpat.entity.CodeModule;
import com.linzi.pitpat.entity.FileType;
import com.linzi.pitpat.entity.TablesBean;
import com.linzi.pitpat.util.PathResolver;
import freemarker.template.Configuration;
import freemarker.template.TemplateExceptionHandler;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileWriter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
public abstract class BaseGenerator {

    /**
     * 代码生成逻辑
     *
     * @param table
     * @param packageName
     */
    public abstract void generate(TablesBean table, String packageName);

    /**
     * 获取文件生成路径
     *
     * @param modelName
     * @param codeModule
     * @param table
     * @return
     */
    protected abstract String getFilePath(String modelName, CodeModule codeModule, TablesBean table);

    /**
     * 解析模板
     *
     * @param modelName  报名
     * @param codeModule 模板名称
     * @param table      数据库表信息
     */
    protected void parseTpl(String modelName, CodeModule codeModule, TablesBean table) {
        try {
            Configuration cfg = getConfiguration();

            Map<String, Object> data = new HashMap<>();
            data.put("table", table);
            data.put("console", Objects.equals(codeModule.getFileType(), FileType.CONSOLE_DTO_FILE.getFileType()));
            data.put("package", PathResolver.getPackageName(modelName));
            data.put("microservices", PathResolver.isMicroservices());

            String pathName = getFilePath(modelName, codeModule, table);
            File file = new File(pathName);
            if (!file.getParentFile().exists()) {
                boolean success = file.getParentFile().mkdirs();
                if (!success) {
                    log.error("pathName ={}", pathName);
                }
            }
            cfg.getTemplate(codeModule.getTplName()).process(data, new FileWriter(file));
            log.info("generated file={}, path: {}", codeModule.getTplName(), file.getAbsolutePath());

        } catch (Exception e) {
            log.error("Fail to generate {}", e.getMessage(), e);
        }
    }

    /**
     * 获取 freemarker 配置信息
     *
     * @return
     */
    protected Configuration getConfiguration() {
        Configuration cfg = new Configuration(Configuration.VERSION_2_3_32);

        //https://blog.csdn.net/u011047968/article/details/107311462
        //https://blog.csdn.net/zhangphil/article/details/127127445
        //cfg.setDirectoryForTemplateLoading(resource.getFile()); 打包作为maven 依赖查找路径会有问题
        cfg.setClassLoaderForTemplateLoading(getClass().getClassLoader(), "/template/");
        cfg.setDefaultEncoding("UTF-8");
        cfg.setTemplateExceptionHandler(TemplateExceptionHandler.IGNORE_HANDLER);
        return cfg;
    }

}
