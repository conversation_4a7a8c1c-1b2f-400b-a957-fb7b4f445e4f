package com.linzi.pitpat.generator;

import com.linzi.pitpat.entity.CodeModule;
import com.linzi.pitpat.entity.TablesBean;
import com.linzi.pitpat.util.PathResolver;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 根据数据表表信息生成 Service对象
 */
@Slf4j
public class ServiceGenerator extends BaseGenerator {

    public List<CodeModule> codeModules = List.of(CodeModule.SERVICE, CodeModule.SERVICE_IMP);

    // v2 接口
    public void generate(TablesBean table, String packageName) {
        codeModules.forEach(module -> parseTpl(packageName, module, table));
    }

    @Override
    protected String getFilePath(String modelName, CodeModule codeModule, TablesBean table) {
        return PathResolver.getServiceFilePath(table, codeModule, modelName);
    }
}
