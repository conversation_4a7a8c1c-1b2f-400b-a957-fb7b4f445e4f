<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
        <relativePath/>
    </parent>

    <groupId>com.linzi.pitpat</groupId>
    <artifactId>pitpat-framework</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <properties>
        <!--测试用0.3.x-->
        <revision>0.3.1-SNAPSHOT</revision>
        <!--稳定版用0.2.x-->
        <!--<revision>0.2.4-SNAPSHOT</revision>-->
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>

        <!--BOM依赖版本-->
        <spring-boot.version>2.7.18</spring-boot.version>
        <spring-cloud.version>2021.0.8</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
        <!-- 第三方依赖版本 -->
        <commons-lang3.version>3.17.0</commons-lang3.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <apache.httpclient.version>5.4.3</apache.httpclient.version>
        <okhttp.version>4.12.0</okhttp.version>
        <lombok.version>1.18.30</lombok.version>
        <mybatis.version>3.5.13</mybatis.version>
        <mysql.version>8.0.33</mysql.version>
        <junit.version>5.10.1</junit.version>
        <flatten-maven-plugin.version>1.2.7</flatten-maven-plugin.version>
        <maven.plugin.version>3.14.0</maven.plugin.version>
        <spring-cloud.version>2021.0.8</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
        <org.mapstruct.version>1.6.3</org.mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <velocity.version>1.7</velocity.version>
        <mybatis-plugin.version>19.1-SNAPSHOT</mybatis-plugin.version>
        <mybatis-en-plugin.version>23.10-SNAPSHOT</mybatis-en-plugin.version>
        <linzi-single-api.version>1.0.1</linzi-single-api.version>
        <firebase.version>9.3.0</firebase.version>
        <pushy.version>0.13.10</pushy.version>
        <eatthepath.pushy.version>0.15.4</eatthepath.pushy.version>
        <paypal-checkout.version>1.0.2</paypal-checkout.version>
        <paypal-payouts.version>1.1.1</paypal-payouts.version>
        <paypal-rest-api.version>1.14.0</paypal-rest-api.version>
        <json.version>20180130</json.version>
        <mail.version>1.5.0-b01</mail.version>
        <jsonwebtoken-jjwt.version>0.11.2</jsonwebtoken-jjwt.version>
        <poi.version>3.17</poi.version>
        <alibaba-transmittable.version>2.11.4</alibaba-transmittable.version>
        <guava.version>32.1.2-jre</guava.version>
        <apache-tika.version>2.9.0</apache-tika.version>
        <apache-collection4.version>4.4</apache-collection4.version>
        <region.version>2.7.0</region.version>
        <mybatis-plus.version>3.5.7</mybatis-plus.version>
        <mybatis-dynamic.version>4.2.0</mybatis-dynamic.version>
        <shardingsphere.version>5.5.2</shardingsphere.version>
        <xxl-job.version>2.4.0</xxl-job.version>
        <snakeyaml.version>2.2</snakeyaml.version>
        <jackson.dataformat.yaml.version>2.16.1</jackson.dataformat.yaml.version>
        <app-store-server-library.version>2.2.0</app-store-server-library.version>
        <redisson-spring-boot-starter.version>3.16.0</redisson-spring-boot-starter.version>
        <fastexcel.version>1.2.0</fastexcel.version>
    </properties>

    <modules>
        <module>pitpat-common</module>
        <module>pitpat-starter</module>
        <module>demo-service</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!--BOM 依赖-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bom</artifactId>
                <version>2.17.100</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--内部模块依赖-->
            <dependency>
                <groupId>com.linzi.pitpat</groupId>
                <artifactId>pitpat-lang</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.linzi.pitpat</groupId>
                <artifactId>pitpat-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.linzi.pitpat</groupId>
                <artifactId>pitpat-excel</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- Apache Commons -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>

            <!-- HTTP 客户端 -->
            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5</artifactId>
                <version>${apache.httpclient.version}</version>
            </dependency>

            <!-- 其他工具库 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!--mybatis-plus 多数据源 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${mybatis-dynamic.version}</version>
            </dependency>
            <!--            分表 分库 -->
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-jdbc</artifactId>
                <version>${shardingsphere.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.shardingsphere</groupId>
                        <artifactId>shardingsphere-test-util</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.lz.mybatis.plugin</groupId>
                <artifactId>mybatis-plugin</artifactId>
                <version>${mybatis-plugin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lz.mybatis.plugin</groupId>
                <artifactId>mybatis-en-plugin</artifactId>
                <version>${mybatis-en-plugin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <!-- Firebase -->
            <dependency>
                <groupId>com.google.firebase</groupId>
                <artifactId>firebase-admin</artifactId>
                <version>${firebase.version}</version>
            </dependency>
            <!-- Pushy -->
            <dependency>
                <groupId>com.turo</groupId>
                <artifactId>pushy</artifactId>
                <version>${pushy.version}</version>
            </dependency>
            <dependency>
                <groupId>com.eatthepath</groupId>
                <artifactId>pushy</artifactId>
                <version>${eatthepath.pushy.version}</version>
            </dependency>
            <!-- PayPal -->
            <dependency>
                <groupId>com.paypal.sdk</groupId>
                <artifactId>checkout-sdk</artifactId>
                <version>${paypal-checkout.version}</version>
            </dependency>
            <dependency>
                <groupId>com.paypal.sdk</groupId>
                <artifactId>payouts-sdk</artifactId>
                <version>${paypal-payouts.version}</version>
            </dependency>
            <dependency>
                <groupId>com.paypal.sdk</groupId>
                <artifactId>rest-api-sdk</artifactId>
                <version>${paypal-rest-api.version}</version>
            </dependency>
            <!-- 邮件 -->
            <dependency>
                <groupId>javax.mail</groupId>
                <artifactId>mail</artifactId>
                <version>${mail.version}</version>
            </dependency>
            <!-- Excel -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId>
                <version>${apache-tika.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${alibaba-transmittable.version}</version>
            </dependency>
            <!-- 苹果支付客户端 -->
            <dependency>
                <groupId>com.apple.itunes.storekit</groupId>
                <artifactId>app-store-server-library</artifactId>
                <version>${app-store-server-library.version}</version>
            </dependency>
            <!-- Google Play支付客户端 -->
            <dependency>
                <groupId>com.google.apis</groupId>
                <artifactId>google-api-services-androidpublisher</artifactId>
                <version>v3-rev20241016-2.0.0</version>
            </dependency>
            <!-- 工具库 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${region.version}</version>
            </dependency>
            <!-- Linzi -->
            <dependency>
                <groupId>com.linzi.single</groupId>
                <artifactId>single-api-feign</artifactId>
                <version>${linzi-single-api.version}</version>
            </dependency>
            <!-- MapStruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>${jackson.dataformat.yaml.version}</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>
            <!-- Redisson -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson-spring-boot-starter.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!--通用依赖-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <!--log4j2 spring boot 扩展-->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-spring-boot</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
            <version>3.4.4</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <!-- Maven Compiler Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                        <debug>true</debug>
                        <debuglevel>lines,vars,source</debuglevel>
                        <compilerArgs>
                            <arg>-parameters</arg>
                        </compilerArgs>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${org.mapstruct.version}</version>
                            </path>
                            <path>
                                <groupId>org.apache.logging.log4j</groupId>
                                <artifactId>log4j-core</artifactId>
                                <version>2.23.1</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>

                <!-- Maven Javadoc Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <goals>
                                <goal>jar-no-fork</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>

                <!-- Spring Boot Maven Plugin -->
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <excludes>
                            <exclude>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                            </exclude>
                        </excludes>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <url>https://nexus.yijiesudai.com/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>https://nexus.yijiesudai.com/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
