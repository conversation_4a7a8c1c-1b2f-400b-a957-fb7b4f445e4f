<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.linzi.pitpat</groupId>
        <artifactId>pitpat-framework</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>demo-service</artifactId>
    <version>0.0.1-SNAPSHOT</version>

    <properties>
        <revision>0.3.1-SNAPSHOT</revision>
        <java.version>21</java.version>
        <apache.httpclient.version>5.4.3</apache.httpclient.version>
        <okhttp.version>4.12.0</okhttp.version>
        <junit.version>5.10.1</junit.version>
    </properties>

    <dependencies>
        <!-- 内部模块依赖，版本由 pitpat-dependencies BOM 管理 -->
        <dependency>
            <groupId>com.linzi.pitpat</groupId>
            <artifactId>pitpat-core</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.linzi.pitpat</groupId>
            <artifactId>pitpat-lang</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.linzi.pitpat</groupId>
            <artifactId>web-spring-boot-starter</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.linzi.pitpat</groupId>
            <artifactId>ampq-spring-boot-starter</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.linzi.pitpat</groupId>
            <artifactId>db-spring-boot-starter</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.linzi.pitpat</groupId>
            <artifactId>pitpat-excel</artifactId>
            <version>${revision}</version>
        </dependency>



        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- RabbitMQ 依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- Apache HttpClient 5 -->
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
            <version>${apache.httpclient.version}</version>
        </dependency>

        <!-- OkHttp -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp.version}</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>3.1.1</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
