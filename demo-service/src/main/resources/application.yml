
spring:
  main:
    allow-circular-references: true
  profiles:
    active: dev
  cloud:
    nacos:
      config:
        import-check:
          enabled: false
      discovery:
        register-enabled: false
  application:
    name: demo-service
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 15MB
  redis:
    redisson:
      cacheGroup:
        # 用例: @Cacheable(cacheNames="groupId", key="#XXX") 方可使用缓存组配置
        - groupId: 5minCacheMap
          ttl: 300000
          maxIdleTime: 300000
          maxSize: 1000
          defaultGroup: true
      config: |
        singleServerConfig:
          idleConnectionTimeout: 10000
          connectTimeout: 10000
          timeout: 3000
          retryAttempts: 4
          password: null
          subscriptionsPerConnection: 5
          clientName: null
          address: "redis://127.0.0.1:6379"
          subscriptionConnectionMinimumIdleSize: 1
          subscriptionConnectionPoolSize: 50
          connectionMinimumIdleSize: 24
          connectionPoolSize: 64
          database: 0
          dnsMonitoringInterval: 5000
        threads: 16
        nettyThreads: 32
        codec: !<org.redisson.codec.JsonJacksonCodec> { }
        transportMode: "NIO"
  rabbitmq:
    custom:
      exchanges:
        - name: user.direct.exchange
          type: direct
        - name: order.direct.exchange
          type: direct
        - name: user.direct.exchange.delay #延迟交换机
          type: direct
          delayed: true
        - name: transfer.direct.exchange #死信交换机
          type: direct
      queues:
        - name: user.register.queue
        - name: user.email.queue
        - name: user.notification.queue
        - name: order.payment.queue
        - name: order.delivery.queue
        - name: transfer.queue
        - name: 1min.queue.dlx
          ttl: 60000
          deadLetterExchange: transfer.direct.exchange
          deadLetterRoutingKey: transfer
        - name: 2min.queue.dlx
          ttl: 120000
          deadLetterExchange: transfer.direct.exchange
          deadLetterRoutingKey: transfer
        - name: 3min.queue.dlx
          ttl: 180000
          deadLetterExchange: transfer.direct.exchange
          deadLetterRoutingKey: transfer
        - name: 4min.queue.dlx
          ttl: 240000
          deadLetterExchange: transfer.direct.exchange
          deadLetterRoutingKey: transfer
        - name: 5min.queue.dlx
          ttl: 300000
          deadLetterExchange: transfer.direct.exchange
          deadLetterRoutingKey: transfer
        - name: 10min.queue.dlx
          ttl: 600000
          deadLetterExchange: transfer.direct.exchange
          deadLetterRoutingKey: transfer


      bindings:
        - queue: user.register.queue
          exchange: user.direct.exchange
          routingKey: user.register
        - queue: user.email.queue
          exchange: user.direct.exchange.delay
          routingKey: user.email
        - queue: order.payment.queue
          exchange: order.direct.exchange
          routingKey: order.payment
        - queue: order.delivery.queue
          exchange: order.direct.exchange
          routingKey: order.delivery
        - queue: transfer.queue
          exchange: transfer.direct.exchange
          routingKey: transfer
        - queue: 1min.queue.dlx
          exchange: transfer.direct.exchange
          routingKey: dlx.1min
        - queue: 2min.queue.dlx
          exchange: transfer.direct.exchange
          routingKey: dlx.2min
        - queue: 3min.queue.dlx
          exchange: transfer.direct.exchange
          routingKey: dlx.3min
        - queue: 4min.queue.dlx
          exchange: transfer.direct.exchange
          routingKey: dlx.4min
        - queue: 5min.queue.dlx
          exchange: transfer.direct.exchange
          routingKey: dlx.5min
        - queue: 10min.queue.dlx
          exchange: transfer.direct.exchange
          routingKey: dlx.10min
      environment-prefix: dev
      enable-environment-prefix: true
  datasource:
    dynamic:
      lazy: true
      primary: master
      strict: false
      datasource:
        master:
          url: *************************************************************************************************************************************************************************
          username: root
          password: root
          driver-class-name: com.mysql.cj.jdbc.Driver
        slave:
          url: *************************************************************************************************************************************************************************
          username: root
          password: root
          driver-class-name: com.mysql.cj.jdbc.Driver
        sharding:
          url: jdbc:shardingsphere:classpath:sharding-jdbc.yml
          driver-class-name: org.apache.shardingsphere.driver.ShardingSphereDriver
      hikari:
        minimum-idle: 5          # ???????
        maximum-pool-size: 200    # ?????
        idle-timeout: 300000     # ????????
        connection-timeout: 20000 # ??????


#
# MyBatisPlus??
# https://baomidou.com/config/
mybatis-plus:
  # ??? XML ????
  mapperLocations: classpath*:mapper/**/*Mapper.xml,classpath*:mapper/**/*Dao.xml
  # ???????package?????????
  typeAliasesPackage: com.linzi.pitpat.**.entity
  configuration:
    # ?????????camel case???
    # ????????????????? @TableField ??????????
    mapUnderscoreToCamelCase: true
    # ???????,????????,????????????????
    # org.apache.ibatis.type.EnumTypeHandler : ???????
    # org.apache.ibatis.type.EnumOrdinalTypeHandler : ???????
    # com.baomidou.mybatisplus.extension.handlers.MybatisEnumTypeHandler : ???????IEnum???????@EnumValue??.
    defaultEnumTypeHandler: org.apache.ibatis.type.EnumTypeHandler
    # ???? true ??????????????????????????????????????? lazyLoadingEnabled ?????
    aggressiveLazyLoading: true
    # MyBatis ??????
    # NONE????????
    # PARTIAL??????? resultMap ??????
    # FULL????? resultMap ???????
    autoMappingBehavior: PARTIAL
    # MyBatis ????????????????
    # NONE??????? (???)
    # WARNING???????????????
    # FAILING????????????????????
    autoMappingUnknownColumnBehavior: NONE
    # Mybatis???????? SESSION
    # SESSION session????????session???????????????
    # STATEMENT ??????
    localCacheScope: SESSION
    # ??Mybatis???????? true
    cacheEnabled: false
    # ???????? ??????
    # logImpl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    # ???? Logo banner
    banner: true
    # ????? SqlRunner
    enableSqlRunner: false
    dbConfig:
      # ????
      # AUTO ???ID??
      # NONE ?
      # INPUT ????ID
      # ASSIGN_ID ????ID
      # ASSIGN_UUID ????ID UUID
      idType: AUTO
      # ????
      tablePrefix: null
      # ?? format,?: %s,(?????)
      columnFormat: null
      # ??????????????,??????
      tableUnderline: true
      # ????,??????????
      capitalMode: false
      # ???entity??????????
      logicDeleteField: isDelete
      # ??????
      logicDeleteValue: 1
      # ??????
      logicNotDeleteValue: 0
      # ??????? insert,? insert ??????????
      # IGNORED ????
      # NOT_NULL ?NULL??
      # NOT_EMPTY ????(?????????,??????????NULL??)
      # DEFAULT ???,????????
      # NEVER ??? SQL
      insertStrategy: NOT_EMPTY
      # ??????? update,? update ??????????
      updateStrategy: NOT_EMPTY
      # ??????? select,? select ??????????? wrapper ???? entity ??? where ??
      whereStrategy: NOT_EMPTY

logging:
  file:
    path: /home/<USER>/logs/${spring.application.name}
server:
  port: 8082
