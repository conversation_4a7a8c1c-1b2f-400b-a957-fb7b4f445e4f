package com.linzi.pitpat.demos.constant;

public interface QueueConstants {

    //交换机
    String USER_EXCHANGE = "user.direct.exchange";
    String ORDER_EXCHANGE = "order.direct.exchange";
    String USER_EXCHANGE_DELAY = "user.direct.exchange.delay";
    String TRANSFER_EXCHANGE = "transfer.direct.exchange";


    //队列
    String USER_REGISTER_QUEUE = "user.register.queue";
    String USER_EMAIL_QUEUE = "user.email.queue";
    String USER_NOTIFICATION_QUEUE = "user.notification.queue"; //延迟队列
    String ORDER_PAYMENT_QUEUE = "order.payment.queue";
    String ORDER_DELIVERY_QUEUE = "order.delivery.queue";
    String ORDER_PAYMENT_QUEUE_DLX = "order.payment.queue.dlx"; //死信队列
    String DLX_QUEUE1MIN = "1min.queue.dlx";
    String DLX_QUEUE2MIN = "2min.queue.dlx";
    String DLX_QUEUE3MIN = "3min.queue.dlx";
    String DLX_QUEUE4MIN = "4min.queue.dlx";
    String DLX_QUEUE5MIN = "5min.queue.dlx";
    String TRANSFER_QUEUE = "transfer.queue";

    //RoutingKey
    String USER_REGISTER_KEY = "user.register";
    String USER_EMAIL_KEY = "user.email";
    String ORDER_PAYMENT_KEY = "order.payment";
    String ORDER_DELIVERY_KEY = "order.delivery";
    String DLX_1MIN_KEY = "dlx.1min";
    String DLX_2MIN_KEY = "dlx.2min";
    String DLX_3MIN_KEY = "dlx.3min";
    String DLX_4MIN_KEY = "dlx.4min";
    String DLX_5MIN_KEY = "dlx.5min";
    String TRANSFER_KEY = "transfer";


}



