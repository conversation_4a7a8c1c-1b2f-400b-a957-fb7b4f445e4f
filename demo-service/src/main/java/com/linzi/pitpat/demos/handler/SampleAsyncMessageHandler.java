package com.linzi.pitpat.demos.handler;

import com.linzi.pitpat.demos.constant.EventConstants;
import com.linzi.pitpat.demos.model.dto.UserDto;
import com.linzi.pitpat.framework.rabbitmq.model.MessageEvent;
import com.linzi.pitpat.framework.rabbitmq.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 示例异步消息处理器
 * 使用@AsyncHandler注解标记为强制异步处理
 */
@Slf4j
@Component
public class SampleAsyncMessageHandler implements MessageHandler<UserDto> {

    @Override
    public String getSupportedEventType() {
        return EventConstants.SIMPLE_ASYNC_EVENT_TYPE;
    }

    @Override
    @Async("messagePostTaskExecutor")
    public void handleMessage(MessageEvent<UserDto> event) {
        log.info("处理示例消息: messageId={}, message={}", event.getMessageId(), event);
        UserDto value = event.getPayload();
        log.info("处理Integer类型异步消息，值为: {}", value);
        // 实现具体的业务逻辑
    }
}
