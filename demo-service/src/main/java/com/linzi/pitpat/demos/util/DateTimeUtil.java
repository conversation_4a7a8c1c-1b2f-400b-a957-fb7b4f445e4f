package com.linzi.pitpat.demos.util;

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.dto.DateTimeStartEnd;
import com.linzi.pitpat.core.util.dto.WeekDayDateDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
public class DateTimeUtil {
    public final static String pattern = "yyyy-MM-dd HH:mm:ss";

    public final static DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);

    public static ZonedDateTime parse(String date) {
        return ZonedDateTime.parse(date, formatter.withZone(ZoneId.systemDefault()));
    }

    public static ZonedDateTime parse(Instant instant) {
        return instant.atZone(ZoneId.systemDefault());
    }

    /**
     * 将字符串转位日期
     *
     * @param date
     * @param zoneId
     * @return
     */
    public static ZonedDateTime parse(String date, String zoneId) {
        if (StringUtils.hasText(zoneId)) {
            return ZonedDateTime.parse(date, formatter.withZone(ZoneId.of(zoneId)));
        }
        return parse(date);
    }

    /**
     * 使用指定的格式将字符串转为日期
     *
     * @param date
     * @param pattern
     * @return
     */
    public static ZonedDateTime parseWithPattern(String date, String pattern) {
        DateTimeFormatter dateFormatter = java.time.format.DateTimeFormatter.ofPattern(pattern).withZone(ZoneId.systemDefault());
        return ZonedDateTime.parse(date, dateFormatter.withZone(ZoneId.systemDefault()));
    }

    /**
     * 使用指定的格式将年月转换为日期
     *
     * @param date
     * @param Pattern
     * @return
     */
    public static ZonedDateTime parseYearMonthWithPattern(String date, String Pattern) {
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(Pattern);
        YearMonth yearMonth = YearMonth.parse(date, dateFormatter);
        LocalDate LocalDate = yearMonth.atDay(1);
        return LocalDate.atStartOfDay(ZoneId.systemDefault());
    }


    /**
     * 检查日期是否在指定的时间范围内（包含边界）
     *
     * @param date  要检查的日期
     * @param start 开始时间（包含）
     * @param end   结束时间（包含）
     * @return 如果日期在范围内返回true，否则返回false
     */
    public static boolean isBetween(ZonedDateTime date, ZonedDateTime start, ZonedDateTime end) {
        if (Objects.isNull(date)) {
            return false;
        }
        if (Objects.isNull(start) || Objects.isNull(end)) {
            return false;
        }
        return !date.isBefore(start) && !date.isAfter(end);
    }

    /**
     * 从 Epoch 毫秒时间戳创建 ZonedDateTime（使用系统默认时区）
     *
     * @param epochMilli 从 1970-01-01T00:00:00Z 开始的毫秒数
     * @return 对应的 ZonedDateTime 对象
     */
    public static ZonedDateTime parse(long epochMilli) {
        return ZonedDateTime.ofInstant(Instant.ofEpochMilli(epochMilli), ZoneId.systemDefault());
    }

    /**
     * 将日期格式化为字符串
     *
     * @param date
     * @return
     */
    public static String format(ZonedDateTime date) {
        return formatter.format(date);
    }

    /**
     * 将日期格式化为字符串
     *
     * @param date
     * @return
     */
    public static String formatWithPattern(ZonedDateTime date, String pattern) {
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(pattern).withZone(ZoneId.systemDefault());
        return dateFormatter.format(date);
    }

    /**
     * 将日期格式化为字符串
     *
     * @param date
     * @return
     */
    public static String format(ZonedDateTime date, String pattern, String zoneId) {
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(pattern).withZone(ZoneId.of(zoneId));
        return dateFormatter.format(date);
    }


    /**
     * 将给定时间按照用户时区减去对应的时间偏差
     *
     * @param date
     * @param zoneId
     * @return
     */
    public static ZonedDateTime withZoneSameLocal(ZonedDateTime date, String zoneId) {
        return date.withZoneSameInstant(ZoneId.of(zoneId)).withZoneSameLocal(ZoneId.systemDefault());
    }


    public static ZonedDateTime firstDayOfMonth(ZonedDateTime date) {
        if (Objects.isNull(date)) {
            return null;
        }
        return date.with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN);
    }

    public static ZonedDateTime lastDayOfMonth(ZonedDateTime date) {
        if (Objects.isNull(date)) {
            return null;
        }
        return date.with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);
    }

    public static boolean inRange(ZonedDateTime date, ZonedDateTime start, ZonedDateTime end) {
        if (Objects.isNull(date)) {
            return false;
        }
        if (Objects.isNull(start) || Objects.isNull(end)) {
            return false;
        }
        return date.compareTo(start) >= 0 && date.compareTo(end) <= 0;
    }

    /**
     * 不建议使用，该方法可以提取到业务类中
     *
     * @return
     */
    @Deprecated
    public static ZonedDateTime addMonthsFirstDay(ZonedDateTime date, int month, String zoneId) {
        ZonedDateTime dateTime = date.withZoneSameInstant(ZoneId.of(zoneId));

        ZonedDateTime zonedDateTime = dateTime.plusMonths(month).withDayOfMonth(1).with(LocalTime.MIN);
        return zonedDateTime.withZoneSameLocal(ZoneId.systemDefault());
    }

    /**
     * 比较日期是否同一天
     *
     * @param date1
     * @param date2
     * @return
     */
    public static boolean isSameDay(ZonedDateTime date1, ZonedDateTime date2) {
        if (Objects.isNull(date1) || Objects.isNull(date2)) {
            return false;
        }
        return date1.truncatedTo(ChronoUnit.DAYS).equals(date2.truncatedTo(ChronoUnit.DAYS));
    }

    /**
     * 计算区间内的连续周末时间段（周六、周日），入参与返回值均为 ZonedDateTime 版本
     * <p>
     * 参考 {@link DateUtil#getWeekEndDay(Date, Date)} 的实现逻辑。
     *
     * @param monthStart 区间开始时间（包含）
     * @param monthEnd   区间结束时间（不包含）
     * @return 周末连续区间列表；当结束时间早于开始时间时返回 null
     */
    public static List<DateTimeStartEnd> getWeekEndDay(ZonedDateTime monthStart, ZonedDateTime monthEnd) {
        if (monthStart == null || monthEnd == null) {
            return null;
        }
        if (monthEnd.toInstant().toEpochMilli() < monthStart.toInstant().toEpochMilli()) {
            return null;
        }

        List<DateTimeStartEnd> result = new ArrayList<>();
        DateTimeStartEnd currentRange = null;

        ZonedDateTime cursor = monthStart;
        while (cursor.isBefore(monthEnd)) {
            ZonedDateTime current = cursor;
            int day = current.getDayOfWeek().getValue(); // 1=Mon ... 7=Sun
            boolean isWeekend = day > 5; // Sat/Sun

            if (isWeekend) {
                if (currentRange == null) {
                    currentRange = new DateTimeStartEnd(current, current.with(LocalTime.MAX));
                } else {
                    currentRange.setEndTime(current.with(LocalTime.MAX));
                }
            } else {
                if (currentRange != null) {
                    result.add(currentRange);
                    currentRange = null;
                }
            }

            cursor = cursor.plusDays(1);
        }

        if (currentRange != null) {
            result.add(currentRange);
        }

        return result;
    }

    /**
     * 计算区间内的连续工作日时间段（周一至周五），入参与返回值均为 ZonedDateTime 版本
     * 参考 DateUtil#getWorkDay(Date, Date) 的实现逻辑。
     *
     * @param monthStart 区间开始时间（包含）
     * @param monthEnd   区间结束时间（不包含）
     * @return 工作日连续区间列表；当结束时间早于开始时间时返回 null
     */
    public static List<DateTimeStartEnd> getWorkDay(ZonedDateTime monthStart, ZonedDateTime monthEnd) {
        if (monthStart == null || monthEnd == null) {
            return null;
        }
        if (monthEnd.toInstant().toEpochMilli() < monthStart.toInstant().toEpochMilli()) {
            return null;
        }

        List<DateTimeStartEnd> result = new ArrayList<>();
        DateTimeStartEnd currentRange = null;

        ZonedDateTime cursor = monthStart;
        while (cursor.toInstant().toEpochMilli() < monthEnd.toInstant().toEpochMilli()) {
            ZonedDateTime current = cursor;
            int day = current.getDayOfWeek().getValue(); // 1=Mon ... 7=Sun
            boolean isWorkday = day < 6; // Mon-Fri

            if (isWorkday) {
                if (currentRange == null) {
                    currentRange = new DateTimeStartEnd(current, current.with(LocalTime.MAX));
                } else {
                    currentRange.setEndTime(current.with(LocalTime.MAX));
                }
            } else {
                if (currentRange != null) {
                    result.add(currentRange);
                    currentRange = null;
                }
            }

            cursor = cursor.plusDays(1);
        }

        if (currentRange != null) {
            result.add(currentRange);
        }

        return result;
    }


    /**
     * 获取指定日期所在周的信息（ZonedDateTime 版本）
     * 逻辑参考 DateUtil#getNumWeek(Date)
     */
    public static WeekDayDateDto getNumWeek(ZonedDateTime date) {
        return getNumWeek(date, null);
    }

    /**
     * 获取指定日期所在周的信息（ZonedDateTime 版本）
     * 逻辑参考 DateUtil#getNumWeek(Date, Date)
     */
    public static WeekDayDateDto getNumWeek(ZonedDateTime date, ZonedDateTime preDate) {
        if (date == null) {
            return null;
        }

        int weekCount = 0;
        int week1 = date.getDayOfWeek().getValue(); // 1=Mon ... 7=Sun
        int flagIndex = week1 - 1;

        // 本周周四
        ZonedDateTime thursdayOfWeek = date.plusDays(3 - flagIndex);

        // 如果这个星期的星期四在上一个月，且当前日期在该周四之后，则从周四所在月重新计算
        if (date.getMonthValue() != thursdayOfWeek.getMonthValue()
                && date.toInstant().toEpochMilli() > thursdayOfWeek.toInstant().toEpochMilli()) {
            return getNumWeek(thursdayOfWeek, date);
        }

        // 本周周日（结束）
        ZonedDateTime sunday = date.plusDays(6 - flagIndex);

        // 统计本月（从1号开始）到本周周日之间出现的周四次数
        ZonedDateTime cursor = thursdayOfWeek.with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN);
        while (!cursor.toLocalDate().isAfter(sunday.toLocalDate())) {
            if (cursor.getDayOfWeek().getValue() == DayOfWeek.THURSDAY.getValue()) {
                weekCount++;
            }
            cursor = cursor.plusDays(1);
        }

        WeekDayDateDto dto = new WeekDayDateDto();
        dto.setWeek(preDate != null ? preDate.getDayOfWeek().getValue() : week1);

        // 周起止（本地时区）
        ZonedDateTime weekStart = date.plusDays(1 - week1).with(LocalTime.MIN);
        ZonedDateTime weekEnd = sunday.with(LocalTime.MAX);
        dto.setWeekStart(weekStart);
        dto.setWeekEnd(weekEnd);

        // 第几周、月份信息
        dto.setWeekN(weekCount);
        dto.setCurrentMonth(thursdayOfWeek.format(DateTimeFormatter.ofPattern("yyyy-MM")));
        dto.setMonth(thursdayOfWeek.getMonthValue());

        // 北京时间（Asia/Shanghai）起止
        ZoneId beijing = ZoneId.of("Asia/Shanghai");
        //dto.setBeijingWeekStart(weekStart.withZoneSameInstant(beijing));
        //dto.setBeijingWeekEnd(weekEnd.withZoneSameInstant(beijing));
        dto.setBeijingWeekStart(weekStart.minusHours(8));
        dto.setBeijingWeekEnd(weekEnd.minusHours(8));

        // 字符串表示
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(pattern);
        dto.setWeekStartStr(dto.getWeekStart().format(fmt));
        dto.setWeekEndStr(dto.getWeekEnd().format(fmt));

        return dto;
    }


}

