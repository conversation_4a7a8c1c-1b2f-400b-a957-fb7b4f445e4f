package com.linzi.pitpat.demos.listener;

import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.framework.rabbitmq.QueueNameGenerator;
import com.linzi.pitpat.framework.rabbitmq.listener.MessageListener;
import com.linzi.pitpat.framework.rabbitmq.model.DelayMessage;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * 处理从死信队列转发到目标队列的消息
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DelayMessageTransferListener extends MessageListener {

    private final RabbitTemplate rabbitTemplate;
    private final QueueNameGenerator nameGenerator;

    /**
     * 处理从死信队列转发到process.queue.target队列的消息
     *
     * @param message RabbitMQ消息
     */
    @RabbitListener(queues = "#{@nameGenerator.queueName(T(com.linzi.pitpat.framework.rabbitmq.constant.QueueConstants).TRANSFER_QUEUE)}", containerFactory = "directRabbitListenerContainerFactory")
    public void onMessage(@Payload Message message, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) {
        parseMessage(message, deliveryTag, channel);
    }

    @Override
    protected void handleMessage(String json) {
        DelayMessage delayMessage = JsonUtil.readValue(json, DelayMessage.class);
        Objects.requireNonNull(delayMessage);

        String exchangeName = nameGenerator.exchangeName(delayMessage.getExchange());

        Message message = MessageBuilder.withBody(delayMessage.getPayload().getBytes(StandardCharsets.UTF_8)).setMessageId(delayMessage.getMessageId()).build();
        rabbitTemplate.convertAndSend(exchangeName, delayMessage.getRoutingKey(), message);
    }

}
