package com.linzi.pitpat.demos.handler;

import com.linzi.pitpat.demos.constant.EventConstants;
import com.linzi.pitpat.demos.model.dto.UserDto;
import com.linzi.pitpat.framework.rabbitmq.model.MessageEvent;
import com.linzi.pitpat.framework.rabbitmq.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 示例同步消息处理器
 * 使用@AsyncHandler注解标记为允许同步处理
 */
@Slf4j
@Component
public class SampleMessageHandler implements MessageHandler<UserDto> {

    @Override
    public String getSupportedEventType() {
        return EventConstants.SIMPLE_EVENT_TYPE;
    }

    @Override
    public void handleMessage(MessageEvent<UserDto> event) {
        log.info("处理示例消息: messageId={}, message={}", event.getMessageId(), event);
        UserDto value = event.getPayload();
        log.info("处理Integer类型消息，值为: {}", value);
        // 实现具体的业务逻辑
    }
}
