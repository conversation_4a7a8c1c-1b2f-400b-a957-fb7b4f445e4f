package com.linzi.pitpat.demos.api.excel;

import com.linzi.pitpat.demos.excel.dto.UserExcelDTO;
import com.linzi.pitpat.excel.config.ExcelExportConfig;
import com.linzi.pitpat.excel.config.ExcelImportConfig;
import com.linzi.pitpat.excel.constants.ExcelConstant;
import com.linzi.pitpat.excel.service.AdvancedExcelService;
import com.linzi.pitpat.excel.util.ExcelUtils;
import com.linzi.pitpat.excel.validator.ExcelValidator;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.function.Consumer;

import static com.linzi.pitpat.excel.constants.ExcelConstant.AUTO_WIDTH_ANALYZE_ROW_NAME;

/**
 * 高级Excel导入导出控制器
 */
@RestController
@RequestMapping("/api/advanced-excel")
@AllArgsConstructor
public class AdvancedExcelController {

    private final AdvancedExcelService advancedExcelService;


    /**
     * 1. 自定义样式导出
     */
    @GetMapping("/custom-style-export")
    public void customStyleExport(HttpServletResponse response) throws IOException {
        // 准备数据
        List<UserExcelDTO> dataList = generateUserData(100);

        // 创建自定义样式配置
        Map<String, Object> styleConfig = new HashMap<>();
        styleConfig.put(ExcelConstant.AUTO_WIDTH_ANALYZE_ROW_NAME, 50); //基于前多少行数据调整宽度

        // 创建导出配置
        ExcelExportConfig<UserExcelDTO> config = ExcelExportConfig.builder(UserExcelDTO.class)
                .sheetName("产品列表")
                .autoColumnWidth(true)     // 启用自动调整列宽
                .freezeFirstRow(true)      // 启用冻结首行
                .styleConfig(styleConfig)  // 设置自定义样式配置
                .excludeFields(new String[]{"username", "gender"}) //手动排除字段
                .build();

        // 设置响应头
        ExcelUtils.setExcelResponseHeaders(response, "用户数据_高级配置");

        // 导出Excel
        advancedExcelService.exportWithConfig(dataList, config, response.getOutputStream());
    }


    /**
     * 导出多Sheet Excel
     */
    @GetMapping("/export/multi-sheet")
    public void exportMultiSheet(HttpServletResponse response) throws IOException {
        // 准备数据
        List<UserExcelDTO> allUsers = generateUserData(100);
        List<UserExcelDTO> maleUsers = filterUsersByGender(allUsers, "男");
        List<UserExcelDTO> femaleUsers = filterUsersByGender(allUsers, "女");

        // 创建Sheet映射
        Map<String, List<?>> dataMap = new HashMap<>();
        dataMap.put("所有用户", allUsers);
        dataMap.put("男性用户", maleUsers);
        dataMap.put("女性用户", femaleUsers);

        // 设置响应头
        ExcelUtils.setExcelResponseHeaders(response, "用户数据_多Sheet");

        // 导出Excel
        advancedExcelService.exportMultiSheet(dataMap, response.getOutputStream());
    }

    /**
     * 使用自动调整列宽和冻结首行导出Excel
     *
     * @throws IOException IO异常
     */
    @GetMapping("export/autoWidth")
    public void exportWithFormatting(HttpServletResponse response) throws IOException {
        // 创建测试数据
        List<UserExcelDTO> products = generateUserData(200);
        // 创建自定义样式配置
        Map<String, Object> styleConfig = new HashMap<>();
        // 自动调整列宽时分析前20行数据来确定列的数据类型
        styleConfig.put(ExcelConstant.AUTO_WIDTH_ANALYZE_ROW_NAME, 20);

        // 创建导出配置
        ExcelExportConfig<UserExcelDTO> config = ExcelExportConfig.builder(UserExcelDTO.class)
                .sheetName("产品列表")
                .autoColumnWidth(true)     // 启用自动调整列宽
                .freezeFirstRow(true)      // 启用冻结首行
                .styleConfig(styleConfig)
                .build();

        ExcelUtils.setExcelResponseHeaders(response, "用户数据_高级配置");
        // 导出Excel
        advancedExcelService.exportWithConfig(products, config, response.getOutputStream());
    }

    /**
     * 产品数据类
     */
    @Data
    public static class ProductData {
        private Integer productId;
        private String productName;
        private String description;
        private Double price;
        private Integer stock;
        private Date createTime;
        private Date updateTime;
    }

    /**
     * 导入设备销售
     */
    @PostMapping("/import/sale")
    public Map<String, Object> importSale(@RequestParam("file") MultipartFile file) throws IOException {
        // 创建导入配置
        ExcelImportConfig<UserExcelDTO> config = ExcelImportConfig.<UserExcelDTO>builder()
                .clazz(UserExcelDTO.class)
                //.headRowNumber(2)
                //.skipInvalid(true)
                //.ignoreEmptyRow(true)
                .sheetNo(1)
                .validator(ExcelValidator.createDefault(UserExcelDTO.class))
                .build();

        // 导入Excel
        List<UserExcelDTO> dataList = advancedExcelService.importWithConfig(file.getInputStream(), config);

        // 返回导入结果
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "导入成功");
        result.put("data", dataList);
        result.put("total", dataList.size());

        return result;
    }

    /**
     * 使用自定义配置导入Excel
     */
    @PostMapping("/import/config")
    public Map<String, Object> importWithConfig(@RequestParam("file") MultipartFile file) throws IOException {
        // 创建导入配置
        ExcelImportConfig<UserExcelDTO> config = ExcelImportConfig.<UserExcelDTO>builder()
                .clazz(UserExcelDTO.class)
                .headRowNumber(10)
                .skipInvalid(true)
                .ignoreEmptyRow(true)
                .validator(ExcelValidator.createDefault(UserExcelDTO.class))
                .build();

        // 导入Excel
        List<UserExcelDTO> dataList = advancedExcelService.importWithConfig(file.getInputStream(), config);

        // 返回导入结果
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "导入成功");
        result.put("data", dataList);
        result.put("total", dataList.size());

        return result;
    }

    /**
     * 批量处理导入数据
     */
    @PostMapping("/import/batch")
    public Map<String, Object> importWithBatch(@RequestParam("file") MultipartFile file) throws IOException {
        final int[] totalCount = {0};
        final List<UserExcelDTO> sampleData = new ArrayList<>();

        // 定义批处理函数
        Consumer<List<UserExcelDTO>> batchConsumer = batchData -> {
            // 这里可以对每批数据进行处理，例如批量保存到数据库
            totalCount[0] += batchData.size();

            // 保存前10条数据作为示例
            if (sampleData.size() < 10) {
                sampleData.addAll(batchData.subList(0, Math.min(10 - sampleData.size(), batchData.size())));
            }
        };

        // 批量导入Excel，每次处理100条数据
        advancedExcelService.importWithBatch(file.getInputStream(), UserExcelDTO.class, 100, batchConsumer);

        // 返回导入结果
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "批量导入成功");
        result.put("totalProcessed", totalCount[0]);
        result.put("sampleData", sampleData);

        return result;
    }


    /**
     * 5. 带进度的批量导入
     */
    @PostMapping("/batch-import-with-progress")
    public Map<String, Object> batchImportWithProgress(@RequestParam("file") MultipartFile file) throws IOException {
        // 创建进度追踪对象
        ImportProgress progress = new ImportProgress();

        // 创建批处理消费者
        Consumer<List<UserExcelDTO>> batchConsumer = batch -> {
            // 更新进度
            progress.addProcessed(batch.size());

            // 模拟处理延迟
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        };

        // 开始批量导入
        advancedExcelService.importWithBatch(file.getInputStream(), UserExcelDTO.class, 100, batchConsumer);

        // 返回导入结果
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "批量导入完成");
        result.put("progress", progress);

        return result;
    }

    /**
     * 9. 导入多个Sheet
     */
    @PostMapping("/import-multi-sheet")
    public Map<String, Object> importMultiSheet(@RequestParam("file") MultipartFile file) throws IOException {
        // 创建结果Map
        Map<String, Object> result = new HashMap<>();
        Map<String, List<?>> sheetData = new HashMap<>();

        // 导入产品数据（假设在第一个Sheet）
        try (InputStream inputStream = file.getInputStream()) {
            ExcelImportConfig<UserExcelDTO> productConfig = ExcelImportConfig.<UserExcelDTO>builder()
                    .clazz(UserExcelDTO.class)
                    .sheetNo(0) // 第一个Sheet
                    .build();

            List<UserExcelDTO> products = advancedExcelService.importWithConfig(inputStream, productConfig);
            sheetData.put("products", products);
        }

        // 导入用户数据（假设在第二个Sheet）
        try (InputStream inputStream = file.getInputStream()) {
            ExcelImportConfig<UserExcelDTO> userConfig = ExcelImportConfig.<UserExcelDTO>builder()
                    .clazz(UserExcelDTO.class)
                    .sheetNo(1) // 第二个Sheet
                    .build();

            List<UserExcelDTO> users = advancedExcelService.importWithConfig(inputStream, userConfig);
            sheetData.put("users", users);
        }

        // 返回结果
        result.put("code", 200);
        result.put("message", "多Sheet导入成功");
        result.put("data", sheetData);

        return result;
    }


    /**
     * 生成用户测试数据
     */
    private List<UserExcelDTO> generateUserData(int count) {
        List<UserExcelDTO> dataList = new ArrayList<>();
        String[] genders = {"男", "女"};
        String[] addressPrefixes = {"北京市", "上海市", "广州市", "深圳市", "杭州市", "成都市", "武汉市", "南京市"};

        Random random = new Random();

        for (int i = 0; i < count; i++) {
            UserExcelDTO user = UserExcelDTO.builder()
                    .id((long) (i + 1))
                    .username("用户" + (i + 1))
                    .gender(genders[random.nextInt(genders.length)])
                    .age(18 + random.nextInt(60))
                    .phone("1" + (random.nextInt(9) + 1) + String.format("%09d", random.nextInt(1000000000)))
                    .email("user" + (i + 1) + "@example.com")
                    .address(addressPrefixes[random.nextInt(addressPrefixes.length)] + "某某区某某街道" + random.nextInt(100) + "号")
                    .birthday(LocalDateTime.ofInstant(Instant.ofEpochMilli(System.currentTimeMillis() - (random.nextInt(365 * 50) * 24L * 60L * 60L * 1000L)), ZoneId.systemDefault()))
                    .createTime(ZonedDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build();
            dataList.add(user);
        }

        return dataList;
    }

    /**
     * 按性别过滤用户数据
     */
    private List<UserExcelDTO> filterUsersByGender(List<UserExcelDTO> users, String gender) {
        return users.stream()
                .filter(user -> gender.equals(user.getGender()))
                .toList();
    }


    /**
     * 导入进度追踪
     */
    @Data
    public static class ImportProgress {
        private int totalProcessed = 0;
        private int validCount = 0;
        private int invalidCount = 0;

        public void addProcessed(int count) {
            this.totalProcessed += count;
        }

        public void addValid(int count) {
            this.validCount += count;
        }

        public void addInvalid(int count) {
            this.invalidCount += count;
        }
    }

}
