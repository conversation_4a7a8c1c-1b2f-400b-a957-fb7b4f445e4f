package com.linzi.pitpat.demos.base;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

public interface Ienum<T> {

    /**
     * @return 数组
     */
    //T[] array();
    //
    //default  T resolve(T param) {
    //    Optional<T> optional = Arrays.stream(array()).filter(item -> Objects.equals(item, param)).findFirst();
    //    return optional.orElse(null);
    //}

}
