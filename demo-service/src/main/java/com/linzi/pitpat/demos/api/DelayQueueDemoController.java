package com.linzi.pitpat.demos.api;

import com.linzi.pitpat.framework.rabbitmq.model.MessageWrapper;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 死信队列演示控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/demo/delay-queue")
@RequiredArgsConstructor
public class DelayQueueDemoController {

    private final QueueMessageService queueMessageService;

    /**
     * 发送消息到30秒死信队列
     * 
     * @param message 消息内容
     * @return 消息ID
     */
    @PostMapping("/send-30s")
    public String send30sDelay(@RequestBody String message) {
        log.info("发送消息到30秒死信队列: {}", message);
        Map<String, Object> payload = new HashMap<>();
        payload.put("message", message);
        payload.put("timestamp", System.currentTimeMillis());
        
        MessageWrapper<Map<String, Object>> wrapper = queueMessageService.sendMessage(
            "delay.direct.exchange", 
            "dlx.30s", 
            payload
        );
        
        return wrapper.getMessageId();
    }
    
    /**
     * 发送消息到1分钟死信队列
     * 
     * @param message 消息内容
     * @return 消息ID
     */
    @PostMapping("/send-1min")
    public String send1minDelay(@RequestBody String message) {
        log.info("发送消息到1分钟死信队列: {}", message);
        Map<String, Object> payload = new HashMap<>();
        payload.put("message", message);
        payload.put("timestamp", System.currentTimeMillis());
        
        MessageWrapper<Map<String, Object>> wrapper = queueMessageService.sendMessage(
            "delay.direct.exchange", 
            "dlx.1min", 
            payload
        );
        
        return wrapper.getMessageId();
    }
    
    /**
     * 发送消息到5分钟死信队列
     * 
     * @param message 消息内容
     * @return 消息ID
     */
    @PostMapping("/send-5min")
    public String send5minDelay(@RequestBody String message) {
        log.info("发送消息到5分钟死信队列: {}", message);
        Map<String, Object> payload = new HashMap<>();
        payload.put("message", message);
        payload.put("timestamp", System.currentTimeMillis());
        
        MessageWrapper<Map<String, Object>> wrapper = queueMessageService.sendMessage(
            "delay.direct.exchange", 
            "dlx.5min", 
            payload
        );
        
        return wrapper.getMessageId();
    }
    
    /**
     * 发送消息到10分钟死信队列
     * 
     * @param message 消息内容
     * @return 消息ID
     */
    @PostMapping("/send-10min")
    public String send10minDelay(@RequestBody String message) {
        log.info("发送消息到10分钟死信队列: {}", message);
        Map<String, Object> payload = new HashMap<>();
        payload.put("message", message);
        payload.put("timestamp", System.currentTimeMillis());
        
        MessageWrapper<Map<String, Object>> wrapper = queueMessageService.sendMessage(
            "delay.direct.exchange", 
            "dlx.10min", 
            payload
        );
        
        return wrapper.getMessageId();
    }
    
    /**
     * 发送消息到30分钟死信队列
     * 
     * @param message 消息内容
     * @return 消息ID
     */
    @PostMapping("/send-30min")
    public String send30minDelay(@RequestBody String message) {
        log.info("发送消息到30分钟死信队列: {}", message);
        Map<String, Object> payload = new HashMap<>();
        payload.put("message", message);
        payload.put("timestamp", System.currentTimeMillis());
        
        MessageWrapper<Map<String, Object>> wrapper = queueMessageService.sendMessage(
            "delay.direct.exchange", 
            "dlx.30min", 
            payload
        );
        
        return wrapper.getMessageId();
    }
} 