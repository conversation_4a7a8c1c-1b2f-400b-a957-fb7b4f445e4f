package com.linzi.pitpat.demos.api.excel;

import com.linzi.pitpat.demos.excel.dto.TreadmillSaleDo;
import com.linzi.pitpat.demos.excel.dto.UserExcelDTO;
import com.linzi.pitpat.excel.config.ExcelImportConfig;
import com.linzi.pitpat.excel.model.ErrorReport;
import com.linzi.pitpat.excel.model.ExcelImportResult;
import com.linzi.pitpat.excel.model.ValidationResult;
import com.linzi.pitpat.excel.service.ExcelService;
import com.linzi.pitpat.excel.util.ExcelUtils;
import com.linzi.pitpat.excel.validator.ExcelValidator;
import com.linzi.pitpat.excel.validator.impl.Jsr380Validator;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * Excel导入导出控制器
 */
@RestController
@RequestMapping("/api/excel")
@AllArgsConstructor
@Slf4j
public class ExcelController {

    private final ExcelService excelService;


    /**
     * 1.1 基本导出示例
     */
    @GetMapping("/basic-export")
    public void basicExport(HttpServletResponse response) throws IOException {
        // 准备数据
        List<UserExcelDTO> dataList = generateUserData(100);

        // 使用ExcelService导出
        excelService.exportExcel(response, dataList, UserExcelDTO.class, "基本导出示例");
    }

    /**
     * 1.2 导出Excel模板
     */
    @GetMapping("/export-template")
    public void exportTemplate(HttpServletResponse response) throws IOException {
        // 导出空模板
        excelService.exportExcelTemplate(response, UserExcelDTO.class, "用户数据模板");
    }


    /**
     * 2.1 不带验证导入
     */
    @PostMapping("/import-without-validation")
    public Map<String, Object> importWithoutValidation(@RequestParam("file") MultipartFile file) throws IOException {
        // 导入Excel（不带验证）
        List<UserExcelDTO> dataList = excelService.importExcelWithoutValidation(file, UserExcelDTO.class);

        // 返回导入结果
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "导入成功（不带验证）");
        result.put("data", dataList);
        result.put("total", dataList.size());

        return result;
    }

    /**
     * 2.2 带校验导入
     * 隐式调用 ExcelValidator.createDefault(UserExcelDTO.class);
     */
    @PostMapping("/basic-import")
    public Map<String, Object> basicImport(@RequestParam("file") MultipartFile file) throws IOException {
        // 导入Excel
        ExcelImportResult<TreadmillSaleDo> dataList = excelService.importExcel(file, TreadmillSaleDo.class);

        // 返回导入结果
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "导入成功");
        result.put("data", dataList.getValidData());
        result.put("total", dataList.getValidData().size());

        return result;
    }


    /**
     * 2.3 自定义验证器导入（默认校验）
     * 试用 Jsr380Validator
     */
    @PostMapping("/import-with-custom-validation")
    public Map<String, Object> importWithCustomValidation(@RequestParam("file") MultipartFile file) throws IOException {
        // 创建自定义验证器
        ExcelValidator<UserExcelDTO> validator = new Jsr380Validator<>();
        // 导入Excel（带自定义验证）
        ExcelImportResult<UserExcelDTO> dataList = excelService.importExcelWithValidation(file, UserExcelDTO.class, validator);

        // 返回导入结果
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "导入成功（自定义验证）");
        result.put("data", dataList.getValidData());
        result.put("total", dataList.getValidData().size());

        return result;
    }


    /**
     * 2.4 自定义验证器导入
     */
    @PostMapping("/import/users/validate")
    public Map<String, Object> importUsersWithValidation(@RequestParam("file") MultipartFile file) throws IOException {
        // 创建校验器
        ExcelValidator<UserExcelDTO> validator = new CustomUserValidator();
        ExcelImportResult<UserExcelDTO> dataList = excelService.importExcelWithValidation(file, UserExcelDTO.class, validator);

        // 返回导入结果
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "导入成功");
        result.put("data", dataList.getValidData());
        result.put("total", dataList.getValidData().size());

        return result;
    }


    /**
     * 2.5 带详细验证结果的导入
     */
    @PostMapping("/import-with-validation-result")
    public Map<String, Object> importWithValidationResult(@RequestParam("file") MultipartFile file) throws IOException {
        try {
            // 创建导入配置
            ExcelImportConfig<UserExcelDTO> config = ExcelImportConfig.<UserExcelDTO>builder()
                    .clazz(UserExcelDTO.class)
                    .validator(new Jsr380Validator<>())
                    .skipInvalid(false) // 不跳过无效数据
                    .build();

            // 导入并获取验证结果
            ExcelImportResult<UserExcelDTO> result = ExcelUtils.readWithValidation(file.getInputStream(), config);

            // 返回导入结果
            Map<String, Object> response = new HashMap<>();
            response.put("code", result.isHasErrors() ? 400 : 200);
            response.put("message", result.isHasErrors() ? "导入包含错误" : "导入成功");
            response.put("validData", result.getValidData());
            response.put("invalidData", result.getInvalidData());
            response.put("errors", result.getErrorMessages());
            response.put("totalValid", result.getValidData().size());
            response.put("totalInvalid", result.getInvalidData().size());

            return response;
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "导入失败: " + e.getMessage());
            return errorResponse;
        }
    }


    /**
     * 2.6 导出错误报告
     */
    @PostMapping("/import-and-export-errors")
    public void importAndExportErrors(@RequestParam("file") MultipartFile file, HttpServletResponse response) throws IOException {
        try {
            // 创建导入配置
            ExcelImportConfig<UserExcelDTO> config = ExcelImportConfig.<UserExcelDTO>builder()
                    .clazz(UserExcelDTO.class)
                    .validator(new Jsr380Validator<>())
                    .skipInvalid(false) // 不跳过无效数据
                    .build();

            // 导入并获取验证结果
            ExcelImportResult<UserExcelDTO> result = excelService.importExcel(file, UserExcelDTO.class);
            //.readWithValidation(file.getInputStream(), config);

            // 如果有错误，导出错误报告
            if (result.isHasErrors()) {
                // 创建错误报告数据
                List<ErrorReport> errorReports = new ArrayList<>();
                // 遍历错误映射
                result.getErrorMap().forEach((rowIndex, fieldErrors) -> {
                    log.info("{},{}", rowIndex, fieldErrors);
                    try {
                        // 获取对应行的数据
                        UserExcelDTO userData = result.getAllData().get(rowIndex - 2); // 行索引从2开始

                        // 遍历字段错误
                        fieldErrors.forEach((field, errorMsg) -> {
                            ErrorReport errorReport = new ErrorReport();
                            errorReport.setRowIndex(rowIndex);
                            errorReport.setUsername(userData.getUsername());
                            errorReport.setField(field);
                            errorReport.setErrorMessage(errorMsg);
                            errorReport.setCurrentValue(getFieldValue(userData, field));
                            errorReports.add(errorReport);
                        });
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                });

                // 设置响应头
                ExcelUtils.setExcelResponseHeaders(response, "导入错误报告");

                // 导出错误报告
                ExcelUtils.export(errorReports, ErrorReport.class, response.getOutputStream());
            } else {
                // 如果没有错误，返回成功消息
                response.setContentType("application/json");
                response.getWriter().write("{\"code\":200,\"message\":\"导入成功，无错误\"}");
            }
        } catch (Exception e) {
            response.setContentType("application/json");
            response.getWriter().write("{\"code\":500,\"message\":\"导入失败: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 获取对象字段值（使用反射重构）
     */
    private String getFieldValue(UserExcelDTO user, String field) {
        try {
            java.lang.reflect.Field fieldObj = user.getClass().getDeclaredField(field);
            fieldObj.setAccessible(true);
            Object value = fieldObj.get(user);
            return value != null ? value.toString() : null;
        } catch (NoSuchFieldException | IllegalAccessException e) {
            return "未知字段";
        }
    }


    /**
     * 生成用户测试数据
     */
    private List<UserExcelDTO> generateUserData(int count) {
        List<UserExcelDTO> dataList = new ArrayList<>();
        String[] genders = {"男", "女"};
        String[] addressPrefixes = {"北京市", "上海市", "广州市", "深圳市", "杭州市", "成都市", "武汉市", "南京市"};

        Random random = new Random();

        for (int i = 0; i < count; i++) {
            UserExcelDTO user = UserExcelDTO.builder()
                    .id((long) (i + 1))
                    .username("用户" + (i + 1))
                    .gender(genders[random.nextInt(genders.length)])
                    .age(18 + random.nextInt(60))
                    .phone("1" + (random.nextInt(9) + 1) + String.format("%09d", random.nextInt(1000000000)))
                    .email("user" + (i + 1) + "@example.com")
                    .address(addressPrefixes[random.nextInt(addressPrefixes.length)] + "某某区某某街道" + random.nextInt(100) + "号")
                    .birthday(LocalDateTime.ofInstant(Instant.ofEpochMilli(System.currentTimeMillis() - (random.nextInt(365 * 50) * 24L * 60L * 60L * 1000L)), ZoneId.systemDefault()))
                    .createTime(ZonedDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build();
            dataList.add(user);
        }

        return dataList;
    }

    /**
     * 自定义用户验证器
     */
    private static class CustomUserValidator implements ExcelValidator<UserExcelDTO> {
        @Override
        public ValidationResult validate(UserExcelDTO data) {
            ValidationResult result = ValidationResult.success();

            // 自定义验证逻辑
            if (data.getUsername() != null && data.getUsername().contains("admin")) {
                result.addError("username", "用户名不能包含admin");
            }

            if (data.getAge() != null && data.getAge() > 100) {
                result.addError("age", "年龄不能超过100岁");
            }

            return result;
        }
    }

}
