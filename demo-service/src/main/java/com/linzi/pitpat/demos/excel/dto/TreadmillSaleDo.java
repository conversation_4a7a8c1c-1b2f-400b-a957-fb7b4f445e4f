package com.linzi.pitpat.demos.excel.dto;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class TreadmillSaleDo {

    /**
     * 主键ID
     */
    @ExcelIgnore
    private Long id;
    /**
     * 是否删除（0否 1是）
     */
    @ExcelIgnore
    private Integer isDelete;
    /**
     * 创建时间
     */
    @ExcelProperty(value = "日期")
    private Date createTime;
    /**
     * 最后修改时间
     */
    @ExcelIgnore
    private Date modifieTime;

    @ExcelProperty(value = "AS01")
    private Integer As01;

    @ExcelProperty(value = "AS02")
    private Integer As02;

    @ExcelProperty(value = "AS03")
    private Integer As03;

    @ExcelProperty(value = "BA02")
    private Integer Ba02;

    @ExcelProperty(value = "BA03")
    private Integer Ba03;

    @ExcelProperty(value = "BA04")
    private Integer Ba04;

    @ExcelProperty(value = "BA05")
    private Integer Ba05;

    @ExcelProperty(value = "BA06")
    private Integer Ba06;

    @ExcelProperty(value = "BA07")
    private Integer Ba07;

    @ExcelProperty(value = "CT03")
    private Integer Ct03;

    @ExcelProperty(value = "CT04")
    private Integer Ct04;

    @ExcelProperty(value = "CT05")
    private Integer Ct05;

    @ExcelProperty(value = "CT07")
    private Integer Ct07;
}
