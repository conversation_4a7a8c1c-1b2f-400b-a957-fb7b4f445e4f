package com.linzi.pitpat.demos.api;

import com.linzi.pitpat.demos.constant.EventConstants;
import com.linzi.pitpat.demos.constant.QueueConstants;
import com.linzi.pitpat.demos.model.dto.UserDto;
import com.linzi.pitpat.framework.rabbitmq.QueueNameGenerator;
import com.linzi.pitpat.framework.rabbitmq.model.MessageWrapper;
import com.linzi.pitpat.framework.rabbitmq.service.QueueMessageService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;

/**
 * 消息组件演示控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/message")
@RequiredArgsConstructor
public class MessageDemoController {

    private final QueueMessageService queueMessageService;
    private final QueueNameGenerator nameGenerator;


    /**
     * 发送实时消息
     */
    @PostMapping("/send")
    public String sendMessage(@RequestBody MessageRequest request) {
        log.info("发送实时消息: {}", request);
        queueMessageService.sendMessage(request.getExchange(), request.getRoutingKey(), request.getPayload());
        queueMessageService.sendMessage(QueueConstants.USER_EXCHANGE, QueueConstants.USER_REGISTER_KEY, request.getPayload());

        return "消息已发送";
    }

    /**
     * 发送延迟消息
     */
    @PostMapping("/send/delay")
    public String sendDelayMessage( @RequestBody MessageRequest<UserDto> request,  @RequestParam(defaultValue = "5000") Long delayMillis) {
        log.info("发送延迟消息: {}, 延迟: {}ms", request, delayMillis);
        queueMessageService.sendDelayMessage(request.getExchange(), request.getRoutingKey(), request.getPayload(), delayMillis);
        queueMessageService.sendDelayMessage(QueueConstants.USER_EXCHANGE_DELAY, QueueConstants.USER_EMAIL_KEY, request.getPayload(), delayMillis);
        return "延迟消息已发送";
    }

    /**
     * 发送本地同步消息
     */
    @PostMapping("/send/local/sync")
    public String sendLocalSyncMessage(@RequestBody UserDto payload) {
        log.info("发送本地同步消息: {}", payload);
        queueMessageService.sendMessageEvent(EventConstants.SIMPLE_EVENT_TYPE, payload);
        return "本地同步消息已处理";
    }

    /**
     * 发送本地异步消息
     */
    @PostMapping("/send/local/async")
    public String sendLocalAsyncMessage(@RequestBody UserDto payload) {
        log.info("发送本地异步消息: {}", payload);
        queueMessageService.sendMessageEvent(EventConstants.SIMPLE_ASYNC_EVENT_TYPE, payload);
        return "本地异步消息已发送";
    }


    /**
     * 发送延迟消息(基于 redisson)
     */
    @PostMapping("/send/delay/redisson")
    public String sendRedissonMessage(@RequestBody MessageRequest request, @RequestParam(defaultValue = "5000") long delayMillis) {

        log.info("发送延迟消息: {}", request);
        Random random = new Random();
        IntStream.range(0, 100).forEach(item -> {
            int millis = random.nextInt(10_000, 100_000);
            queueMessageService.sendDelayMessageShort(request.getExchange(), request.getRoutingKey(), request.getPayload().toString().replace("%d", millis + ""), (long) millis);

        });


        IntStream.range(0, 100).forEach(item -> {
            int millis = random.nextInt(150_000, 300_000);
            queueMessageService.sendDelayMessageShort(request.getExchange(), request.getRoutingKey(), request.getPayload().toString().replace("%d", millis + ""), (long) millis);

        });
        return "发送延迟消息已发送";
    }

    /**
     * 发送长期延迟消息
     */
    @PostMapping("/long-delay/send")
    public Map<String, Object> sendLongDelayMessage(@RequestBody LongDelayMessageRequest<UserDto> request, @RequestParam("range") Integer range) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        IntStream.range(0, range).forEach(item -> {
            int scheduledTime = ThreadLocalRandom.current().nextInt(-10,20);
            MessageWrapper<UserDto> taskId = queueMessageService.sendDelayMessageLong(
                    request.getExchange(),
                    request.getRoutingKey(),
                    request.getPayload(),
                    TimeUnit.MINUTES.toMillis(scheduledTime) //延迟十分钟
            );
        });
        stopWatch.stop();

        double totalTimeSeconds = stopWatch.getTotalTimeSeconds();

        Map<String, Object> result = new HashMap<>();
        result.put("message", "长期延迟消息已创建");
        log.info("<UNK>: cost={}s, message={}", totalTimeSeconds, "长期延迟消息已创建");
        return result;
    }


    /**
     * 消息请求模型
     */
    @Data
    public static class MessageRequest<T> {
        private String exchange;
        private String routingKey;
        private T payload;
    }


    /**
     * 长期延迟消息请求
     */
    @Data
    public static class LongDelayMessageRequest<T> {
        private String messageType;
        private String title;
        private String exchange;
        private String routingKey;
        private T payload;
    }
}
