package com.linzi.pitpat.demos.listener;

import com.alibaba.nacos.common.utils.ConcurrentHashSet;
import com.fasterxml.jackson.core.type.TypeReference;
import com.linzi.pitpat.core.util.JsonUtil;
import com.linzi.pitpat.demos.model.dto.UserDto;
import com.linzi.pitpat.framework.rabbitmq.listener.MessageListener;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 示例消息监听器
 */
@Slf4j
@Component
public class SampleMessageListener extends MessageListener{
    private final ConcurrentHashSet<String> messageMap = new ConcurrentHashSet<>();

    /**
     * 监听示例队列
     */
    @RabbitListener(queues = "#{@nameGenerator.queueName(T(com.linzi.pitpat.demos.constant.QueueConstants).USER_REGISTER_QUEUE)}", containerFactory = "directRabbitListenerContainerFactory")
    public void onMessage(@Payload Message message, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) {
        parseMessage(message,  deliveryTag, channel);
    }

    /**
     * 监听示例队列
     */
    @RabbitListener(queues = "#{@nameGenerator.queueName(T(com.linzi.pitpat.demos.constant.QueueConstants).USER_EMAIL_QUEUE)}", containerFactory = "directRabbitListenerContainerFactory")
    public void onOrderMessage(@Payload Message message, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) {
        parseMessage(message, deliveryTag, channel);
    }

    @Override
    protected void handleMessage(String message) {
        UserDto userDto = JsonUtil.readValue(message, new TypeReference<>() {
        });
        Objects.requireNonNull(userDto);
        log.info("处理用户消息: {}, class={}", userDto, userDto.getClass().getName());
    }


}
