package com.linzi.pitpat.demos.api;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
@RestController
@RequestMapping("/api")
class BenchmarkController {

    @GetMapping("/test")
    public Map<String, Object> test() {
        // 模拟一些处理时间 (10-30ms)
        try {
            Thread.sleep((long) (Math.random() * 20 + 10));
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        Map<String, Object> response = new HashMap<>();
        response.put("id", UUID.randomUUID().toString());
        response.put("timestamp", System.currentTimeMillis());
        response.put("message", "Hello from benchmark API");
        log.info("response={}",response);
        return response;
    }

    @GetMapping("/heavy")
    public Map<String, Object> heavyTest() {
        // 模拟较重的处理 (50-150ms)
        try {
            Thread.sleep((long) (Math.random() * 100 + 50));
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        Map<String, Object> response = new HashMap<>();
        response.put("id", UUID.randomUUID().toString());
        response.put("timestamp", System.currentTimeMillis());
        response.put("message", "Heavy processing complete");
        response.put("data", generateRandomData());
        return response;
    }

    private String[] generateRandomData() {
        String[] data = new String[10];
        for (int i = 0; i < data.length; i++) {
            data[i] = UUID.randomUUID().toString();
        }
        return data;
    }
}
