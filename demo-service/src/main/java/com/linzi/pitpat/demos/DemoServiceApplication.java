package com.linzi.pitpat.demos;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import java.time.Instant;

@SpringBootApplication(scanBasePackages = "com.linzi.pitpat")
@MapperScan({"com.linzi.pitpat.**.mapper"})// 指定mapper接口所在的包
@Slf4j
public class DemoServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(DemoServiceApplication.class, args);
        log.info("启动完毕start ready, {}, {}, {}", Instant.now().toEpochMilli(), "<EMAIL>", "https://pitpat-oss.s3.us-east-2.amazonaws.com/1751870885729.jpg");
        log.warn("启动完毕start ready, {}", Instant.now().toEpochMilli());
    }
}
