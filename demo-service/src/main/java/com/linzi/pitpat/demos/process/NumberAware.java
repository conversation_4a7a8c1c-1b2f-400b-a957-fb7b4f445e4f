package com.linzi.pitpat.demos.process;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

@Slf4j
public class NumberAware {
    public void processAllFields(Object object) {
        processFields(object);
    }

   private static void  processFields(Object obj) {
        // Process direct fields
        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            log.info("field name={}", field.getName());
            field.setAccessible(true);
            processField(field, obj);
            processCollectionField(field, obj);
        }
    }

   private static void  processField(Field field, Object obj) {
        Class<?> type = field.getType();
        if (isProcessableType(type)) {
            try {
                Object value = field.get(obj);
                Object processed = processValue(value, field);
                field.set(obj, processed);
            } catch (IllegalAccessException e) {
                throw new RuntimeException("Failed to process field: " + field.getName(), e);
            }
        }
    }

   private static void  processCollectionField(Field field, Object obj) {
        if (Collection.class.isAssignableFrom(field.getType())) {
            try {
                Collection<?> collection = (Collection<?>) field.get(obj);
                if (collection != null) {
                    collection.forEach(NumberAware::processFields);
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException("Failed to process collection field: " + field.getName(), e);
            }
        }
    }

    private static boolean isProcessableType(Class<?> type) {
        return type == BigDecimal.class
                || type == Double.class
                || type == Float.class
                || type == double.class
                || type == float.class;
    }

    public static Object processValue(Object value, Field field) {
        if (value == null) return null;

        DecimalScale decimal = field.getAnnotation(DecimalScale.class);
        final int scale = Objects.isNull(decimal) ? 2 : decimal.scale();

        if (value instanceof BigDecimal bigDecimal) {
            return bigDecimal.setScale(scale, RoundingMode.HALF_UP);
        } else if (value instanceof Float) {
            BigDecimal bigDecimal = BigDecimal.valueOf(((Number) value).floatValue());
            return bigDecimal.setScale(scale, RoundingMode.HALF_UP).floatValue();
        } else if (value instanceof Double) {
            BigDecimal bigDecimal = BigDecimal.valueOf(((Number) value).doubleValue());
            return bigDecimal.setScale(scale, RoundingMode.HALF_UP).doubleValue();
        }

        return value;
    }

    public static void main(String[] args) {
        OrderItem orderItem = new OrderItem();
        orderItem.setPrice(new BigDecimal("52.02045"));
        orderItem.setQuantity(88.289f);

        Order order = new Order();
        order.setItems(List.of(orderItem));
        order.setDiscountRate(0.555555D);
        order.setTotalAmount(new BigDecimal("52.********"));

        User user = new User();
        user.setRating(0.2456f);
        user.setSalary(35.222D);
        user.setAccountBalance(new BigDecimal("1103.09024"));
        user.setOrders(List.of(order));
        NumberAware numberAware = new NumberAware();
        numberAware.processAllFields(user);
        log.info("user={}", user);
    }
}
