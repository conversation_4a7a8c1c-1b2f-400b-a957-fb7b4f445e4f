package com.linzi.pitpat.demos.excel.dto;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.Date;

/**
 * 带下拉选择的Excel数据模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SelectExcelDTO {

    /**
     * ID，导出时忽略
     */
    @ExcelIgnore
    private Long id;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名", index = 0)
    @NotBlank(message = "姓名不能为空")
    private String name;

    /**
     * 性别，使用下拉框
     */
    @ExcelProperty(value = "性别", index = 1)
    @NotBlank(message = "性别不能为空")
    @Pattern(regexp = "^[男女]$", message = "性别只能是男或女")
    private String gender;

    /**
     * 学历，使用下拉框
     */
    @ExcelProperty(value = "学历", index = 2)
    @NotBlank(message = "学历不能为空")
    private String education;

    /**
     * 出生日期
     */
    @ExcelProperty(value = "出生日期", index = 3)
    private Date birthdate;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注", index = 4)
    private String remark;

    /**
     * 创建时间，导出时忽略
     */
    @ExcelIgnore
    private Date createTime;

    /**
     * 更新时间，导出时忽略
     */
    @ExcelIgnore
    private Date updateTime;
}
