package com.linzi.pitpat.demos.base;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

public enum UserStatus implements Ienum<Integer> {

    NORMAL(1, "正常"),
    LOck(2, "账户已锁定"),
    ;

    private Integer status;
    private String name;

    UserStatus(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static Integer[] array() {
        return Arrays.stream(values()).map(UserStatus::getStatus).toArray(Integer[]::new);
    }

    public static Integer resolve(Integer param) {
        Optional<Integer> optional = Arrays.stream(array()).filter(item -> Objects.equals(item, param)).findFirst();
        return optional.orElse(null);
    }

}
