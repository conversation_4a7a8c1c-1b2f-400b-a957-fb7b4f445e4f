package com.linzi.pitpat.demos;

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.dto.WeekDayDateDto;
import com.linzi.pitpat.demos.util.DateTimeUtil;
import org.junit.jupiter.api.Test;

import java.time.ZoneId;
import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试 DateUtil 中基于 ZonedDateTime 的 getNumWeek 方法
 */
public class DateUtilZonedDateTimeTest {

    @Test
    public void testGetNumWeekWithZonedDateTime() {
        // 测试当前时间
        ZonedDateTime now = ZonedDateTime.now();
        WeekDayDateDto result = DateTimeUtil.getNumWeek(now);

        assertNotNull(result);
        assertNotNull(result.getWeekStart());
        assertNotNull(result.getWeekEnd());
        assertNotNull(result.getBeijingWeekStart());
        assertNotNull(result.getBeijingWeekEnd());
        assertTrue(result.getWeekN() > 0);
        assertTrue(result.getWeek() >= 1 && result.getWeek() <= 7);
        assertNotNull(result.getCurrentMonth());
        assertTrue(result.getMonth() >= 1 && result.getMonth() <= 12);
        assertNotNull(result.getWeekStartStr());
        assertNotNull(result.getWeekEndStr());

        System.out.println("当前时间测试结果:");
        System.out.println("周开始: " + result.getWeekStart());
        System.out.println("周结束: " + result.getWeekEnd());
        System.out.println("北京时间周开始: " + result.getBeijingWeekStart());
        System.out.println("北京时间周结束: " + result.getBeijingWeekEnd());
        System.out.println("第几周: " + result.getWeekN());
        System.out.println("星期几: " + result.getWeek());
        System.out.println("当前月份: " + result.getCurrentMonth());
        System.out.println("月份数字: " + result.getMonth());
    }

    @Test
    public void testGetNumWeekWithSpecificDate() {
        // 测试特定日期：2024年1月15日（周一）
        ZonedDateTime specificDate = ZonedDateTime.of(2024, 1, 15, 10, 30, 0, 0, ZoneId.systemDefault());
        WeekDayDateDto result = DateTimeUtil.getNumWeek(specificDate);

        assertNotNull(result);
        assertEquals(1, result.getWeek()); // 周一
        assertEquals("2024-01", result.getCurrentMonth());
        assertEquals(1, result.getMonth());

        System.out.println("\n特定日期测试结果 (2024-01-15):");
        System.out.println("周开始: " + result.getWeekStart());
        System.out.println("周结束: " + result.getWeekEnd());
        System.out.println("第几周: " + result.getWeekN());
        System.out.println("星期几: " + result.getWeek());
        System.out.println("当前月份: " + result.getCurrentMonth());
    }

    @Test
    public void testGetNumWeekWithPreDate() {
        ZonedDateTime currentDate = ZonedDateTime.now();
        ZonedDateTime preDate = currentDate.minusDays(1);

        WeekDayDateDto result = DateTimeUtil.getNumWeek(currentDate, preDate);

        assertNotNull(result);
        // 验证使用了preDate的星期几

        System.out.println("\n带preDate测试结果:");
        System.out.println("当前日期: " + currentDate);
        System.out.println("前一日期: " + preDate);
        System.out.println("结果中的星期几: " + result.getWeek());
    }

}
