package com.linzi.pitpat.demos;

import com.linzi.pitpat.core.util.DateUtil;
import com.linzi.pitpat.core.util.NumberUtils;
import com.linzi.pitpat.core.util.dto.DateStartEnd;
import com.linzi.pitpat.core.util.dto.DateTimeStartEnd;
import com.linzi.pitpat.core.util.dto.West8StartEndDto;
import com.linzi.pitpat.demos.util.DateTimeUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.TimeZone;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
public class DateMigrateTest extends DemoServiceApplicationTests {
    String str = "2024-10-01 09:00:02";

    /**
     * 测试 字符串解析为日期
     * DateUtil.getDate
     * DateTimeUtil.parse
     */
    @Test
    public void testGetDate() {

        Date date = DateUtil.getDate(str);
        ZonedDateTime zonedDateTime = DateTimeUtil.parse(str);
        log.info("date={}", date);
        log.info("zonedDateTime={}", zonedDateTime);
        assertEquals(date.getTime(), zonedDateTime.toInstant().toEpochMilli());
    }

    @Test
    public void testParseDate() {
        Date date = DateUtil.parseDate(str);
        ZonedDateTime zonedDateTime = DateTimeUtil.parse(str);
        log.info("date={}", date);
        log.info("zonedDateTime={}", zonedDateTime);
        assertEquals(date.getTime(), zonedDateTime.toInstant().toEpochMilli());
    }

    @Test
    public void testGetStampByZone() {
        ZonedDateTime parse = DateTimeUtil.parse(str, "UTC");
        ZonedDateTime parse1 = DateTimeUtil.parse(DateUtil.getStampByZone(str, "UTC"));

        log.info("parse={}", parse);
        log.info("parse1={}", parse1);
        assertEquals(parse, parse1);
    }

    @Test
    public void testPattern() {
        ZonedDateTime parse = DateTimeUtil.parse(str);
        ZonedDateTime parse1 = DateTimeUtil.parseWithPattern(str, "yyyy-MM-dd HH:mm:ss");

        log.info("parse={}", parse);
        log.info("parse1={}", parse1);
        assertEquals(parse, parse1);
    }

    @Test
    public void testParseDateToStr() {
        String parse = DateUtil.parseDateToStr(com.linzi.pitpat.core.util.DateUtil.YYYYMMDD, com.linzi.pitpat.core.util.DateUtil.getDate(str));
        String parse1 = DateTimeUtil.formatWithPattern(DateTimeUtil.parse(str), DateUtil.YYYYMMDD);
        log.info("parse={}", parse);
        log.info("parse1={}", parse1);
        assertEquals(parse, parse1);
    }

    @Test
    public void testParseString() {
        String str = "2025-07-30T23:48:32.060Z";
        String str1 = "2025-07-26T18:50:46Z";
        ZonedDateTime zonedDateTime = DateTimeUtil.parseWithPattern(str, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        log.info("zonedDateTime={}", zonedDateTime);
        ZonedDateTime zonedDateTime1 = DateTimeUtil.parseWithPattern(str1, "yyyy-MM-dd'T'HH:mm:ss'Z'");
        log.info("zonedDateTime={}", zonedDateTime1);


    }

    @Test
    public void testParseYearMonthWithPattern() {
        String date = "2024-10";
        ZonedDateTime zonedDateTime = DateTimeUtil.parseYearMonthWithPattern(date, "yyyy-MM");
        log.info("zonedDateTime={}", zonedDateTime);

        date = "2024年10月";

        ZonedDateTime zonedDateTime1 = DateTimeUtil.parseYearMonthWithPattern(date, "yyyy年MM月");
        log.info("zonedDateTime={}", zonedDateTime1);

        try {
            Date date2 = DateUtil.parseDate("2024年10月", "yyyy年MM月");
            log.info("date={}", date2);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

    }

    @Test
    public void testMinute() {
        Date date = new Date();
        ZonedDateTime zonedDateTime = date.toInstant().atZone(java.time.ZoneId.systemDefault());
        Date date1 = DateUtil.addMinutes(date, 5);
        ZonedDateTime zonedDateTime1 = zonedDateTime.plusMinutes(5);

        log.info("date1={}", date1);
        log.info("zonedDateTime1={}", zonedDateTime1);
        assertEquals(date1.getTime(), zonedDateTime1.toInstant().toEpochMilli());

    }

    @Test
    public void testformateDate() {
        Date date1 = DateUtil.formateDate(str, com.linzi.pitpat.core.util.DateUtil.YYYY_MM_DD_HH_MM_SS);
        ZonedDateTime zonedDateTime1 = DateTimeUtil.parse(str);
        log.info("date1={}", date1);
        log.info("zonedDateTime1={}", zonedDateTime1);
        assertEquals(date1.getTime(), zonedDateTime1.toInstant().toEpochMilli());

        Date runDate = DateUtil.formateDate(new Date(), DateUtil.YYYY_MM_DD_HH_MM);
        ZonedDateTime runDat1e = ZonedDateTime.now().withSecond(0).withNano(0);
        ZonedDateTime runDat2e = ZonedDateTime.now().truncatedTo(ChronoUnit.MINUTES);
        log.info("runDate={}", runDate);
        log.info("runDate={}", runDat1e);
        log.info("runDate={}", runDat2e);

        assertEquals(runDate.getTime(), runDat1e.toInstant().toEpochMilli());
        assertEquals(runDate.getTime(), runDat2e.toInstant().toEpochMilli());

    }

    @Test
    public void testDateStr() {
        Date date = new Date();
        ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.systemDefault());
        String dateStr = DateUtil.dateStr(date, DateUtil.YYYY_MM_DD_HH_MM_SS);
        String dateTimeStr = DateTimeUtil.format(zonedDateTime);
        String dateTimeStr2 = DateTimeUtil.formatWithPattern(zonedDateTime, DateUtil.YYYY_MM_DD_DOT);

        log.info("date1={}", dateStr);
        log.info("zonedDateTime1={}", dateTimeStr);
        log.info("zonedDateTime2={}", dateTimeStr2);
        assertEquals(dateTimeStr, dateStr);
    }

    @Test
    public void parseDateToStr() {
        Date date = new Date();
        ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.systemDefault());
        String dateStr = DateUtil.parseDateToStr(DateUtil.MM_DD, date);
        String dateTimeStr = DateTimeUtil.formatWithPattern(zonedDateTime, DateUtil.MM_DD);

        log.info("date1={}", dateStr);
        log.info("zonedDateTime1={}", dateTimeStr);
        assertEquals(dateTimeStr, dateStr);
    }

    @Test
    public void testDate() {

        Date date = new Date(DateUtil.getStampByZone(str, "UTC"));
        ZonedDateTime zonedDateTime = DateTimeUtil.parse(str, "UTC");

        log.info("date1={}", date);
        log.info("zonedDateTime1={}", zonedDateTime);
        assertEquals(date.getTime(), zonedDateTime.toInstant().toEpochMilli());
    }


    @Test
    public void testDateAdd() {
        Date date = new Date();
        ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.systemDefault());
        Date dateAdd = DateUtil.addHours(date, 10);
        ZonedDateTime zonedDateTimeAdd = zonedDateTime.plusHours(10);
        log.info("date1={}", zonedDateTimeAdd);
        log.info("zonedDateTime1={}", zonedDateTime);
        assertEquals(dateAdd.getTime(), zonedDateTimeAdd.toInstant().toEpochMilli());
    }

    @Test
    public void testParseStr2Date() {
        Date date = new Date();
        ZonedDateTime now = date.toInstant().atZone(ZoneId.systemDefault());
        YearMonth yearMonth = YearMonth.from(now);
        HashMap<YearMonth, String> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put(yearMonth, "true");
        log.info("yearMonth={},{}", yearMonth, yearMonth.toString());
        assertNotNull(objectObjectHashMap.get(yearMonth));
        //Date date = DateUtil.parseStr2Date(yearMonth, "yyyy年MM月");


        String str = DateTimeUtil.formatWithPattern(now, "yyyy年MM月");
        log.info("str={}", str);
        ZonedDateTime zonedDateTime = DateTimeUtil.parseYearMonthWithPattern(str, "yyyy年MM月");
        log.info("zonedDateTime={}", zonedDateTime.toInstant().toEpochMilli());


        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月");
        String yearMonth2 = simpleDateFormat.format(date);
        Date date1 = DateUtil.parseStr2Date(yearMonth2, "yyyy年MM月");
        log.info("date1={}", zonedDateTime.toInstant().toEpochMilli());


        YearMonth yearMonthDate = YearMonth.from(now);
        ZonedDateTime gmtCreate = yearMonthDate.atDay(1).atStartOfDay(ZoneId.systemDefault());


        assertEquals(zonedDateTime.toInstant().toEpochMilli(), date1.getTime());
        assertEquals(gmtCreate.toInstant().toEpochMilli(), date1.getTime());
    }

    @Test
    public void testAddWeek() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());
        Date dateAdd = DateUtil.addWeeks(date, 1);
        ZonedDateTime dateTimeAdd = dateTime.plusWeeks(1);

        log.info("date1={}", dateAdd);
        log.info("zonedDateTime1={}", dateTimeAdd);
        assertEquals(dateAdd.getTime(), dateTimeAdd.toInstant().toEpochMilli());
    }

    @Test
    public void testAddDays1() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());
        Date dateAdd = DateUtil.addDays1(date, 1);
        ZonedDateTime dateTimeAdd = dateTime.plusDays(1);

        log.info("date1={}", dateAdd);
        log.info("zonedDateTime1={}", dateTimeAdd);
        assertEquals(dateAdd.getTime(), dateTimeAdd.toInstant().toEpochMilli());
    }

    @Test
    public void testStartOfDate() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());
        Date dateAdd = DateUtil.startOfDate(date);
        ZonedDateTime dateTimeAdd = dateTime.with(LocalTime.MIN);

        log.info("date1={}", dateAdd);
        log.info("zonedDateTime1={}", dateTimeAdd);
        assertEquals(dateAdd.getTime(), dateTimeAdd.toInstant().toEpochMilli());
    }

    @Test
    public void getStartOfDate() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());
        Date dateAdd = DateUtil.getStartOfDate(date);
        ZonedDateTime dateTimeAdd = dateTime.with(LocalTime.MIN);

        log.info("date1={}", dateAdd);
        log.info("zonedDateTime1={}", dateTimeAdd);
        assertEquals(dateAdd.getTime(), dateTimeAdd.toInstant().toEpochMilli());
    }


    @Test
    public void endOfDate() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());
        Date dateAdd = DateUtil.endOfDate(date);
        ZonedDateTime dateTimeAdd = dateTime.with(LocalTime.MAX);

        log.info("date1={}", dateAdd);
        log.info("zonedDateTime1={}", dateTimeAdd);
        assertEquals(dateAdd.getTime(), dateTimeAdd.toInstant().toEpochMilli());
    }


    //


    //

    /**
     * 测试  ZonedDateTime 转换为 Date
     * DateUtil.toDate
     */
    @Test
    public void testToDate() {
        Date date1 = new Date();

        ZonedDateTime now = ZonedDateTime.now();
        Date date = DateUtil.toDate(now);
        log.info("date={}", date);
        log.info("date1={}", date1);
        assertEquals(date.getTime(), now.toInstant().toEpochMilli());


        ZonedDateTime now2 = DateTimeUtil.parse(Instant.ofEpochMilli(date1.getTime()));
        Date date2 = new Date(date1.getTime());

        log.info("date={}", date2);
        log.info("date1={}", now2);
        assertEquals(date2.getTime(), now2.toInstant().toEpochMilli());
    }

    @Test
    public void switchZone() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date date1 = DateUtil.getDate2ByTimeZone(date, TimeZone.getTimeZone("UTC"));
        ZonedDateTime dateTime1 = DateTimeUtil.withZoneSameLocal(dateTime, "UTC");

        log.info("Date={},{}", date1, date1.getTime());
        log.info("ZonedDateTime={},{}", dateTime1, dateTime1.toInstant().toEpochMilli());

        assertEquals(date.getTime() / 1000, dateTime.toEpochSecond());
    }


    @Test
    public void getStartOfDateWithZoneId() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date startTime = DateUtil.getStartOfDate(date, TimeZone.getTimeZone("UTC"));
        Date startTime1 = DateUtil.getStartOfDate(date);
        log.info("date={},{}", startTime, startTime.getTime());
        log.info("date={},{}", startTime1, startTime1.getTime());


        //需要获取 给定时区的 日期开始时间
        //1. 将基于本地时区的时间 转换成 指定时区的开始时间， 然后将其再换为本地时区的开始
        ZonedDateTime with = dateTime.withZoneSameLocal(ZoneId.of("UTC")).with(LocalTime.MIN);
        ZonedDateTime dateTime1 = DateTimeUtil.withZoneSameLocal(with, "GMT+8");

        log.info("dateTime={},{}", dateTime, dateTime.toInstant().toEpochMilli());
        log.info("dateTime={},{}", with, with.toInstant().toEpochMilli());
        log.info("dateTime={},{}", dateTime1, dateTime1.toInstant().toEpochMilli());

        assertEquals(startTime.getTime() / 1000, dateTime1.toEpochSecond());
    }


    @Test
    public void getEndOfDateWithZoneId() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date startTime = DateUtil.getEndOfDate(date, TimeZone.getTimeZone("UTC"));
        Date startTime1 = DateUtil.getEndOfDate(date);
        log.info("date={},{}", startTime, startTime.getTime());
        log.info("date={},{}", startTime1, startTime1.getTime());


        //需要获取 给定时区的 日期开始时间
        //1. 将基于本地时区的时间 转换成 指定时区的开始时间， 然后将其再换为本地时区的开始
        ZonedDateTime with = dateTime.withZoneSameLocal(ZoneId.of("UTC")).with(LocalTime.MAX);
        ZonedDateTime dateTime1 = DateTimeUtil.withZoneSameLocal(with, "GMT+8");

        log.info("dateTime={},{}", with, with.toInstant().toEpochMilli());
        log.info("dateTime={},{}", dateTime1, dateTime1.toInstant().toEpochMilli());

        assertEquals(startTime.getTime() / 1000, dateTime1.toEpochSecond());

        assertEquals(with.toEpochSecond(), dateTime1.toEpochSecond());
    }

    @Test
    public void getDate2ByTimeZone() {
        Date now = DateUtil.getDate2ByTimeZone(new Date(), TimeZone.getTimeZone("UTC"));
        Date startOfDate = DateUtil.getStartOfDate(now, TimeZone.getTimeZone("UTC"));
        Date endOfDate = DateUtil.getEndOfDate(now, TimeZone.getTimeZone("UTC"));

        log.info("startOfDate={},{}", startOfDate, startOfDate.getTime());
        log.info("endOfDate={},{}", endOfDate, endOfDate.getTime());

        Date startOfDate1 = DateUtil.getStartOfDate(new Date(), TimeZone.getTimeZone("UTC"));
        Date endOfDate1 = DateUtil.getEndOfDate(new Date(), TimeZone.getTimeZone("UTC"));

        log.info("startOfDate={},{}", startOfDate1, startOfDate1.getTime());
        log.info("endOfDate={},{}", endOfDate1, endOfDate1.getTime());
    }

    @Test
    public void testSwtich() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());
        log.info("dateTime={},{}", dateTime, dateTime.toInstant().toEpochMilli());

        ZonedDateTime with1 = dateTime.withZoneSameLocal(ZoneId.of("UTC"));
        ZonedDateTime with2 = dateTime.withZoneSameInstant(ZoneId.of("UTC"));
        ZonedDateTime with12 = dateTime.withZoneSameInstant(ZoneId.of("UTC")).withZoneSameLocal(ZoneId.systemDefault());
        ZonedDateTime with13 = DateTimeUtil.withZoneSameLocal(dateTime, "UTC");

        log.info("with1={},{}", with1, with1.toInstant().toEpochMilli());
        log.info("with2={},{}", with2, with2.toInstant().toEpochMilli());
        log.info("with12={},{}", with12, with12.toInstant().toEpochMilli());
        log.info("with13={},{}", with13, with13.toInstant().toEpochMilli());

        //需要获取 给定时区的 日期开始时间
        //1. 将基于本地时区的时间 转换成 指定时区的开始时间， 然后将其再换为本地时区的开始
        ZonedDateTime withStart = with1.with(LocalTime.MIN);
        ZonedDateTime withStart1 = DateTimeUtil.withZoneSameLocal(withStart, "GMT+8");

        ZonedDateTime withEnd = with1.with(LocalTime.MAX);
        ZonedDateTime withEnd1 = DateTimeUtil.withZoneSameLocal(withEnd, "GMT+8");

        log.info("dateTime={},{}", withStart, withStart.toInstant().toEpochMilli());
        log.info("dateTime={},{}", withEnd, withEnd.toInstant().toEpochMilli());


        log.info("dateTime={},{}", withStart1, withStart1.toInstant().toEpochMilli());
        log.info("dateTime={},{}", withEnd1, withEnd1.toInstant().toEpochMilli());

    }

    @Test
    public void getDateOfMonth() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());
        int dateOfMonth = DateUtil.getDateOfMonth(date);
        int monthValue = dateTime.getMonthValue();
        log.info("dateTime={}", dateOfMonth);
        log.info("dateTime={}", monthValue);

        assertEquals(dateOfMonth, monthValue);
    }

    @Test
    public void getDateOfDay() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());
        int dateOfMonth = DateUtil.getDateOfDay(date);
        int monthValue = dateTime.getDayOfMonth();
        log.info("dateTime={}", dateOfMonth);
        log.info("dateTime={}", monthValue);

        assertEquals(dateOfMonth, monthValue);
    }


    //DateUtil.DateUtil.getDate2ByTimeZone(activityStartTime, TimeZone.getTimeZone(zoneId));
    @Test
    public void testSwtich1() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        //ZonedDateTime zonedDateTime = DateTimeUtil.withZoneSameLocal(dateTime, "UTC");
        ZonedDateTime zonedDateTime = dateTime.withZoneSameInstant(ZoneId.of("UTC"));
        ZonedDateTime start = zonedDateTime.with(LocalTime.MIN);
        ZonedDateTime end = zonedDateTime.with(LocalTime.MAX);

        log.info("dateTime={},{}", dateTime, dateTime.toInstant().toEpochMilli());
        log.info("zonedDateTime={},{}", zonedDateTime, zonedDateTime.toInstant().toEpochMilli());
        log.info("start={},{}", start, start.toInstant().toEpochMilli());
        log.info("end={},{}", end, end.toInstant().toEpochMilli());

        ZonedDateTime start1 = start.withZoneSameInstant(ZoneId.systemDefault());
        ZonedDateTime end1 = end.withZoneSameInstant(ZoneId.systemDefault());
        log.info("start1={},{}", start1, start1.toInstant().toEpochMilli());
        log.info("end1={},{}", end1, end1.toInstant().toEpochMilli());
    }

    @Test
    public void getDate2ByTimeZone1() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date date1 = DateUtil.getDate2ByTimeZone(date, TimeZone.getTimeZone("UTC"));

        //ZonedDateTime zonedDateTime = dateTime.withZoneSameInstant(ZoneId.of("UTC")).withZoneSameLocal(ZoneId.systemDefault());
        ZonedDateTime zonedDateTime = DateTimeUtil.withZoneSameLocal(dateTime, "UTC");
        log.info("start1={},{}", date1, date1.toInstant().toEpochMilli());
        log.info("start1={},{}", date1, date1.toInstant().toEpochMilli());
        log.info("end1={},{}", zonedDateTime, zonedDateTime.toInstant().toEpochMilli());
        assertEquals(date1.toInstant().getEpochSecond(), date1.toInstant().getEpochSecond());

        // 2025-08-07T07:32:46.000+0800
    }

    @Test
    public void firstDayOfMonth() {
        ZonedDateTime zonedDateTime = ZonedDateTime.now().withZoneSameInstant(ZoneId.of("GMT-8"));
        ZonedDateTime startOfMonth = DateTimeUtil.firstDayOfMonth(zonedDateTime).withZoneSameInstant(ZoneId.systemDefault());
        ZonedDateTime endOfMonth = DateTimeUtil.lastDayOfMonth(zonedDateTime).withZoneSameInstant(ZoneId.systemDefault());
        log.info("startOfMonth={},{}", startOfMonth, startOfMonth.toInstant().toEpochMilli());
        log.info("endOfMonth={},{}", endOfMonth, endOfMonth.toInstant().toEpochMilli());

    }

    @Test
    public void getFirstOfMonth_getEndOfMonth() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());
        String zoneId = "GMT-8";
        TimeZone timeZone = TimeZone.getTimeZone(zoneId);
        int hour = timeZone.getRawOffset() / (60 * 60 * 1000);
        Date now = DateUtil.getDate2ByTimeZone(date, timeZone);
        Date startOfMonth = DateUtil.addHours(DateUtil.getFirstOfMonth(now), -hour);
        Date endOfMonth = DateUtil.addHours(DateUtil.getEndOfMonth(now), -hour);

        log.info("startOfMonth={},{}", startOfMonth, startOfMonth.toInstant().toEpochMilli());
        log.info("endOfMonth={},{}", endOfMonth, endOfMonth.toInstant().toEpochMilli());

        //转成用户时区的日期
        ZonedDateTime zonedDateTime = dateTime.withZoneSameInstant(ZoneId.of(zoneId));
        //获取用户的日期对应的当月开始时间，并转回到系统时间
        ZonedDateTime startOfMonth1 = DateTimeUtil.firstDayOfMonth(zonedDateTime).withZoneSameInstant(ZoneId.systemDefault());
        //获取用户的日期对应的当月截止时间，并转回到系统时间
        ZonedDateTime endOfMonth1 = DateTimeUtil.lastDayOfMonth(zonedDateTime).withZoneSameInstant(ZoneId.systemDefault());

        log.info("startOfMonth1={},{}", startOfMonth1, startOfMonth1.toInstant().toEpochMilli());
        log.info("endOfMonth1={},{}", endOfMonth1, endOfMonth1.toInstant().toEpochMilli());

        assertEquals(startOfMonth.toInstant().getEpochSecond(), startOfMonth1.toInstant().getEpochSecond());
        assertEquals(endOfMonth.toInstant().getEpochSecond(), endOfMonth1.toInstant().getEpochSecond());
    }

    @Test
    public void addSeconds() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date dateAdd = DateUtil.addSeconds(date, 10);
        ZonedDateTime dateTimeAdd = dateTime.plusSeconds(10);

        log.info("startOfMonth={},{}", dateAdd, dateAdd.toInstant().toEpochMilli());
        log.info("endOfMonth={},{}", dateTimeAdd, dateTimeAdd.toInstant().toEpochMilli());
        assertEquals(dateAdd.toInstant().getEpochSecond(), dateTimeAdd.toInstant().getEpochSecond());


    }

    @Test
    public void getAddHours() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date date1 = DateUtil.getAddHours(3);
        ZonedDateTime zonedDateTime = dateTime.plusDays(3).truncatedTo(ChronoUnit.HOURS);


        log.info("date1={},{}", date1, date1.toInstant().toEpochMilli());
        log.info("zonedDateTime={},{}", zonedDateTime, zonedDateTime.toInstant().toEpochMilli());
    }

    //

    @Test
    public void formatDate() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        String s = DateUtil.formatDate(date, DateUtil.DATE_TIME_SHORT);
        String ss = DateTimeUtil.formatWithPattern(dateTime, DateUtil.DATE_TIME_SHORT);

        log.info("date1={}", s);
        log.info("date1={}", ss);
        log.info("{}", dateTime);

        assertEquals(ss, s);

    }

    @Test
    public void convertTimeZoneToString() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        String date1 = DateUtil.convertTimeZoneToString(date, "UTC", "GMT-8");

        ZonedDateTime zonedDateTime = DateTimeUtil.withZoneSameLocal(dateTime, "GMT-8");
        String dateTime1 = DateTimeUtil.formatWithPattern(zonedDateTime, "MMMM dd, yyyy HH:mm:ss");

        log.info("date1={}", date1);
        log.info("dateTime1={}", dateTime1);

        assertEquals(date1, dateTime1);

    }

    @Test
    public void getDate3ByTimeZone() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        String date1 = DateUtil.getDate3ByTimeZone(date, TimeZone.getTimeZone("GMT-8"), "yyyy-MM-dd HH:mm:ss");

        String dateTime1 = DateTimeUtil.format(dateTime, "GMT-8", "yyyy-MM-dd HH:mm:ss");
        log.info("date1={}", date1);
        log.info("dateTime1={}", dateTime1);

        assertEquals(date1, dateTime1);
    }


    @Test
    public void formatMinites() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date date1 = DateUtil.formatMinites(date);
        ZonedDateTime dateTime1 = dateTime.truncatedTo(ChronoUnit.MINUTES);

        log.info("date1={}, {}", date1, date1.getTime());
        log.info("dateTime1={},{}", dateTime1, dateTime1.toInstant().toEpochMilli());

        assertEquals(date1.toInstant(), dateTime1.toInstant());
    }


    @Test
    public void isBetweenE() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        boolean dateE = DateUtil.isBetweenE(date, DateUtil.addSeconds(date, -1), DateUtil.addSeconds(date, 1));
        boolean dateTimeE = DateTimeUtil.inRange(dateTime, dateTime.plusSeconds(-1), dateTime.plusSeconds(1));

        log.info("date1={}", dateE);
        log.info("dateTime1={}", dateTimeE);

        assertEquals(dateE, dateTimeE);
    }

    @Test
    public void isBetweenE2() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        boolean dateE = DateUtil.isBetweenE(date, new Date(), date);
        boolean dateTimeE = DateTimeUtil.inRange(dateTime, ZonedDateTime.now(), dateTime);

        log.info("date1={}", dateE);
        log.info("dateTime1={}", dateTimeE);

        assertEquals(dateE, dateTimeE);
    }

    @Test
    public void isBetweenE3() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        boolean dateE = DateUtil.isBetweenE(date, DateUtil.addSeconds(new Date(), -5), date);
        boolean dateTimeE = DateTimeUtil.inRange(dateTime, ZonedDateTime.now().minusSeconds(5), dateTime);

        log.info("date1={}", dateE);
        log.info("dateTime1={}", dateTimeE);

        assertEquals(dateE, dateTimeE);
    }

    @Test
    public void betweenStartDay() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        long days = Duration.between(dateTime.with(LocalTime.MIN), dateTime.with(LocalTime.MAX)).toDays();
        long days2 = Duration.between(dateTime.with(LocalTime.MIN), dateTime.with(LocalTime.MAX).plusDays(1)).toDays();
        log.info("date1={}", days);
        log.info("dateTime1={}", days2);

        //assertEquals(dateE,dateTimeE);
    }

    @Test
    public void betweenStartDay2() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date sameDay = DateUtil.getSameDay(date);
        log.info("date1={}", date);
        log.info("dateTime1={}", sameDay);

        //assertEquals(dateE,dateTimeE);
    }

    @Test
    public void getCurrentHHMMSS() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date date1 = DateUtil.getSameDay(DateUtil.addDays1(date, 2));
        ZonedDateTime dateTime1 = getCurrentHHMMSS(dateTime.plusDays(2));

        log.info("date1={}, {}", date1, date1.getTime());
        log.info("dateTime1={},{}", dateTime1, dateTime1.toInstant().toEpochMilli());

        assertEquals(date1.toInstant(), dateTime1.toInstant());
    }

    @Test
    public void getDayStartByZone() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date date1 = DateUtil.getDayStartByZone(date, "GMT-8");
        ZonedDateTime dateTime1 = dateTime.withZoneSameInstant(ZoneId.of("GMT-8")).with(LocalTime.MIN).withZoneSameInstant(ZoneId.systemDefault());
        ZonedDateTime dateTime2 = DateTimeUtil.withZoneSameLocal(dateTime, "GMT-8").with(LocalTime.MIN);

        log.info("date1={}, {}", date1, date1.getTime());
        log.info("dateTime1={},{}", dateTime1, dateTime1.toInstant().toEpochMilli());
        log.info("dateTime2={},{}", dateTime2, dateTime2.toInstant().toEpochMilli());

        assertEquals(date1.toInstant(), dateTime1.toInstant());
    }

    @Test
    public void getStartOfMonth() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date date1 = DateUtil.getStartOfMonth(date, TimeZone.getTimeZone("GMT-8"));
        ZonedDateTime dateTime1 = dateTime.withZoneSameInstant(ZoneId.of("GMT-8")).with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN).withZoneSameInstant(ZoneId.systemDefault());
        ZonedDateTime dateTime2 = DateTimeUtil.withZoneSameLocal(dateTime, "GMT-8").with(LocalTime.MIN);

        log.info("date1={}, {}", date1, date1.getTime());
        log.info("dateTime1={},{}", dateTime1, dateTime1.toInstant().toEpochMilli());
        log.info("dateTime2={},{}", dateTime2, dateTime2.toInstant().toEpochMilli());

        assertEquals(date1.toInstant(), dateTime1.toInstant());
    }


    private ZonedDateTime getCurrentHHMMSS(ZonedDateTime dateTime) {
        String date = DateTimeUtil.formatWithPattern(ZonedDateTime.now(), "yyyy-MM-dd");
        String time = DateTimeUtil.formatWithPattern(dateTime, "HH:mm:ss");
        return DateTimeUtil.parseWithPattern(date + " " + time, "yyyy-MM-dd HH:mm:ss");
    }


    //

    @Test
    public void le() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        boolean le = DateUtil.le(date, DateUtil.addSeconds(date, 1));
        boolean le1 = date.compareTo(DateUtil.addSeconds(date, 1)) <= 0;
        boolean le2 = dateTime.compareTo(dateTime.plusSeconds(1)) <= 0;

        log.info("le={}", le);
        log.info("le1={}", le1);
        log.info("le2={}", le2);

        assertEquals(le1, le2);

        //date.getHours() >= 6 && date.getHours()
        log.info("le={}", date.getMinutes());
        log.info("le={}", dateTime.getHour());

    }

    @Test
    public void addMonthsFirstDay() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date date1 = DateUtil.addMonthsFirstDay(date, 13, TimeZone.getTimeZone("UTC-8"));
        ZonedDateTime dateTime1 = DateTimeUtil.addMonthsFirstDay(dateTime, 13, "UTC-8");

        Date date2 = Date.from(dateTime1.toInstant());

        log.info("date={},{}", date, date.getTime());
        log.info("date1={},{}", date1, date1.getTime());

        log.info("dateTime1={},{}", dateTime1, dateTime1.toInstant().toEpochMilli());
        log.info("date2={},{}", date2, date2.getTime());

        ZonedDateTime start = dateTime.with(LocalTime.MIN).minusDays(1);
        ZonedDateTime end = dateTime.with(LocalTime.MAX);

        long days = Duration.between(start, end).toDays();
        log.info("days= {}", days);

    }

    @Test
    public void getStartOfDayByZoneId() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date date1 = DateUtil.getStartOfDayByZoneId(date, "UTC-8");

        ZonedDateTime dateTime1 = dateTime.withZoneSameInstant(ZoneId.of("UTC-8")).with(LocalTime.MIN).withZoneSameInstant(ZoneId.systemDefault());

        log.info("date={},{}", date, date.getTime());
        log.info("date1={},{}", date1, date1.getTime());

        log.info("dateTime={},{}", dateTime, dateTime.toInstant().toEpochMilli());
        log.info("dateTime1={},{}", dateTime1, dateTime1.toInstant().toEpochMilli());
    }
    //DateUtil.isSameDay(currentDate, expUser.getGmtCreate())

    @Test
    public void isSameDay() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        boolean sameDay = DateUtil.isSameDay(date, DateUtil.addHours(date, 2));
        boolean sameDay1 = DateUtil.isSameDay(date, DateUtil.addHours(date, 24));
        log.info("sameDay={}", sameDay);
        log.info("sameDay1={}", sameDay1);

        ZonedDateTime zonedDateTime = dateTime.truncatedTo(ChronoUnit.DAYS);

        boolean equals = DateTimeUtil.isSameDay(dateTime.plusHours(2), dateTime);
        boolean equals2 = DateTimeUtil.isSameDay(dateTime.plusHours(20), dateTime);
        log.info("equals={}", equals);
        log.info("equals2={}", equals2);

        assertTrue(equals);
        assertFalse(equals2);


    }

    @Test
    public void formateDateStrForEn() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());
        String date1 = DateUtil.formateDateStrForEn(date, "MMMM dd, YYYY HH:mm:ss");

        String dateTime1 = DateTimeUtil.formatWithPattern(dateTime, "MMMM dd, YYYY HH:mm:ss");
        log.info("date={},{}", dateTime1, date1);
        assertEquals(date1, dateTime1);
    }

    @Test
    public void getStrByDateAndZone() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        String dateStr = DateUtil.getStrByDateAndZone(date, TimeZone.getTimeZone("UTC"));
        String dateTimeStr = DateTimeUtil.format(dateTime);
        log.info("dateStr={}", dateStr);
        log.info("dateTimeStr={}", dateTimeStr);
        assertEquals(dateStr, dateTimeStr);
    }

    @Test
    public void betweenDayUp() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        int dayUp = DateUtil.betweenDayUp(date, DateUtil.addHours(date, 2));

        long hours = Duration.between(dateTime, dateTime.plusHours(2)).toHoursPart();
        log.info("dateStr={}", hours);
        long dayUp1 = hours / 24;
        //int dayUp1 = (int) Math.ceil((double) hours / 24);

        log.info("dateStr={}", dayUp);
        log.info("dateTimeStr={}", dayUp1);
    }

    @Test
    public void toLocalDate() throws ParseException {
        String str = "2025-12-31 23:59:59";

        //Date date = new Date();
        Date date = DateUtil.parseDate(str, "yyyy-MM-dd HH:mm:ss");
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        //int monthValue = DateUtil.toLocalDate(date).getMonthValue();
        int dayOfMonth = DateUtil.toLocalDate(date).getDayOfMonth();

        Date date1 = DateUtil.addYearsByZoneId(date, 1, "UTC-8");

        //dateTime.withZoneSameInstant("UTC-8")
        log.info("date1={}", date);
        log.info("date1={}", dayOfMonth);
    }

    @Test
    public void getUserTodayStart() throws ParseException {
        String str = "2025-12-31 23:59:59";

        //Date date = new Date();
        Date date = DateUtil.parseDate(str, "yyyy-MM-dd HH:mm:ss");
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());


        Date date1 = DateUtil.getUserTodayStart("UTC+8");

        ZonedDateTime zonedDateTime = getUserTodayStart("UTC+8");

        //dateTime.withZoneSameInstant("UTC-8")
        log.info("date1={}, {}", date1, date1.toInstant().toEpochMilli());
        log.info("zonedDateTime={},{}", zonedDateTime, zonedDateTime.toInstant().toEpochMilli());


        Date date11 = DateUtil.getUserTodayEnd("UTC+8");

        ZonedDateTime zonedDateTime1 = getUserTodayEnd("UTC+8");
        log.info("date1={}, {}", date11, date11.toInstant().toEpochMilli());
        log.info("zonedDateTime={},{}", zonedDateTime1, zonedDateTime1.toInstant().toEpochMilli());

    }

    //DateUtil.convertZonedDateTime2Date(detail.getLastTime())
    private ZonedDateTime getUserTodayEnd(String userZoneId) {
        return ZonedDateTime.now().withZoneSameInstant(ZoneId.of(userZoneId)).with(LocalTime.MAX).withZoneSameInstant(ZoneOffset.UTC);
    }

    private ZonedDateTime getUserTodayStart(String userZoneId) {
        return ZonedDateTime.now().withZoneSameInstant(ZoneId.of(userZoneId)).with(LocalTime.MIN).withZoneSameInstant(ZoneOffset.UTC);
    }

    @Test
    public void convertZonedDateTime2Date() throws ParseException {
        String str = "2025-12-31 23:59:59";

        //Date date = new Date();
        Date date = DateUtil.parseDate(str, "yyyy-MM-dd HH:mm:ss");
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date date1 = DateUtil.convertZonedDateTime2Date(dateTime);


        log.info("date1={}", date1);
        log.info("date1={}", date);
    }

    @Test
    public void getMonthStartByZone() throws ParseException {
        String str = "2025-12-31 23:59:59";

        //Date date = new Date();
        Date date = DateUtil.parseDate(str, "yyyy-MM-dd HH:mm:ss");
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date date1 = DateUtil.convertZonedDateTime2Date(dateTime);
        Date start = DateUtil.getMonthStartByZone(2025, 12, "GMT-8");
        Date end = DateUtil.getMonthEndByZone(2025, 12, "GMT-8");

        ZonedDateTime startTime = YearMonth.of(2025, 12).atDay(1).atStartOfDay(ZoneId.of("GMT-8"));
        ZonedDateTime endTime = YearMonth.of(2025, 12).atEndOfMonth().atStartOfDay(ZoneId.of("GMT-8"));

        log.info("date1={}", start);
        log.info("date1={}", end);

        log.info("date1={}", startTime);
        log.info("date1={}", endTime);

        log.info("date1={}", startTime.withZoneSameInstant(ZoneId.systemDefault()));
        log.info("date1={}", endTime.withZoneSameInstant(ZoneId.systemDefault()));
    }


    @Test
    public void getYearStartByZone() throws ParseException {
        String str = "2025-12-31 23:59:59";

        //Date date = new Date();
        Date date = DateUtil.parseDate(str, "yyyy-MM-dd HH:mm:ss");
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date date1 = DateUtil.convertZonedDateTime2Date(dateTime);
        Date start = DateUtil.getYearStartByZone(2025, "GMT-8");
        Date end = DateUtil.getYearEndByZone(2025, "GMT-8");

        ZonedDateTime startTime = YearMonth.of(2025, 1).atDay(1).atStartOfDay(ZoneId.of("GMT-8"));
        //ZonedDateTime endTime = YearMonth.of(2025, 1).atDay(1).atStartOfDay(ZoneId.of("GMT-8"));
        ZonedDateTime endTime = startTime.plusYears(1).minusSeconds(1);

        log.info("start={},{}", start, start.toInstant().toEpochMilli());
        log.info("end={},{}", end, end.toInstant().toEpochMilli());

        log.info("date1={},{}", startTime, startTime.toInstant().toEpochMilli());
        log.info("date1={},{}", endTime, endTime.toInstant().toEpochMilli());

        log.info("date1={}", startTime.withZoneSameInstant(ZoneId.systemDefault()));
        log.info("date1={}", endTime.withZoneSameInstant(ZoneId.systemDefault()));
    }

    @Test
    public void getStartOfDayByZoneId2() throws ParseException {
        String str = "2025-12-31 23:59:59";

        //Date date = new Date();
        Date date = DateUtil.parseDate(str, "yyyy-MM-dd HH:mm:ss");
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date start = DateUtil.getStartOfDayByZoneId(date, "GMT-8");

        Date start1 = DateUtil.getDayStartByZone(date, "GMT-8");

        log.info("start={},{}", start, start.toInstant().toEpochMilli());

        log.info("start1={},{}", start1, start1.toInstant().toEpochMilli());
        assertEquals(start, start1);
    }

    @Test
    public void getStartOfWeekByZoneId() throws ParseException {
        String str = "2025-12-31 23:59:59";

        //Date date = new Date();
        Date date = DateUtil.parseDate(str, "yyyy-MM-dd HH:mm:ss");
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date start = DateUtil.getStartOfWeekByZoneId(date, "GMT-8");
        ZonedDateTime start1 = dateTime.withZoneSameInstant(ZoneId.of("GMT-8")).with(DayOfWeek.MONDAY).with(LocalTime.MIN);

        log.info("start={},{}", start, start.toInstant().toEpochMilli());
        log.info("start1={},{}", start1, start1.toInstant().toEpochMilli());
        assertEquals(start.toInstant(), start1.toInstant());
    }

    @Test
    public void getStartOfWeek() throws ParseException {
        String str = "2025-12-31 23:59:59";

        //Date date = new Date();
        Date date = DateUtil.parseDate(str, "yyyy-MM-dd HH:mm:ss");
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date start = DateUtil.getStartOfWeek(date.getTime(), ZoneId.of("GMT-8"));
        ZonedDateTime start1 = dateTime.withZoneSameInstant(ZoneId.of("GMT-8")).with(DayOfWeek.MONDAY).with(LocalTime.MIN);

        log.info("start={},{}", start, start.toInstant().toEpochMilli());
        log.info("start1={},{}", start1, start1.toInstant().toEpochMilli());
        assertEquals(start.toInstant(), start1.toInstant());
    }

    @Test
    public void getEndOfWeek() throws ParseException {
        String str = "2025-12-31 23:59:59";

        //Date date = new Date();
        Date date = DateUtil.parseDate(str, "yyyy-MM-dd HH:mm:ss");
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date start = DateUtil.getEndOfWeek(date.getTime(), ZoneId.of("GMT-8"));
        ZonedDateTime start1 = dateTime.withZoneSameInstant(ZoneId.of("GMT-8")).with(DayOfWeek.SUNDAY).with(LocalTime.MAX);

        log.info("start={},{}", start, start.toInstant().toEpochMilli());
        log.info("start1={},{}", start1, start1.toInstant().toEpochMilli());
        assertEquals(start.toInstant().toEpochMilli(), start1.toInstant().toEpochMilli());
    }


    //
    @Test
    public void addMonthsByZoneId() throws ParseException {
        String str = "2025-12-31 23:59:59";

        //Date date = new Date();
        Date date = DateUtil.parseDate(str, "yyyy-MM-dd HH:mm:ss");
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date start = DateUtil.addMonthsByZoneId(date, 1, "GMT-8");
        ZonedDateTime start1 = dateTime.withZoneSameInstant(ZoneId.of("GMT-8")).with(TemporalAdjusters.firstDayOfMonth()).plusMonths(1).with(LocalTime.MIN).withZoneSameInstant(ZoneId.systemDefault());

        Date start2 = DateUtil.getStartOfMonth(DateUtil.addMonthsByZoneId(date, 1, "GMT-8"), TimeZone.getTimeZone("GMT-8"));

        log.info("start={},{}", start, start.toInstant().toEpochMilli());
        log.info("start1={},{}", start1, start1.toInstant().toEpochMilli());

        log.info("start2={},{}", start2, start2.toInstant().toEpochMilli());
        assertEquals(start.toInstant(), start1.toInstant());
    }

    @Test
    public void addYearsByZoneId() throws ParseException {
        String str = "2025-12-31 23:59:59";

        //Date date = new Date();
        Date date = DateUtil.parseDate(str, "yyyy-MM-dd HH:mm:ss");
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date start = DateUtil.addYearsByZoneId(date, 1, "UTC-8");
        ZonedDateTime start1 = dateTime.withZoneSameInstant(ZoneId.of("GMT-8")).with(TemporalAdjusters.firstDayOfYear()).plusYears(1).with(LocalTime.MIN).withZoneSameInstant(ZoneId.systemDefault());


        log.info("start={},{}", start, start.toInstant().toEpochMilli());
        log.info("start1={},{}", start1, start1.toInstant().toEpochMilli());

        assertEquals(start.toInstant(), start1.toInstant());
    }


    @Test
    public void convertTimeZone() throws ParseException {
        //String str = "2009-02-14 07:31:30";
        //
        //Date date = DateUtil.parseDate(str, "yyyy-MM-dd HH:mm:ss");
        //ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date date = new Date(1234567890000L);
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date date1 = DateUtil.convertTimeZone(date, "Asia/Shanghai", "UTC");
        ZonedDateTime dateTime1 = dateTime;

        Date start2 = DateUtil.convertTimeZone(date, "UTC", "Asia/Shanghai");
        ZonedDateTime dateTime2 = dateTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
        //ZonedDateTime dateTime2 = dateTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai")).withZoneSameInstant(ZoneId.systemDefault());


        log.info("date1={},{}", date1, date1.toInstant().toEpochMilli());
        log.info("dateTime1={},{}", dateTime1, dateTime1.toInstant().toEpochMilli());


        log.info("start2={},{}", start2, start2.toInstant().toEpochMilli());
        log.info("dateTime2={},{}", dateTime2, dateTime2.toInstant().toEpochMilli());

        assertEquals(date1.toInstant(), dateTime1.toInstant());
        assertEquals(start2.toInstant(), dateTime2.toInstant());
    }


    @Test
    public void testFormat() {
        Date date = new Date(1234567890000L);
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        ZonedDateTime zonedDateTime = dateTime.withZoneSameInstant(ZoneId.of("GMT+8"));
        String dateString = DateTimeUtil.formatWithPattern(zonedDateTime, "MMMM d'st', yyyy");

        String dateString2 = DateTimeUtil.format(dateTime, "GMT+8", "MMMM d'st', yyyy");

        Date timeZoneDate = DateUtil.getTimeZoneDate(date, TimeZone.getTimeZone("GMT+8"));
        SimpleDateFormat outputFormat = new SimpleDateFormat("MMMM d'st', yyyy");
        String dateString3 = outputFormat.format(timeZoneDate);


        log.info("start2={}", dateString);
        log.info("start2={}", dateString2);
        log.info("start2={}", dateString3);
    }

    //

    @Test
    public void getWeekEndDay() {
        Date date = new Date(1234567890000L);
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        List<DateStartEnd> weekEndDay = DateUtil.getWeekEndDay(DateUtil.getFirstOfMonth(date), DateUtil.getEndOfMonth(date));
        List<DateTimeStartEnd> weekEndDay1 = DateTimeUtil.getWeekEndDay(DateTimeUtil.firstDayOfMonth(dateTime), DateTimeUtil.lastDayOfMonth(dateTime));

        log.info("start2={}", weekEndDay);
        log.info("start2={}", weekEndDay1);
    }

    @Test
    public void getWorkDay() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        List<DateStartEnd> weekEndDay = DateUtil.getWorkDay(DateUtil.getFirstOfMonth(date), DateUtil.getEndOfMonth(date));
        List<DateTimeStartEnd> weekEndDay1 = DateTimeUtil.getWorkDay(DateTimeUtil.firstDayOfMonth(dateTime), DateTimeUtil.lastDayOfMonth(dateTime));

        log.info("start2={}", weekEndDay);
        log.info("start2={}", weekEndDay1);
    }

    @Test
    public void getUtcDate() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        Date utcDate = DateUtil.getUtcDate("2022-05-27T15:36:00Z");
        ZonedDateTime zonedDateTime = DateTimeUtil.parseWithPattern("2022-05-27T15:36:00Z", "yyyy-MM-dd'T'HH:mm:ss'Z'");
        log.info("start2={}", utcDate);
        log.info("start2={}", zonedDateTime);

        assertEquals(utcDate.toInstant(), zonedDateTime.toInstant());
    }

    @Test
    public void parse() {
        Date date = new Date();
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        LocalDate localDate = LocalDate.parse("27/05/2022", formatter);
        ZonedDateTime zonedDateTime = localDate.atStartOfDay(ZoneId.systemDefault());

        log.info("start2={}", zonedDateTime);

    }

    @Test
    public void getDateByTimeZone() {
        Date date = new Date(1234567890000L);
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        String now = DateUtil.getDateByTimeZone(date, TimeZone.getTimeZone("Asia/Shanghai"));
        String now1 = DateTimeUtil.format(dateTime, "Asia/Shanghai", "yyyy-MM-dd HH:mm:ss");
        //String now1 = DateTimeUtil.formatWithPattern(dateTime,"yyyy-MM-dd HH:mm:ss");

        log.info("dateTime={}", dateTime);
        log.info("start2={}", now);
        log.info("start2={}", now1);

        assertEquals(now,now1);
    }
//

    @Test
    public void testFormat1() {
        Date date = new Date(1234567890000L);
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());

        String format = DateTimeUtil.format(dateTime, "yyyy-MM-dd HH:mm:ss", "GMT+8");

        ZonedDateTime zonedDateTime = dateTime.withZoneSameInstant(ZoneId.of("GMT+8"));

        log.info("start2={}", format);
        log.info("dateTime={}", zonedDateTime);
    }


    @Test
    public void testGetWest8PairTime() {
        West8StartEndDto west8StartEndTime = NumberUtils.getWest8StartEndTime();
        West8PairDto west8PairTime = getWest8PairTime();

        log.info("west8StartEndTime={},{}", west8StartEndTime.getStartTime(), west8StartEndTime.getStartTime().toInstant().toEpochMilli());
        log.info("west8StartEndTime={},{}", west8StartEndTime.getEndTime(), west8StartEndTime.getEndTime().toInstant().toEpochMilli());

        log.info("west8PairTime={},{}", west8PairTime.getStartTime(), west8PairTime.getStartTime().toInstant().toEpochMilli());
        log.info("west8PairTime={},{}", west8PairTime.getEndTime(), west8PairTime.getEndTime().toInstant().toEpochMilli());

        assertEquals(west8StartEndTime.getStartTime().toInstant(), west8PairTime.getStartTime().toInstant());
        assertEquals(west8StartEndTime.getEndTime().toInstant(), west8PairTime.getEndTime().toInstant());
    }

    public static West8PairDto getWest8PairTime() {
        String a = DateUtil.formateDateStr(new Date(), DateUtil.YYYY_MM_DD);
        Date current16 = DateUtil.parseStr2Date(a + " 08:00:00", DateUtil.YYYY_MM_DD_HH_MM_SS);
        West8PairDto west8StartEndDto = null;
        // 如果当前时间大于16点/NumberUtils
        if (new Date().getTime() > current16.getTime()) {
            String b = DateUtil.formateDateStr(DateUtil.addDays(new Date(), 1), DateUtil.YYYY_MM_DD);
            Date tomrrow = DateUtil.parseStr2Date(b + " 08:00:00", DateUtil.YYYY_MM_DD_HH_MM_SS);
            west8StartEndDto = new West8PairDto(current16, tomrrow);
        } else {
            String b = DateUtil.formateDateStr(DateUtil.addDays(new Date(), -1), DateUtil.YYYY_MM_DD);
            Date yesterday = DateUtil.parseStr2Date(b + " 08:00:00", DateUtil.YYYY_MM_DD_HH_MM_SS);
            west8StartEndDto = new West8PairDto(yesterday, current16);
        }
        return west8StartEndDto;
    }

    @Data
    @NoArgsConstructor
    public static class West8PairDto {

        private Date startTime;
        private Date endTime;

        public West8PairDto(Date startTime, Date endTime) {
            this.startTime = startTime;
            this.endTime = endTime;
        }
    }

}

