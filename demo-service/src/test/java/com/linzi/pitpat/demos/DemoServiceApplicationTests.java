package com.linzi.pitpat.demos;

import com.linzi.pitpat.core.util.DingTalkUtils;
import com.linzi.pitpat.core.util.dto.DingTalkRequestDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.StopWatch;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.IntStream;

@Slf4j
@SpringBootTest
class DemoServiceApplicationTests {
    public static final DateTimeFormatter pattern = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Test
    void contextLoads() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        IntStream.range(0, 50000).forEach(i -> {
            ZonedDateTime time = ZonedDateTime.now();
            String format = pattern.format(time);
            log.info("<UNK>{}", format);
        });
        stopWatch.stop();
        log.info("<UNK>{}", stopWatch.getTotalTimeMillis());
    }

    @Test
    void sendDingTalkmsg() {
        //https://oapi.dingtalk.com/robot/send?access_token=****************************************************************
        String secret = "SECb88df1d4f73b0b50f30f3e7d97155c3ea96c4d79545f6b7937d843d158ee7d7f";
        String accessKey = "****************************************************************";
        //DingTalkUtils.sendDingDingGroupMsgNoAt(accessKey, secret, "普通消息");
        //DingTalkUtils.sendMsg(DingTalkRequestDto.of(accessKey, secret, "普通消息V2"));

        //DingTalkUtils.sendDingDingGroupMsg(accessKey, secret, "普通消息", "18658833279");
        //DingTalkUtils.sendMsg(DingTalkRequestDto.of(accessKey, secret, "普通消息V3", "18658833279"));
        //DingTalkUtils.sendMsg(DingTalkRequestDto.of(accessKey, secret, "普通消息V3", ""));
        //DingTalkUtils.sendMsg(DingTalkRequestDto.of(accessKey, secret, "普通消息V3"));

        //DingTalkUtils.sendDingDingGroupMsgHasAt(accessKey, secret, "国际版的就是靠国内版的喂数据", "18658833279", "prod");

        //DingTalkUtils.sendMsg(DingTalkRequestDto.of(accessKey, secret, "国际版的就是靠国内版的喂数据 V3","18658833279"));
        DingTalkUtils.sendMsg(DingTalkRequestDto.ofMarkdown(accessKey, secret, "国际版的就是靠国内版的喂数据 V3","18658833279"));
        DingTalkUtils.sendMsgOnline(DingTalkRequestDto.ofMarkdown(accessKey, secret, "国际版的就是靠国内版的喂数据 V3","18658833279,18404904315"), "prod");
    }


}
