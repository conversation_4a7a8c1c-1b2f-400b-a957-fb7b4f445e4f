## 版本发布流程

1. 为了避免版本发布后无法方便的对比版本内容进行比较，做出一下流程规范
   1. 开发功能，自测验证没有问题
   2. 更改 maven 版本号，推送到 maven 私服
   3. 更改 git 版本号，与 maven 版本号保持一致，推送到 gitlab

## amqp-starter 

1. TODO 

由于所有消息未全量迁移到 基于配置的 队列、交换机声明，导致无法全量启用 nameGenerator 生成基于配置的 queueName, exchangeName, 
具体遗留的代码为（均加了 TODO）,这两个地方主要是基于数据库的长期发送方案， 后续等 msg center 配置迁移过来，就可以启用了，

这会导致一个问题，如果在 demo-service 中测试，需要启用 DelayMessageTransferListener 和 DelayMessageTask

而在pitpat 里面需要暂时关闭。 这点需要注意，否则会导致无法正常基于死信队列的消息转发。

      - com.linzi.pitpat.consumer.msgtransfer.DelayMessageTransferListener#handleMessage
      - com.linzi.pitpat.framework.rabbitmq.task.DelayMessageTask.sendMessageToRabbitMQ
